#!/usr/bin/env python3
"""
Скрипт для запуска Telegram Payment Bot в продакшене
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_environment():
    """Проверяет окружение перед запуском"""
    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'LAVA_API_KEY', 
        'LAVA_SHOP_ID',
        'SECRET_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Отсутствуют переменные окружения: {', '.join(missing_vars)}")
        return False
    
    print("✅ Все необходимые переменные окружения установлены")
    return True

def check_database():
    """Проверяет наличие и инициализацию базы данных"""
    db_path = Path("payments.db")
    if not db_path.exists():
        print("📊 Инициализация базы данных...")
        try:
            from app.models.database import init_db
            init_db()
            print("✅ База данных инициализирована")
        except Exception as e:
            print(f"❌ Ошибка инициализации БД: {e}")
            return False
    else:
        print("✅ База данных найдена")
    
    return True

def start_gunicorn():
    """Запускает приложение через Gunicorn"""
    print("🚀 Запуск приложения через Gunicorn...")
    
    try:
        # Проверяем наличие gunicorn.conf.py
        if not Path("gunicorn.conf.py").exists():
            print("⚠️  gunicorn.conf.py не найден, используем стандартные настройки")
            cmd = ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "app:app"]
        else:
            cmd = ["gunicorn", "--config", "gunicorn.conf.py", "app:app"]
        
        print(f"Команда запуска: {' '.join(cmd)}")
        subprocess.run(cmd, check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Ошибка запуска Gunicorn: {e}")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Получен сигнал остановки")
        return True
    
    return True

def main():
    print("=== Запуск Telegram Payment Bot в продакшене ===")
    print(f"Рабочая директория: {os.getcwd()}")
    print(f"Python версия: {sys.version}")
    print()
    
    # Проверка окружения
    if not check_environment():
        sys.exit(1)
    
    # Проверка базы данных
    if not check_database():
        sys.exit(1)
    
    # Запуск приложения
    if not start_gunicorn():
        sys.exit(1)
    
    print("✅ Приложение успешно завершено")

if __name__ == "__main__":
    main()