#!/bin/bash

# Update system
sudo apt update
sudo apt upgrade -y

# Install required packages
sudo apt install -y python3 python3-pip python3-venv nginx git curl

# Create application directory
mkdir -p /home/<USER>/telegram_bot
cd /home/<USER>/telegram_bot

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install flask gunicorn requests python-telegram-bot sqlite3 python-dotenv

# Create systemd service
sudo tee /etc/systemd/system/telegram-bot.service > /dev/null <<EOF
[Unit]
Description=Telegram Payment Bot
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/telegram_bot
Environment=PATH=/home/<USER>/telegram_bot/venv/bin
ExecStart=/home/<USER>/telegram_bot/venv/bin/python app.py
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable telegram-bot
sudo systemctl start nginx

echo "Environment setup completed!"