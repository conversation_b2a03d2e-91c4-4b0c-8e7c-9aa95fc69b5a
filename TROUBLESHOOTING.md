# Troubleshooting Guide - Telegram Payment Bot

## 🚨 Критические проблемы

### ❌ Админ-панель: Не удается войти (форма не отправляется)

**Симптомы:**
- Форма входа загружается нормально
- При вводе правильного пароля (`admin123`) ничего не происходит
- Страница остается на `/admin/login` без сообщений об ошибке
- В логах нет ошибок
- Форма как будто "не отправляется"

**Причина:**
Настройка `SESSION_COOKIE_SECURE = true` в `config.py` заставляет Flask устанавливать cookies только для HTTPS соединений. При использовании HTTP cookies не сохраняются, что приводит к невозможности аутентификации.

**Диагностика:**
```bash
# Проверьте текущую настройку
grep "SESSION_COOKIE_SECURE" /home/<USER>/app/config.py

# Тестовый запрос (должен показать Set-Cookie с Secure)
curl -I http://localhost:5000/admin/login

# Тест аутентификации
curl -c cookies.txt http://localhost:5000/admin/login | grep csrf_token
# Если CSRF токен найден, попробуйте войти:
curl -b cookies.txt -X POST -d "password=admin123&csrf_token=ТОКЕН" http://localhost:5000/admin/login
```

**Решение:**

**Вариант 1: Для HTTP (разработка/тестирование)**
```bash
# Измените настройку в config.py
sudo sed -i "s/SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'true')/SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'false')/" /home/<USER>/app/config.py

# Перезапустите приложение
sudo pkill -f gunicorn
sudo bash -c "cd /home/<USER>/app && nohup sudo -u telegrambot /home/<USER>/app/venv/bin/gunicorn --bind 0.0.0.0:5000 --workers 2 --pid /tmp/gunicorn.pid wsgi:app > /var/log/telegram-payment-bot/gunicorn.log 2>&1 &"
```

**Вариант 2: Через переменную окружения**
```bash
# Добавьте в .env файл
echo "SESSION_COOKIE_SECURE=false" >> /home/<USER>/app/.env

# Перезапустите приложение
sudo systemctl restart telegram-payment-bot
```

**Вариант 3: Настройка HTTPS (production)**
```bash
# Установите SSL сертификат
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com

# Обновите конфигурацию nginx для HTTPS
# Оставьте SESSION_COOKIE_SECURE=true для безопасности
```

**Проверка решения:**
```bash
# Проверьте изменения
grep "SESSION_COOKIE_SECURE" /home/<USER>/app/config.py

# Тест входа (должен вернуть редирект на dashboard)
curl -c cookies.txt http://localhost:5000/admin/login | grep csrf_token
curl -b cookies.txt -X POST -d "password=admin123&csrf_token=ТОКЕН" http://localhost:5000/admin/login
```

## 🔧 Другие проблемы с админ-панелью

### CSRF токен не работает

**Симптомы:**
- Ошибка "CSRF token missing" или "CSRF token invalid"

**Решение:**
```bash
# Проверьте SECRET_KEY
grep "SECRET_KEY" /home/<USER>/app/.env

# Убедитесь, что форма содержит CSRF токен
curl http://localhost:5000/admin/login | grep csrf_token
```

### Ошибки импорта модулей

**Симптомы:**
- ImportError при запуске приложения

**Решение:**
```bash
# Переустановите зависимости
cd /home/<USER>/app
sudo -u telegrambot /home/<USER>/app/venv/bin/pip install -r requirements.txt

# Проверьте PYTHONPATH
sudo -u telegrambot /home/<USER>/app/venv/bin/python -c "import sys; print(sys.path)"
```

## 🤖 Проблемы с Telegram ботом

### Бот не отвечает на сообщения

**Диагностика:**
```bash
# Проверьте webhook
curl "https://api.telegram.org/bot<YOUR_TOKEN>/getWebhookInfo"

# Проверьте логи
sudo journalctl -u telegram-payment-bot -f
```

**Решение:**
```bash
# Переустановите webhook
curl -X POST "https://api.telegram.org/bot<YOUR_TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://yourdomain.com/webhook"}'
```

### Ошибки при обработке платежей

**Диагностика:**
```bash
# Проверьте настройки Lava.top
grep -E "LAVA_API_KEY|LAVA_SECRET_KEY" /home/<USER>/app/.env

# Тест webhook
curl -X POST http://localhost:5000/webhook/lava \
     -H "Content-Type: application/json" \
     -d '{"test": "data"}'
```

## 🗄️ Проблемы с базой данных

### База данных недоступна

**Диагностика:**
```bash
# Проверьте файл БД
ls -la /home/<USER>/app/payments.db

# Проверьте права доступа
sudo -u telegrambot sqlite3 /home/<USER>/app/payments.db ".tables"
```

**Решение:**
```bash
# Создайте БД заново
cd /home/<USER>/app
sudo -u telegrambot /home/<USER>/app/venv/bin/python -c "
from app.database import init_db
init_db()
"
```

## 🌐 Проблемы с веб-сервером

### Nginx не запускается

**Диагностика:**
```bash
# Проверьте конфигурацию
sudo nginx -t

# Проверьте логи
sudo tail -f /var/log/nginx/error.log
```

### Приложение недоступно извне

**Диагностика:**
```bash
# Проверьте порты
sudo netstat -tlnp | grep -E ':80|:5000'

# Проверьте файрвол
sudo ufw status
```

**Решение:**
```bash
# Откройте порты
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 5000
```

## 📊 Команды для диагностики

### Проверка статуса системы
```bash
# Статус сервисов
sudo systemctl status telegram-payment-bot
sudo systemctl status nginx

# Проверка процессов
ps aux | grep -E "python|gunicorn|nginx"

# Проверка портов
sudo netstat -tlnp | grep -E ':80|:443|:5000'
```

### Просмотр логов
```bash
# Логи приложения
sudo journalctl -u telegram-payment-bot -f

# Логи Nginx
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# Логи Gunicorn (если используется)
sudo tail -f /var/log/telegram-payment-bot/gunicorn.log
```

### Тестирование endpoints
```bash
# Health check
curl http://localhost:5000/health

# Админ-панель
curl -I http://localhost:5000/admin/login

# Webhook
curl -X POST http://localhost:5000/webhook/test

# Метрики
curl http://localhost:5000/metrics
```

## 🆘 Экстренное восстановление

### Полный перезапуск системы
```bash
# Остановите все сервисы
sudo systemctl stop telegram-payment-bot
sudo systemctl stop nginx

# Очистите процессы
sudo pkill -f gunicorn
sudo pkill -f python

# Перезапустите
sudo systemctl start nginx
sudo systemctl start telegram-payment-bot
```

### Откат к рабочей версии
```bash
# Восстановите из резервной копии
sudo cp /home/<USER>/backups/latest.tar.gz /tmp/
cd /home/<USER>/app
sudo tar -xzf /tmp/latest.tar.gz

# Перезапустите
sudo systemctl restart telegram-payment-bot
```

## 📞 Получение помощи

Если проблема не решается:

1. **Соберите диагностическую информацию:**
   ```bash
   # Создайте отчет о системе
   sudo systemctl status telegram-payment-bot > debug_report.txt
   sudo journalctl -u telegram-payment-bot -n 100 >> debug_report.txt
   curl -I http://localhost:5000/health >> debug_report.txt
   ```

2. **Проверьте документацию:**
   - [DEPLOYMENT_INSTRUCTIONS.md](DEPLOYMENT_INSTRUCTIONS.md)
   - [README.md](README.md)

3. **Обратитесь за поддержкой** с приложенным отчетом о системе.
