"""
Тесты для системы логирования административных действий
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from decimal import Decimal

from app.models.database import DatabaseManager, DatabaseService
from app.models.models import User, AdminLog


class TestAdminLogging:
    """Тесты системы логирования административных действий"""
    
    @pytest.fixture
    def db_service(self):
        """Создание временной базы данных для тестов"""
        # Создаем временный файл для базы данных
        db_fd, db_path = tempfile.mkstemp()
        os.close(db_fd)
        
        try:
            # Инициализируем базу данных
            db_manager = DatabaseManager(db_path)
            db_manager.init_database()
            
            db_service = DatabaseService(db_manager)
            yield db_service
        finally:
            # Удаляем временный файл
            if os.path.exists(db_path):
                os.unlink(db_path)
    
    def test_log_admin_action(self, db_service):
        """Тест логирования административного действия"""
        # Создаем пользователя
        user = db_service.create_user(
            telegram_id=12345,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        # Логируем действие
        result = db_service.log_admin_action(
            admin_telegram_id=0,
            action="test_action",
            target_user_id=user.id,
            details="Test action details"
        )
        
        assert result is True
        
        # Проверяем, что лог создался
        logs = db_service.get_admin_logs(limit=1)
        assert len(logs) == 1
        
        log = logs[0]
        assert log.admin_telegram_id == 0
        assert log.action == "test_action"
        assert log.target_user_id == user.id
        assert log.details == "Test action details"
        assert log.created_at is not None
    
    def test_log_admin_action_without_target(self, db_service):
        """Тест логирования действия без целевого пользователя"""
        result = db_service.log_admin_action(
            admin_telegram_id=123456,
            action="view_dashboard",
            details="Admin viewed dashboard"
        )
        
        assert result is True
        
        logs = db_service.get_admin_logs(limit=1)
        assert len(logs) == 1
        
        log = logs[0]
        assert log.admin_telegram_id == 123456
        assert log.action == "view_dashboard"
        assert log.target_user_id is None
        assert log.details == "Admin viewed dashboard"
    
    def test_get_admin_logs_with_pagination(self, db_service):
        """Тест получения логов с пагинацией"""
        # Создаем несколько логов
        for i in range(10):
            db_service.log_admin_action(
                admin_telegram_id=0,
                action=f"test_action_{i}",
                details=f"Test action {i}"
            )
        
        # Получаем первые 5 логов
        logs_page1 = db_service.get_admin_logs(limit=5, offset=0)
        assert len(logs_page1) == 5
        
        # Получаем следующие 5 логов
        logs_page2 = db_service.get_admin_logs(limit=5, offset=5)
        assert len(logs_page2) == 5
        
        # Проверяем, что логи разные
        page1_actions = [log.action for log in logs_page1]
        page2_actions = [log.action for log in logs_page2]
        assert set(page1_actions).isdisjoint(set(page2_actions))
    
    def test_get_admin_logs_with_user_info(self, db_service):
        """Тест получения логов с информацией о пользователях"""
        # Создаем пользователя
        user = db_service.create_user(
            telegram_id=12345,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        # Логируем действие с этим пользователем
        db_service.log_admin_action(
            admin_telegram_id=0,
            action="grant_subscription",
            target_user_id=user.id,
            details="Granted 1 month subscription"
        )
        
        # Получаем логи
        logs = db_service.get_admin_logs(limit=1)
        assert len(logs) == 1
        
        log = logs[0]
        assert log.target_user_id == user.id
        # Проверяем, что добавлена информация о пользователе
        assert hasattr(log, 'target_user_name')
        assert "Test User" in log.target_user_name
    
    def test_admin_grant_subscription_logging(self, db_service):
        """Тест логирования при выдаче подписки администратором"""
        # Создаем пользователя
        user = db_service.create_user(
            telegram_id=12345,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        # Выдаем подписку
        result = db_service.admin_grant_subscription(user.id, 3)
        assert result is True
        
        # Логируем действие (это должно делаться в веб-интерфейсе)
        db_service.log_admin_action(
            admin_telegram_id=0,
            action="grant_subscription",
            target_user_id=user.id,
            details="Выдана подписка на 3 месяца"
        )
        
        # Проверяем лог
        logs = db_service.get_admin_logs(limit=1)
        assert len(logs) == 1
        
        log = logs[0]
        assert log.action == "grant_subscription"
        assert log.target_user_id == user.id
        assert "3 месяца" in log.details
    
    def test_admin_revoke_subscription_logging(self, db_service):
        """Тест логирования при отзыве подписки администратором"""
        # Создаем пользователя и выдаем подписку
        user = db_service.create_user(
            telegram_id=12345,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        db_service.admin_grant_subscription(user.id, 1)
        
        # Отзываем подписку
        result = db_service.admin_revoke_subscription(user.id)
        assert result is True
        
        # Логируем действие
        db_service.log_admin_action(
            admin_telegram_id=0,
            action="revoke_subscription",
            target_user_id=user.id,
            details="Подписка отозвана администратором"
        )
        
        # Проверяем лог
        logs = db_service.get_admin_logs(limit=1)
        assert len(logs) == 1
        
        log = logs[0]
        assert log.action == "revoke_subscription"
        assert log.target_user_id == user.id
        assert "отозвана" in log.details
    
    def test_bulk_operations_logging(self, db_service):
        """Тест логирования массовых операций"""
        # Создаем несколько пользователей
        users = []
        for i in range(3):
            user = db_service.create_user(
                telegram_id=12345 + i,
                username=f"testuser{i}",
                first_name=f"Test{i}",
                last_name="User"
            )
            users.append(user)
        
        # Выполняем массовую операцию и логируем каждое действие
        for user in users:
            db_service.admin_grant_subscription(user.id, 1)
            db_service.log_admin_action(
                admin_telegram_id=0,
                action="bulk_grant_subscription",
                target_user_id=user.id,
                details="Массовая выдача подписки на 1 месяц"
            )
        
        # Проверяем логи
        logs = db_service.get_admin_logs(limit=10)
        bulk_logs = [log for log in logs if log.action == "bulk_grant_subscription"]
        assert len(bulk_logs) == 3
        
        # Проверяем, что все пользователи залогированы
        logged_user_ids = [log.target_user_id for log in bulk_logs]
        expected_user_ids = [user.id for user in users]
        assert set(logged_user_ids) == set(expected_user_ids)
    
    def test_view_actions_logging(self, db_service):
        """Тест логирования действий просмотра"""
        # Логируем различные действия просмотра
        view_actions = [
            ("view_users", "Просмотр пользователей: страница 1"),
            ("view_subscriptions", "Просмотр подписок: найдено 5 активных"),
            ("view_payments", "Просмотр платежей: статус pending"),
            ("view_dashboard", "Просмотр дашборда")
        ]
        
        for action, details in view_actions:
            db_service.log_admin_action(
                admin_telegram_id=0,
                action=action,
                details=details
            )
        
        # Проверяем логи
        logs = db_service.get_admin_logs(limit=10)
        view_logs = [log for log in logs if log.action.startswith("view_")]
        assert len(view_logs) == 4
        
        # Проверяем, что все действия залогированы
        logged_actions = [log.action for log in view_logs]
        expected_actions = [action for action, _ in view_actions]
        assert set(logged_actions) == set(expected_actions)
    
    def test_payment_processing_logging(self, db_service):
        """Тест логирования обработки платежей"""
        # Создаем пользователя
        user = db_service.create_user(
            telegram_id=12345,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        # Логируем ручную обработку платежа
        db_service.log_admin_action(
            admin_telegram_id=0,
            action="manual_payment_processing",
            target_user_id=user.id,
            details="Ручная обработка платежа 123: complete"
        )
        
        # Проверяем лог
        logs = db_service.get_admin_logs(limit=1)
        assert len(logs) == 1
        
        log = logs[0]
        assert log.action == "manual_payment_processing"
        assert log.target_user_id == user.id
        assert "платежа 123" in log.details
        assert "complete" in log.details
    
    def test_admin_log_model_validation(self):
        """Тест валидации модели AdminLog"""
        # Корректный лог
        log = AdminLog(
            admin_telegram_id=12345,
            action="test_action",
            target_user_id=67890,
            details="Test details",
            created_at=datetime.now()
        )
        assert log.validate() is True
        
        # Некорректный admin_telegram_id
        with pytest.raises(Exception):
            AdminLog(
                admin_telegram_id=0,  # Должен быть положительным
                action="test_action"
            )
        
        # Пустое действие
        with pytest.raises(Exception):
            AdminLog(
                admin_telegram_id=12345,
                action=""  # Не может быть пустым
            )
        
        # Слишком длинное действие
        with pytest.raises(Exception):
            AdminLog(
                admin_telegram_id=12345,
                action="x" * 256  # Слишком длинное
            )
    
    def test_logs_cleanup(self, db_service):
        """Тест очистки старых логов"""
        # Создаем старые логи (имитируем старую дату)
        old_date = datetime.now() - timedelta(days=100)
        
        # Создаем несколько логов
        for i in range(5):
            db_service.log_admin_action(
                admin_telegram_id=0,
                action=f"old_action_{i}",
                details=f"Old action {i}"
            )
        
        # Получаем логи старше 90 дней (должны быть пустыми, так как мы только что создали)
        old_logs = db_service.get_admin_logs_older_than_days(90)
        assert len(old_logs) == 0
        
        # Получаем все логи
        all_logs = db_service.get_admin_logs(limit=10)
        assert len(all_logs) == 5


if __name__ == '__main__':
    pytest.main([__file__, '-v'])