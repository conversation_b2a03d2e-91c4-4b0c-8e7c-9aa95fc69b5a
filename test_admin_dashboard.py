#!/usr/bin/env python3
"""
Тест для проверки дашборда админ-панели
"""

import requests
import sys

def test_admin_dashboard():
    """Тестирует доступ к дашборду админ-панели"""
    base_url = "https://195.49.212.172"
    
    # Создаем сессию
    session = requests.Session()
    session.verify = False  # Отключаем проверку SSL для тестирования
    
    try:
        # Сначала входим в систему
        print("Выполняем вход в систему...")
        
        # Получаем страницу входа
        login_page = session.get(f"{base_url}/admin/login")
        
        # Ищем CSRF токен
        csrf_token = None
        if 'csrf_token' in login_page.text:
            import re
            csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
            if csrf_match:
                csrf_token = csrf_match.group(1)
        
        # Данные для входа
        login_data = {
            'password': 'admin123',
            'submit': 'Войти'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        # Входим в систему
        login_response = session.post(
            f"{base_url}/admin/login",
            data=login_data,
            allow_redirects=True
        )
        
        print(f"Статус после входа: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"Ошибка входа: {login_response.text[:500]}")
            return False
        
        # Теперь пробуем получить дашборд
        print("Получаем дашборд...")
        dashboard_response = session.get(f"{base_url}/admin/dashboard")
        
        print(f"Статус дашборда: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("Дашборд загружен успешно!")
            # Проверяем, что в ответе есть ожидаемый контент
            if "Админ панель" in dashboard_response.text or "dashboard" in dashboard_response.text:
                print("Контент дашборда корректный")
                return True
            else:
                print("Контент дашборда некорректный")
                print(dashboard_response.text[:200])
                return False
        else:
            print(f"Ошибка загрузки дашборда:")
            print(dashboard_response.text[:500])
            return False
            
    except Exception as e:
        print(f"Ошибка при тестировании дашборда: {e}")
        return False

if __name__ == "__main__":
    success = test_admin_dashboard()
    sys.exit(0 if success else 1)