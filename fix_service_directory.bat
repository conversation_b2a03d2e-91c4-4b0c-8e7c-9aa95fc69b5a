@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Fixing systemd service WorkingDirectory...
echo.
echo Current service file:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S cat /etc/systemd/system/telegram-payment-bot.service"
echo.
echo Updating WorkingDirectory to /opt/telegram-payment-bot/app...
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S sed -i 's|WorkingDirectory=/home/<USER>/app|WorkingDirectory=/opt/telegram-payment-bot/app|g' /etc/systemd/system/telegram-payment-bot.service"
echo.
echo Updated service file:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S cat /etc/systemd/system/telegram-payment-bot.service"
echo.
echo Reloading systemd and restarting service...
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S systemctl daemon-reload && echo '%PASSWORD%' | sudo -S systemctl restart telegram-payment-bot"
echo Done.