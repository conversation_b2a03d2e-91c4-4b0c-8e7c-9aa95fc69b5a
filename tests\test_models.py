"""
Тесты для моделей данных с валидацией
"""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from app.models.models import (
    User, Subscription, Payment, AdminLog, ValidationError,
    create_user_from_telegram, create_subscription_for_plan, create_payment_for_subscription
)


class TestUser:
    """Тесты для модели User"""
    
    def test_valid_user_creation(self):
        """Тест создания валидного пользователя"""
        user = User(
            telegram_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        assert user.telegram_id == 123456789
        assert user.username == "testuser"
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.validate() is True
    
    def test_user_with_minimal_data(self):
        """Тест создания пользователя с минимальными данными"""
        user = User(telegram_id=123456789)
        assert user.validate() is True
        assert user.telegram_id == 123456789
        assert user.username is None
        assert user.first_name is None
        assert user.last_name is None
    
    def test_invalid_telegram_id(self):
        """Тест валидации неверного telegram_id"""
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=0)
        assert "telegram_id должен быть положительным целым числом" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=-123)
        assert "telegram_id должен быть положительным целым числом" in str(exc_info.value)
    
    def test_invalid_username(self):
        """Тест валидации неверного username"""
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=123456789, username="")
        assert "username не может быть пустым" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=123456789, username="a" * 33)
        assert "username не может быть длиннее 32 символов" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=123456789, username="invalid-username!")
        assert "username может содержать только буквы, цифры и подчеркивания" in str(exc_info.value)
    
    def test_invalid_first_name(self):
        """Тест валидации неверного first_name"""
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=123456789, first_name="")
        assert "first_name не может быть пустым" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=123456789, first_name="a" * 65)
        assert "first_name не может быть длиннее 64 символов" in str(exc_info.value)
    
    def test_invalid_last_name(self):
        """Тест валидации неверного last_name"""
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=123456789, last_name="")
        assert "last_name не может быть пустым" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            User(telegram_id=123456789, last_name="a" * 65)
        assert "last_name не может быть длиннее 64 символов" in str(exc_info.value)
    
    def test_get_display_name(self):
        """Тест получения отображаемого имени"""
        # Полное имя
        user = User(telegram_id=123, first_name="John", last_name="Doe")
        assert user.get_display_name() == "John Doe"
        
        # Только имя
        user = User(telegram_id=123, first_name="John")
        assert user.get_display_name() == "John"
        
        # Только username
        user = User(telegram_id=123, username="johndoe")
        assert user.get_display_name() == "@johndoe"
        
        # Только telegram_id
        user = User(telegram_id=123)
        assert user.get_display_name() == "User 123"
    
    def test_to_dict(self):
        """Тест преобразования в словарь"""
        now = datetime.now()
        user = User(
            id=1,
            telegram_id=123456789,
            username="testuser",
            first_name="Test",
            created_at=now
        )
        
        result = user.to_dict()
        assert result['id'] == 1
        assert result['telegram_id'] == 123456789
        assert result['username'] == "testuser"
        assert result['first_name'] == "Test"
        assert result['created_at'] == now.isoformat()


class TestSubscription:
    """Тесты для модели Subscription"""
    
    def test_valid_subscription_creation(self):
        """Тест создания валидной подписки"""
        start_date = datetime.now()
        end_date = start_date + timedelta(days=30)
        
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=start_date,
            end_date=end_date
        )
        
        assert subscription.user_id == 1
        assert subscription.plan_type == "monthly"
        assert subscription.status == "active"
        assert subscription.validate() is True
    
    def test_subscription_with_auto_dates(self):
        """Тест создания подписки с автоматическими датами"""
        subscription = Subscription(
            user_id=1,
            plan_type="monthly"
        )
        
        assert subscription.start_date is not None
        assert subscription.end_date is not None
        assert subscription.end_date > subscription.start_date
        assert subscription.validate() is True
    
    def test_invalid_user_id(self):
        """Тест валидации неверного user_id"""
        with pytest.raises(ValidationError) as exc_info:
            Subscription(user_id=0, plan_type="monthly")
        assert "user_id должен быть положительным целым числом" in str(exc_info.value)
    
    def test_invalid_plan_type(self):
        """Тест валидации неверного plan_type"""
        with pytest.raises(ValidationError) as exc_info:
            Subscription(user_id=1, plan_type="invalid")
        assert "plan_type должен быть одним из" in str(exc_info.value)
    
    def test_invalid_status(self):
        """Тест валидации неверного status"""
        with pytest.raises(ValidationError) as exc_info:
            Subscription(user_id=1, plan_type="monthly", status="invalid")
        assert "status должен быть одним из" in str(exc_info.value)
    
    def test_invalid_date_order(self):
        """Тест валидации неверного порядка дат"""
        start_date = datetime.now()
        end_date = start_date - timedelta(days=1)
        
        with pytest.raises(ValidationError) as exc_info:
            Subscription(
                user_id=1,
                plan_type="monthly",
                start_date=start_date,
                end_date=end_date
            )
        assert "end_date должен быть позже start_date" in str(exc_info.value)
    
    def test_is_active(self):
        """Тест проверки активности подписки"""
        # Активная подписка
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now() - timedelta(days=1),
            end_date=datetime.now() + timedelta(days=30)
        )
        assert subscription.is_active() is True
        
        # Неактивная подписка (истекшая)
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now() - timedelta(days=31),
            end_date=datetime.now() - timedelta(days=1)
        )
        assert subscription.is_active() is False
        
        # Неактивная подписка (отмененная)
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            status="cancelled",
            start_date=datetime.now() - timedelta(days=1),
            end_date=datetime.now() + timedelta(days=30)
        )
        assert subscription.is_active() is False
    
    def test_is_expired(self):
        """Тест проверки истечения подписки"""
        # Истекшая подписка
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            start_date=datetime.now() - timedelta(days=31),
            end_date=datetime.now() - timedelta(days=1)
        )
        assert subscription.is_expired() is True
        
        # Активная подписка
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            start_date=datetime.now() - timedelta(days=1),
            end_date=datetime.now() + timedelta(days=30)
        )
        assert subscription.is_expired() is False
    
    def test_days_until_expiry(self):
        """Тест подсчета дней до истечения"""
        # Подписка истекает через 5 дней
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            start_date=datetime.now() - timedelta(days=25),
            end_date=datetime.now() + timedelta(days=5)
        )
        assert subscription.days_until_expiry() == 5
        
        # Истекшая подписка
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            start_date=datetime.now() - timedelta(days=31),
            end_date=datetime.now() - timedelta(days=1)
        )
        assert subscription.days_until_expiry() == 0
    
    def test_extend_subscription(self):
        """Тест продления подписки"""
        subscription = Subscription(
            user_id=1,
            plan_type="monthly",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30)
        )
        
        original_end_date = subscription.end_date
        subscription.extend_subscription(2)  # Продлить на 2 месяца
        
        assert subscription.end_date > original_end_date
        assert subscription.updated_at is not None
        
        # Тест с неверными параметрами
        with pytest.raises(ValidationError):
            subscription.extend_subscription(0)
        
        with pytest.raises(ValidationError):
            subscription.extend_subscription(-1)


class TestPayment:
    """Тесты для модели Payment"""
    
    def test_valid_payment_creation(self):
        """Тест создания валидного платежа"""
        payment = Payment(
            user_id=1,
            lava_invoice_id="inv_123456",
            amount=Decimal("100.00"),
            payment_method="card_ru",
            payment_url="https://example.com/pay"
        )
        
        assert payment.user_id == 1
        assert payment.lava_invoice_id == "inv_123456"
        assert payment.amount == Decimal("100.00")
        assert payment.payment_method == "card_ru"
        assert payment.validate() is True
    
    def test_payment_with_string_amount(self):
        """Тест создания платежа со строковой суммой"""
        payment = Payment(
            user_id=1,
            lava_invoice_id="inv_123456",
            amount="100.50",
            payment_method="card_ru"
        )
        
        assert payment.amount == Decimal("100.50")
        assert payment.validate() is True
    
    def test_invalid_user_id(self):
        """Тест валидации неверного user_id"""
        with pytest.raises(ValidationError) as exc_info:
            Payment(
                user_id=0,
                lava_invoice_id="inv_123456",
                amount=Decimal("100.00"),
                payment_method="card_ru"
            )
        assert "user_id должен быть положительным целым числом" in str(exc_info.value)
    
    def test_invalid_lava_invoice_id(self):
        """Тест валидации неверного lava_invoice_id"""
        with pytest.raises(ValidationError) as exc_info:
            Payment(
                user_id=1,
                lava_invoice_id="",
                amount=Decimal("100.00"),
                payment_method="card_ru"
            )
        assert "lava_invoice_id не может быть пустым" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            Payment(
                user_id=1,
                lava_invoice_id="a" * 256,
                amount=Decimal("100.00"),
                payment_method="card_ru"
            )
        assert "lava_invoice_id не может быть длиннее 255 символов" in str(exc_info.value)
    
    def test_invalid_amount(self):
        """Тест валидации неверной суммы"""
        with pytest.raises(ValidationError) as exc_info:
            Payment(
                user_id=1,
                lava_invoice_id="inv_123456",
                amount=Decimal("0.00"),
                payment_method="card_ru"
            )
        assert "amount должен быть положительным числом" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            Payment(
                user_id=1,
                lava_invoice_id="inv_123456",
                amount=Decimal("1000000.00"),
                payment_method="card_ru"
            )
        assert "amount не может превышать 999999.99" in str(exc_info.value)
    
    def test_invalid_payment_method(self):
        """Тест валидации неверного способа оплаты"""
        with pytest.raises(ValidationError) as exc_info:
            Payment(
                user_id=1,
                lava_invoice_id="inv_123456",
                amount=Decimal("100.00"),
                payment_method="invalid"
            )
        assert "payment_method должен быть одним из" in str(exc_info.value)
    
    def test_invalid_currency(self):
        """Тест валидации неверной валюты"""
        with pytest.raises(ValidationError) as exc_info:
            Payment(
                user_id=1,
                lava_invoice_id="inv_123456",
                amount=Decimal("100.00"),
                payment_method="card_ru",
                currency="INVALID"
            )
        assert "currency должен быть одним из" in str(exc_info.value)
    
    def test_invalid_payment_url(self):
        """Тест валидации неверного URL оплаты"""
        with pytest.raises(ValidationError) as exc_info:
            Payment(
                user_id=1,
                lava_invoice_id="inv_123456",
                amount=Decimal("100.00"),
                payment_method="card_ru",
                payment_url="invalid-url"
            )
        assert "payment_url должен быть валидным URL" in str(exc_info.value)
    
    def test_valid_payment_urls(self):
        """Тест валидных URL оплаты"""
        valid_urls = [
            "https://example.com",
            "http://localhost:8000",
            "https://pay.lava.top/invoice/123",
            "https://sub.domain.com/path?param=value"
        ]
        
        for url in valid_urls:
            payment = Payment(
                user_id=1,
                lava_invoice_id="inv_123456",
                amount=Decimal("100.00"),
                payment_method="card_ru",
                payment_url=url
            )
            assert payment.validate() is True
    
    def test_payment_status_methods(self):
        """Тест методов проверки статуса платежа"""
        # Ожидающий платеж
        payment = Payment(
            user_id=1,
            lava_invoice_id="inv_123456",
            amount=Decimal("100.00"),
            payment_method="card_ru",
            status="pending"
        )
        assert payment.is_pending() is True
        assert payment.is_completed() is False
        
        # Завершенный платеж
        payment.mark_completed(subscription_id=1)
        assert payment.is_completed() is True
        assert payment.is_pending() is False
        assert payment.subscription_id == 1
        assert payment.completed_at is not None
        
        # Неудачный платеж
        payment.mark_failed()
        assert payment.status == "failed"
        
        # Истекший платеж
        payment.mark_expired()
        assert payment.status == "expired"
    
    def test_is_expired(self):
        """Тест проверки истечения платежа"""
        # Истекший платеж
        payment = Payment(
            user_id=1,
            lava_invoice_id="inv_123456",
            amount=Decimal("100.00"),
            payment_method="card_ru",
            status="pending",
            expires_at=datetime.now() - timedelta(hours=1)
        )
        assert payment.is_expired() is True
        
        # Активный платеж
        payment = Payment(
            user_id=1,
            lava_invoice_id="inv_123456",
            amount=Decimal("100.00"),
            payment_method="card_ru",
            status="pending",
            expires_at=datetime.now() + timedelta(hours=1)
        )
        assert payment.is_expired() is False
    
    def test_get_payment_method_display(self):
        """Тест получения отображаемого названия способа оплаты"""
        payment = Payment(
            user_id=1,
            lava_invoice_id="inv_123456",
            amount=Decimal("100.00"),
            payment_method="card_ru"
        )
        assert payment.get_payment_method_display() == "Карта РФ"
        
        payment.payment_method = "card_foreign"
        assert payment.get_payment_method_display() == "Карта иностранного банка"
        
        payment.payment_method = "crypto"
        assert payment.get_payment_method_display() == "Криптовалюта"


class TestAdminLog:
    """Тесты для модели AdminLog"""
    
    def test_valid_admin_log_creation(self):
        """Тест создания валидного лога администратора"""
        log = AdminLog(
            admin_telegram_id=123456789,
            action="create_subscription",
            target_user_id=987654321,
            details="Создана подписка на 1 месяц"
        )
        
        assert log.admin_telegram_id == 123456789
        assert log.action == "create_subscription"
        assert log.target_user_id == 987654321
        assert log.details == "Создана подписка на 1 месяц"
        assert log.validate() is True
    
    def test_admin_log_minimal_data(self):
        """Тест создания лога с минимальными данными"""
        log = AdminLog(
            admin_telegram_id=123456789,
            action="system_check"
        )
        
        assert log.validate() is True
        assert log.target_user_id is None
        assert log.details is None
    
    def test_invalid_admin_telegram_id(self):
        """Тест валидации неверного admin_telegram_id"""
        with pytest.raises(ValidationError) as exc_info:
            AdminLog(admin_telegram_id=0, action="test")
        assert "admin_telegram_id должен быть положительным целым числом" in str(exc_info.value)
    
    def test_invalid_action(self):
        """Тест валидации неверного action"""
        with pytest.raises(ValidationError) as exc_info:
            AdminLog(admin_telegram_id=123456789, action="")
        assert "action не может быть пустым" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            AdminLog(admin_telegram_id=123456789, action="a" * 256)
        assert "action не может быть длиннее 255 символов" in str(exc_info.value)
    
    def test_invalid_details(self):
        """Тест валидации неверных details"""
        with pytest.raises(ValidationError) as exc_info:
            AdminLog(
                admin_telegram_id=123456789,
                action="test",
                details="a" * 1001
            )
        assert "details не может быть длиннее 1000 символов" in str(exc_info.value)


class TestUtilityFunctions:
    """Тесты для утилитарных функций"""
    
    def test_create_user_from_telegram(self):
        """Тест создания пользователя из Telegram объекта"""
        # Мок объекта Telegram пользователя
        class MockTelegramUser:
            def __init__(self):
                self.id = 123456789
                self.username = "testuser"
                self.first_name = "Test"
                self.last_name = "User"
        
        telegram_user = MockTelegramUser()
        user = create_user_from_telegram(telegram_user)
        
        assert user.telegram_id == 123456789
        assert user.username == "testuser"
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.created_at is not None
        assert user.validate() is True
    
    def test_create_subscription_for_plan(self):
        """Тест создания подписки для плана"""
        subscription = create_subscription_for_plan(1, "monthly")
        
        assert subscription.user_id == 1
        assert subscription.plan_type == "monthly"
        assert subscription.status == "pending"
        assert subscription.start_date is not None
        assert subscription.validate() is True
    
    def test_create_payment_for_subscription(self):
        """Тест создания платежа для подписки"""
        payment = create_payment_for_subscription(
            user_id=1,
            lava_invoice_id="inv_123456",
            amount=Decimal("100.00"),
            payment_method="card_ru",
            payment_url="https://example.com/pay"
        )
        
        assert payment.user_id == 1
        assert payment.lava_invoice_id == "inv_123456"
        assert payment.amount == Decimal("100.00")
        assert payment.payment_method == "card_ru"
        assert payment.payment_url == "https://example.com/pay"
        assert payment.created_at is not None
        assert payment.validate() is True


if __name__ == "__main__":
    pytest.main([__file__])