#!/usr/bin/env python3
"""
Интеграционный тест админ панели для задачи 8.5
"""

import os
import sys
import tempfile
import json
from decimal import Decimal

# Добавляем путь к проекту
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.database import DatabaseManager, DatabaseService
from app.admin import admin_bp, init_admin_services
from flask import Flask

def test_admin_settings_integration():
    """Тестирование интеграции админ панели с системой настроек"""
    print("🧪 Тестирование интеграции админ панели...")
    
    # Создаем временную базу данных для тестов
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        test_db_path = tmp_file.name
    
    try:
        # Инициализируем базу данных
        db_manager = DatabaseManager(test_db_path)
        db_manager.init_database()
        
        db_service = DatabaseService(db_manager)
        
        # Создаем Flask приложение для тестов
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'test-secret-key'
        app.config['TESTING'] = True
        
        # Регистрируем админ blueprint
        app.register_blueprint(admin_bp)
        
        # Инициализируем сервисы админ панели
        init_admin_services(db_service, None)
        
        print("✅ Админ панель инициализирована")
        
        # Создаем тестовый клиент
        with app.test_client() as client:
            # Имитируем авторизацию администратора
            with client.session_transaction() as sess:
                sess['admin_logged_in'] = True
            
            print("\n📋 Тестирование API системных настроек...")
            
            # Тестируем обновление настроек
            settings_data = {
                'settings': {
                    'test_category': {
                        'test_setting': {
                            'value': 'test_value',
                            'data_type': 'string'
                        }
                    }
                }
            }
            
            response = client.post('/admin/api/settings/update',
                                 data=json.dumps(settings_data),
                                 content_type='application/json')
            
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            print("✅ API обновления настроек работает")
            
            print("\n💳 Тестирование API тарифных планов...")
            
            # Тестируем получение тарифных планов
            response = client.get('/admin/api/subscription-plans')
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            assert len(data['plans']) >= 4, "Недостаточно тарифных планов"
            print("✅ API получения тарифных планов работает")
            
            # Тестируем создание тарифного плана
            plan_data = {
                'name': 'Тестовый API план',
                'duration_months': 2,
                'price': 599.00,
                'currency': 'RUB',
                'description': 'Тестовый план через API',
                'is_active': True,
                'sort_order': 10
            }
            
            response = client.post('/admin/api/subscription-plans',
                                 data=json.dumps(plan_data),
                                 content_type='application/json')
            
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            plan_id = data['plan_id']
            print("✅ API создания тарифного плана работает")
            
            # Тестируем обновление тарифного плана
            update_data = {
                'name': 'Обновленный API план',
                'price': 699.00
            }
            
            response = client.put(f'/admin/api/subscription-plans/{plan_id}',
                                data=json.dumps(update_data),
                                content_type='application/json')
            
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            print("✅ API обновления тарифного плана работает")
            
            print("\n🤖 Тестирование API текстов бота...")
            
            # Тестируем получение текстов бота
            response = client.get('/admin/api/bot-texts')
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            assert len(data['texts']) >= 8, "Недостаточно текстов бота"
            print("✅ API получения текстов бота работает")
            
            # Тестируем создание текста бота
            text_data = {
                'key': 'test_api_text',
                'text': 'Тестовый текст через API',
                'description': 'Описание тестового текста'
            }
            
            response = client.post('/admin/api/bot-texts',
                                 data=json.dumps(text_data),
                                 content_type='application/json')
            
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            print("✅ API создания текста бота работает")
            
            print("\n💾 Тестирование API резервного копирования...")
            
            # Тестируем создание резервной копии
            response = client.post('/admin/api/backup/create',
                                 content_type='application/json')
            
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            backup_filename = data['backup_file']
            print("✅ API создания резервной копии работает")
            
            # Тестируем получение списка резервных копий
            response = client.get('/admin/api/backup/list')
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            assert len(data['backups']) >= 1, "Резервные копии не найдены"
            print("✅ API получения списка резервных копий работает")
            
            print("\n⚙️ Тестирование API инициализации настроек по умолчанию...")
            
            # Тестируем инициализацию настроек по умолчанию
            response = client.post('/admin/api/initialize-defaults',
                                 content_type='application/json')
            
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            data = json.loads(response.data)
            assert data['success'], f"Ошибка API: {data.get('error', 'Неизвестная ошибка')}"
            print("✅ API инициализации настроек по умолчанию работает")
            
            print("\n📄 Тестирование страницы настроек...")
            
            # Тестируем загрузку страницы настроек
            response = client.get('/admin/settings')
            assert response.status_code == 200, f"Неверный статус код: {response.status_code}"
            assert b'settings' in response.data.lower(), "Страница настроек не содержит ожидаемый контент"
            print("✅ Страница настроек загружается корректно")
        
        print("\n🎉 Все интеграционные тесты пройдены успешно!")
        
        # Проверяем финальное состояние базы данных
        all_settings = db_service.get_all_settings()
        all_plans = db_service.get_all_subscription_plans()
        all_texts = db_service.get_all_bot_texts()
        
        print("\n📊 Финальная статистика:")
        print(f"   • Системных настроек: {sum(len(settings) for settings in all_settings.values())}")
        print(f"   • Тарифных планов: {len(all_plans)}")
        print(f"   • Текстов бота: {len(all_texts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка интеграционного тестирования: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Удаляем временные файлы
        try:
            if os.path.exists(test_db_path):
                os.unlink(test_db_path)
            # Удаляем созданные резервные копии
            import glob
            backup_files = glob.glob('backups/payments_backup_*.db')
            for backup_file in backup_files:
                try:
                    os.unlink(backup_file)
                except:
                    pass
        except:
            pass

if __name__ == '__main__':
    success = test_admin_settings_integration()
    sys.exit(0 if success else 1)