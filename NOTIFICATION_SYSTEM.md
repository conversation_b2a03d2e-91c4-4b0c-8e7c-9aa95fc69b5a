# Система уведомлений Telegram бота

## 🔔 Обзор системы

Система уведомлений обеспечивает автоматическую отправку сообщений пользователям на всех этапах взаимодействия с ботом - от создания платежа до истечения подписки.

## 🏗️ Архитектура

### Основные компоненты

```
NotificationService
├── TelegramBotHandler (отправка сообщений)
├── SchedulerService (автоматические уведомления)
├── WebhookHandler (уведомления о платежах)
└── DatabaseService (данные пользователей)
```

### Поток уведомлений

```mermaid
graph TD
    A[Событие] --> B[NotificationService]
    B --> C[TelegramBotHandler]
    C --> D[Telegram API]
    D --> E[Пользователь]
    
    F[Планировщик] --> G[Проверка подписок]
    G --> B
    
    H[Webhook] --> I[WebhookHandler]
    I --> B
```

## 📱 Типы уведомлений

### 1. Уведомления о платежах

#### ✅ Успешная оплата
**Триггер:** Webhook от Lava.top со статусом `completed`

**Содержание:**
```
✅ Платеж успешно обработан!

💳 Детали платежа:
• Номер заказа: tg_123456789_1753479676
• Сумма: 299 ₽
• План: 1 месяц

🎉 Ваша подписка активирована!

Сейчас мы создадим для вас пригласительную ссылку...
```

**Кнопки:**
- `📊 Проверить статус подписки`

#### ❌ Неудачная оплата
**Триггер:** Webhook от Lava.top со статусом `failed`

**Содержание:**
```
❌ Платеж не удался

📋 Детали:
• Номер заказа: tg_123456789_1753479676
• Причина: Недостаточно средств на карте

💡 Что делать:
• Попробуйте создать новый счет для оплаты
• Проверьте данные карты и баланс
• Обратитесь в поддержку, если проблема повторяется
```

**Кнопки:**
- `🔄 Попробовать снова`
- `🆘 Связаться с поддержкой`

### 2. Пригласительные ссылки

#### 🔗 Доступ к каналу
**Триггер:** После успешной оплаты и создания подписки

**Содержание:**
```
🎉 Добро пожаловать в приватный канал!

Ваша подписка активирована и готова к использованию.

📅 Подписка действует до: 25.08.2025 в 21:41

👇 Нажмите кнопку ниже, чтобы присоединиться к каналу:
```

**Кнопки:**
- `🔗 Присоединиться к каналу` (URL)
- `📊 Статус подписки`
- `ℹ️ О канале`

### 3. Уведомления об истечении подписки

#### ⚠️ Предупреждения (за 7, 3, 1 день)
**Триггер:** Автоматически через планировщик

**За 1 день:**
```
⚠️ Уведомление о подписке

Ваша подписка истекает завтра. Рекомендуем продлить её заранее, чтобы не потерять доступ к каналу.

📊 Статус: истекает завтра
```

**За 3 дня:**
```
⚠️ Уведомление о подписке

Ваша подписка истекает через 3 дня. Не забудьте продлить её, чтобы сохранить доступ к каналу.

📊 Статус: истекает через 3 дня
```

**Кнопки:**
- `💳 Продлить подписку`
- `📊 Статус подписки`

#### 🔴 Подписка истекла
**Триггер:** После истечения подписки

**Содержание:**
```
⏰ Подписка истекла

Ваша подписка на приватный канал истекла. Доступ к каналу приостановлен.

💡 Чтобы восстановить доступ:
• Выберите подходящий тарифный план
• Оплатите подписку
• Получите новую пригласительную ссылку

Мы будем рады видеть вас снова в нашем канале!
```

**Кнопки:**
- `💳 Продлить подписку`
- `ℹ️ О канале`
- `🆘 Поддержка`

## 🤖 Реализация в коде

### NotificationService

```python
class NotificationService:
    def __init__(self, bot_handler: TelegramBotHandler, db_service: DatabaseService):
        self.bot_handler = bot_handler
        self.db_service = db_service
    
    def send_expiry_notifications(self) -> Dict[str, int]:
        """Отправляет уведомления об истечении подписок"""
        stats = {
            'expiring_today': 0,
            'expiring_tomorrow': 0,
            'expiring_in_3_days': 0,
            'expired': 0,
            'errors': 0
        }
        
        expiring_subscriptions = self.db_service.get_expiring_subscriptions()
        
        for subscription in expiring_subscriptions:
            user = self.db_service.get_user_by_id(subscription.user_id)
            days_left = subscription.days_until_expiry()
            
            if days_left <= 0:
                success = self.bot_handler.send_subscription_expired_notification(user.telegram_id)
                if success: stats['expired'] += 1
            elif days_left == 1:
                success = self.bot_handler.send_subscription_expiry_warning(user.telegram_id, days_left)
                if success: stats['expiring_tomorrow'] += 1
            # ... и т.д.
        
        return stats
```

### TelegramBotHandler методы уведомлений

```python
def send_payment_success_notification(self, user_id: int, order_id: str, amount: float, plan_months: int) -> bool:
    """Отправляет уведомление об успешной оплате"""
    
def send_payment_failed_notification(self, user_id: int, order_id: str, error_message: str = None) -> bool:
    """Отправляет уведомление о неудачной оплате"""
    
def send_invite_link_notification(self, user_id: int, invite_url: str, subscription_end_date: datetime) -> bool:
    """Отправляет пригласительную ссылку на канал"""
    
def send_subscription_expiry_warning(self, user_id: int, days_left: int) -> bool:
    """Отправляет предупреждение об истечении подписки"""
    
def send_subscription_expired_notification(self, user_id: int) -> bool:
    """Отправляет уведомление об истечении подписки"""
```

## ⏰ Автоматические уведомления

### Планировщик задач

```python
class SchedulerService:
    def _add_expiration_warning_job(self):
        """Добавляет задачу отправки предупреждений об истечении"""
        self.scheduler.add_job(
            func=self.send_expiration_warnings,
            trigger=CronTrigger(hour=10, minute=0),  # Каждый день в 10:00
            id='send_expiration_warnings',
            name='Отправка предупреждений об истечении подписок'
        )
```

### Расписание автоматических уведомлений

| Время | Задача | Описание |
|-------|--------|----------|
| Каждый час | Проверка истекающих подписок | Обновление статусов, удаление из канала |
| 10:00 ежедневно | Предупреждения об истечении | За 1, 3, 7 дней до истечения |
| 11:00 ежедневно | Уведомления об истечении | Для уже истекших подписок |
| При webhook | Уведомления о платежах | Мгновенно после получения от Lava.top |

## 📊 Статистика уведомлений

### Метрики системы

```python
# Статистика отправки уведомлений
{
    'expiring_today': 5,        # Истекают сегодня
    'expiring_tomorrow': 12,    # Истекают завтра
    'expiring_in_3_days': 8,    # Истекают через 3 дня
    'expired': 3,               # Уже истекли
    'errors': 0                 # Ошибки отправки
}
```

### Мониторинг через API

```bash
# Общие метрики системы
curl http://localhost:5000/metrics

# Статистика webhook
curl http://localhost:5000/webhook/test

# Здоровье системы
curl http://localhost:5000/health
```

## 🔧 Настройка уведомлений

### Конфигурация в .env

```env
# Интервалы уведомлений (в днях)
EXPIRY_WARNING_DAYS=7,3,1

# Время отправки ежедневных уведомлений
DAILY_NOTIFICATIONS_HOUR=10

# Включение/отключение типов уведомлений
PAYMENT_NOTIFICATIONS_ENABLED=true
EXPIRY_NOTIFICATIONS_ENABLED=true
INVITE_NOTIFICATIONS_ENABLED=true

# Настройки планировщика
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=Europe/Moscow
```

### Кастомизация текстов

Тексты уведомлений можно настроить через базу данных:

```python
# Установка кастомного текста
db_service.set_bot_text(
    'payment_success', 
    '🎉 Ваш платеж успешно обработан!\n\nСпасибо за покупку подписки!',
    'Сообщение об успешном платеже'
)

# Получение текста
text = db_service.get_bot_text('payment_success')
```

## 🛠️ Тестирование уведомлений

### Mock тестирование

```python
class MockBotHandler:
    def __init__(self):
        self.sent_notifications = []
    
    def send_payment_success_notification(self, user_id, order_id, amount, plan_months):
        notification = {
            'type': 'payment_success',
            'user_id': user_id,
            'order_id': order_id,
            'timestamp': datetime.now()
        }
        self.sent_notifications.append(notification)
        return True
```

### Тестовые сценарии

```bash
# Тест всех типов уведомлений
python test_notifications_integration.py

# Тест планировщика
python -c "
from app.services.scheduler_service import SchedulerService
scheduler = SchedulerService(db_service, notification_service, channel_manager)
result = scheduler.send_expiration_warnings()
print(result)
"

# Тест отдельного уведомления
python -c "
from app.services.bot_handler import TelegramBotHandler
bot = TelegramBotHandler(db_service, payment_service, channel_manager)
bot.send_payment_success_notification(123456789, 'test_order', 299.0, 1)
"
```

## 🔒 Безопасность уведомлений

### Защита от спама

- **Rate limiting** - не более 10 уведомлений в минуту на пользователя
- **Дедупликация** - предотвращение повторных уведомлений
- **Валидация пользователей** - проверка существования в БД

### Обработка ошибок

```python
def send_notification_with_retry(self, notification_func, *args, **kwargs):
    """Отправка уведомления с повторными попытками"""
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            return notification_func(*args, **kwargs)
        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"Не удалось отправить уведомление после {max_retries} попыток: {e}")
                return False
            time.sleep(2 ** attempt)  # Экспоненциальная задержка
```

## 📈 Оптимизация производительности

### Асинхронная отправка

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AsyncNotificationService:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=5)
    
    async def send_bulk_notifications(self, notifications):
        """Асинхронная отправка множественных уведомлений"""
        tasks = []
        for notification in notifications:
            task = asyncio.get_event_loop().run_in_executor(
                self.executor, 
                self.send_notification, 
                notification
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
```

### Кэширование

- **Кэш пользователей** - избежание повторных запросов к БД
- **Кэш шаблонов** - предкомпилированные тексты уведомлений
- **Кэш статистики** - агрегированные данные

## 🚨 Мониторинг и алерты

### Критические метрики

- **Процент доставки** уведомлений > 95%
- **Время отправки** < 5 секунд
- **Количество ошибок** < 1% от общего числа
- **Доступность Telegram API** > 99%

### Алерты

```python
def check_notification_health():
    """Проверка здоровья системы уведомлений"""
    stats = get_notification_stats()
    
    if stats['error_rate'] > 0.05:  # Более 5% ошибок
        send_admin_alert("Высокий процент ошибок уведомлений")
    
    if stats['avg_response_time'] > 10:  # Более 10 секунд
        send_admin_alert("Медленная отправка уведомлений")
```

## 📝 Логирование уведомлений

### Структура логов

```
2025-07-25 21:41:16 - notification - INFO - Уведомление об успешной оплате отправлено пользователю 123456789
2025-07-25 21:41:17 - notification - INFO - Пригласительная ссылка отправлена пользователю 123456789
2025-07-25 21:41:18 - notification - ERROR - Ошибка отправки уведомления пользователю 987654321: Telegram API timeout
```

### Анализ логов

```bash
# Статистика уведомлений за день
grep "$(date +%Y-%m-%d)" bot.log | grep "notification" | wc -l

# Ошибки уведомлений
grep "notification.*ERROR" bot.log | tail -10

# Успешные уведомления по типам
grep "notification.*INFO.*успешной оплате" bot.log | wc -l
grep "notification.*INFO.*Пригласительная ссылка" bot.log | wc -l
```

## 🔄 Обслуживание системы

### Регулярные задачи

#### Ежедневно:
- Проверка статистики доставки
- Анализ ошибок уведомлений
- Очистка старых логов

#### Еженедельно:
- Анализ эффективности уведомлений
- Оптимизация текстов сообщений
- Проверка производительности

#### Ежемесячно:
- Обновление шаблонов уведомлений
- Анализ пользовательской активности
- Планирование улучшений

### Резервное копирование

```bash
# Бэкап настроек уведомлений
python -c "
from app.models.database import DatabaseService
db = DatabaseService()
texts = db.get_all_bot_texts()
import json
with open('notification_texts_backup.json', 'w') as f:
    json.dump([{
        'key': t.key, 
        'text': t.text, 
        'description': t.description
    } for t in texts], f, ensure_ascii=False, indent=2)
"
```

---

**Система уведомлений обеспечивает полный цикл взаимодействия с пользователями и готова к продуктивному использованию!** 🎯