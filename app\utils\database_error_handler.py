"""
Специализированная обработка ошибок базы данных с восстановлением
"""

import logging
import sqlite3
import shutil
import os
import functools
from typing import Optional, Dict, Any, Callable
from datetime import datetime
from contextlib import contextmanager

from app.utils.error_handler import (
    <PERSON>rror<PERSON><PERSON>ler, ErrorCategory, ErrorSeverity, 
    with_error_handling, with_retry, recovery_manager
)
from config import Config

logger = logging.getLogger('database_error_handler')

class DatabaseErrorHandler:
    """Специализированный обработчик ошибок базы данных"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.backup_dir = os.path.join(os.path.dirname(db_path), 'backups')
        self.error_handler = ErrorHandler()
        
        # Создаем директорию для резервных копий
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Регистрируем стратегии восстановления
        self._register_recovery_strategies()
    
    def _register_recovery_strategies(self):
        """Регистрирует стратегии восстановления для различных ошибок БД"""
        
        recovery_manager.register_recovery_strategy(
            sqlite3.DatabaseError,
            self._recover_from_database_error
        )
        
        # Регистрируем стратегию восстановления для повреждения БД
        # sqlite3.CorruptError не существует в стандартной библиотеке, используем DatabaseError
        recovery_manager.register_recovery_strategy(
            sqlite3.DatabaseError,
            self._recover_from_corruption
        )
        
        recovery_manager.register_recovery_strategy(
            sqlite3.OperationalError,
            self._recover_from_operational_error
        )
    
    def _recover_from_database_error(self, error: sqlite3.DatabaseError) -> bool:
        """
        Восстановление от общих ошибок базы данных
        
        Args:
            error: Ошибка базы данных
            
        Returns:
            True если восстановление успешно
        """
        logger.warning(f"Попытка восстановления от ошибки БД: {error}")
        
        try:
            # Проверяем доступность файла БД
            if not os.path.exists(self.db_path):
                logger.error("Файл базы данных не существует")
                return self._restore_from_backup()
            
            # Проверяем права доступа
            if not os.access(self.db_path, os.R_OK | os.W_OK):
                logger.error("Нет прав доступа к файлу БД")
                try:
                    os.chmod(self.db_path, 0o666)
                    logger.info("Права доступа к БД восстановлены")
                    return True
                except OSError as e:
                    logger.error(f"Не удалось изменить права доступа: {e}")
                    return False
            
            # Пытаемся выполнить простой запрос для проверки
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("SELECT 1").fetchone()
                logger.info("База данных доступна после проверки")
                return True
            except sqlite3.Error:
                logger.warning("БД недоступна, пытаемся восстановить из резервной копии")
                return self._restore_from_backup()
        
        except Exception as e:
            logger.error(f"Ошибка при восстановлении от ошибки БД: {e}")
            return False
    
    def _recover_from_corruption(self, error: sqlite3.DatabaseError) -> bool:
        """
        Восстановление от повреждения базы данных
        
        Args:
            error: Ошибка повреждения
            
        Returns:
            True если восстановление успешно
        """
        logger.error(f"Обнаружено повреждение БД: {error}")
        
        try:
            # Создаем резервную копию поврежденной БД
            corrupted_backup = f"{self.db_path}.corrupted.{int(datetime.now().timestamp())}"
            shutil.copy2(self.db_path, corrupted_backup)
            logger.info(f"Поврежденная БД сохранена как {corrupted_backup}")
            
            # Пытаемся восстановить данные из поврежденной БД
            recovered_data = self._extract_data_from_corrupted_db()
            
            # Восстанавливаем из резервной копии
            if self._restore_from_backup():
                # Пытаемся восстановить извлеченные данные
                if recovered_data and self._restore_recovered_data(recovered_data):
                    logger.info("Данные успешно восстановлены из поврежденной БД")
                return True
            
            return False
        
        except Exception as e:
            logger.error(f"Ошибка при восстановлении от повреждения БД: {e}")
            return False
    
    def _recover_from_operational_error(self, error: sqlite3.OperationalError) -> bool:
        """
        Восстановление от операционных ошибок
        
        Args:
            error: Операционная ошибка
            
        Returns:
            True если восстановление успешно
        """
        error_msg = str(error).lower()
        
        if "database is locked" in error_msg:
            return self._recover_from_lock_error()
        elif "no such table" in error_msg:
            return self._recover_from_missing_table()
        elif "disk i/o error" in error_msg:
            return self._recover_from_io_error()
        else:
            logger.warning(f"Неизвестная операционная ошибка БД: {error}")
            return False
    
    def _recover_from_lock_error(self) -> bool:
        """Восстановление от ошибки блокировки БД"""
        logger.warning("БД заблокирована, ожидаем разблокировки")
        
        import time
        max_wait_time = 30  # максимальное время ожидания в секундах
        wait_interval = 0.5
        total_waited = 0
        
        while total_waited < max_wait_time:
            try:
                with sqlite3.connect(self.db_path, timeout=1.0) as conn:
                    conn.execute("SELECT 1").fetchone()
                logger.info("БД разблокирована")
                return True
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower():
                    time.sleep(wait_interval)
                    total_waited += wait_interval
                else:
                    break
        
        logger.error("Не удалось дождаться разблокировки БД")
        return False
    
    def _recover_from_missing_table(self) -> bool:
        """Восстановление от отсутствующих таблиц"""
        logger.warning("Обнаружены отсутствующие таблицы, пересоздаем схему")
        
        try:
            # Импортируем DatabaseManager для пересоздания схемы
            from app.models.database import DatabaseManager
            
            db_manager = DatabaseManager(self.db_path)
            db_manager.init_database()
            
            logger.info("Схема БД успешно пересоздана")
            return True
        
        except Exception as e:
            logger.error(f"Ошибка при пересоздании схемы БД: {e}")
            return False
    
    def _recover_from_io_error(self) -> bool:
        """Восстановление от ошибок ввода-вывода"""
        logger.error("Ошибка ввода-вывода БД, проверяем файловую систему")
        
        try:
            # Проверяем доступное место на диске
            statvfs = os.statvfs(os.path.dirname(self.db_path))
            free_space = statvfs.f_frsize * statvfs.f_bavail
            
            if free_space < 100 * 1024 * 1024:  # Менее 100 МБ
                logger.error(f"Недостаточно места на диске: {free_space / 1024 / 1024:.1f} МБ")
                return False
            
            # Пытаемся восстановить из резервной копии
            return self._restore_from_backup()
        
        except Exception as e:
            logger.error(f"Ошибка при проверке файловой системы: {e}")
            return False
    
    def create_backup(self, backup_name: Optional[str] = None) -> bool:
        """
        Создает резервную копию базы данных
        
        Args:
            backup_name: Имя резервной копии (None = автоматическое)
            
        Returns:
            True если резервная копия создана успешно
        """
        try:
            if not os.path.exists(self.db_path):
                logger.warning("Файл БД не существует, резервная копия не создана")
                return False
            
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}.db"
            
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            # Создаем резервную копию с использованием SQLite backup API
            with sqlite3.connect(self.db_path) as source_conn:
                with sqlite3.connect(backup_path) as backup_conn:
                    source_conn.backup(backup_conn)
            
            logger.info(f"Резервная копия создана: {backup_path}")
            
            # Очищаем старые резервные копии
            self._cleanup_old_backups()
            
            return True
        
        except Exception as e:
            logger.error(f"Ошибка создания резервной копии: {e}")
            return False
    
    def _restore_from_backup(self) -> bool:
        """
        Восстанавливает БД из последней резервной копии
        
        Returns:
            True если восстановление успешно
        """
        try:
            # Находим последнюю резервную копию
            backup_files = [
                f for f in os.listdir(self.backup_dir) 
                if f.endswith('.db') and f.startswith('backup_')
            ]
            
            if not backup_files:
                logger.error("Резервные копии не найдены")
                return False
            
            # Сортируем по времени создания (новые первыми)
            backup_files.sort(key=lambda x: os.path.getctime(
                os.path.join(self.backup_dir, x)
            ), reverse=True)
            
            latest_backup = os.path.join(self.backup_dir, backup_files[0])
            
            # Создаем резервную копию текущей БД перед восстановлением
            if os.path.exists(self.db_path):
                damaged_backup = f"{self.db_path}.damaged.{int(datetime.now().timestamp())}"
                shutil.move(self.db_path, damaged_backup)
                logger.info(f"Поврежденная БД сохранена как {damaged_backup}")
            
            # Восстанавливаем из резервной копии
            shutil.copy2(latest_backup, self.db_path)
            
            # Проверяем восстановленную БД
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("SELECT 1").fetchone()
            
            logger.info(f"БД успешно восстановлена из {latest_backup}")
            return True
        
        except Exception as e:
            logger.error(f"Ошибка восстановления из резервной копии: {e}")
            return False
    
    def _extract_data_from_corrupted_db(self) -> Optional[Dict[str, Any]]:
        """
        Пытается извлечь данные из поврежденной БД
        
        Returns:
            Словарь с извлеченными данными или None
        """
        try:
            recovered_data = {}
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # Получаем список таблиц
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """)
                
                tables = [row[0] for row in cursor.fetchall()]
                
                for table in tables:
                    try:
                        cursor.execute(f"SELECT * FROM {table}")
                        rows = cursor.fetchall()
                        recovered_data[table] = [dict(row) for row in rows]
                        logger.info(f"Извлечено {len(rows)} записей из таблицы {table}")
                    except sqlite3.Error as e:
                        logger.warning(f"Не удалось извлечь данные из таблицы {table}: {e}")
            
            return recovered_data if recovered_data else None
        
        except Exception as e:
            logger.error(f"Ошибка извлечения данных из поврежденной БД: {e}")
            return None
    
    def _restore_recovered_data(self, recovered_data: Dict[str, Any]) -> bool:
        """
        Восстанавливает извлеченные данные в новую БД
        
        Args:
            recovered_data: Извлеченные данные
            
        Returns:
            True если восстановление успешно
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for table_name, rows in recovered_data.items():
                    if not rows:
                        continue
                    
                    # Получаем структуру таблицы
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns_info = cursor.fetchall()
                    
                    if not columns_info:
                        logger.warning(f"Таблица {table_name} не существует в новой БД")
                        continue
                    
                    column_names = [col[1] for col in columns_info]
                    
                    # Вставляем данные
                    for row in rows:
                        # Фильтруем только существующие колонки
                        filtered_row = {k: v for k, v in row.items() if k in column_names}
                        
                        if filtered_row:
                            placeholders = ', '.join(['?' for _ in filtered_row])
                            columns = ', '.join(filtered_row.keys())
                            values = list(filtered_row.values())
                            
                            try:
                                cursor.execute(
                                    f"INSERT OR IGNORE INTO {table_name} ({columns}) VALUES ({placeholders})",
                                    values
                                )
                            except sqlite3.Error as e:
                                logger.warning(f"Ошибка вставки записи в {table_name}: {e}")
                
                conn.commit()
                logger.info("Извлеченные данные успешно восстановлены")
                return True
        
        except Exception as e:
            logger.error(f"Ошибка восстановления извлеченных данных: {e}")
            return False
    
    def _cleanup_old_backups(self, max_backups: int = 10):
        """
        Очищает старые резервные копии
        
        Args:
            max_backups: Максимальное количество резервных копий для хранения
        """
        try:
            backup_files = [
                f for f in os.listdir(self.backup_dir) 
                if f.endswith('.db') and f.startswith('backup_')
            ]
            
            if len(backup_files) <= max_backups:
                return
            
            # Сортируем по времени создания (старые первыми)
            backup_files.sort(key=lambda x: os.path.getctime(
                os.path.join(self.backup_dir, x)
            ))
            
            # Удаляем старые резервные копии
            files_to_delete = backup_files[:-max_backups]
            
            for file_to_delete in files_to_delete:
                file_path = os.path.join(self.backup_dir, file_to_delete)
                os.remove(file_path)
                logger.info(f"Удалена старая резервная копия: {file_to_delete}")
        
        except Exception as e:
            logger.error(f"Ошибка очистки старых резервных копий: {e}")
    
    @contextmanager
    def safe_transaction(self, conn: sqlite3.Connection):
        """
        Контекстный менеджер для безопасных транзакций с автоматическим откатом
        
        Args:
            conn: Соединение с БД
        """
        try:
            conn.execute("BEGIN")
            yield conn
            conn.commit()
        except Exception as e:
            conn.rollback()
            logger.error(f"Транзакция отменена из-за ошибки: {e}")
            raise
    
    def check_database_integrity(self) -> bool:
        """
        Проверяет целостность базы данных
        
        Returns:
            True если БД целая
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Проверка целостности
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                
                if result and result[0] == "ok":
                    logger.info("Проверка целостности БД пройдена")
                    return True
                else:
                    logger.error(f"Проблемы с целостностью БД: {result}")
                    return False
        
        except Exception as e:
            logger.error(f"Ошибка проверки целостности БД: {e}")
            return False
    
    def optimize_database(self) -> bool:
        """
        Оптимизирует базу данных (VACUUM, ANALYZE)
        
        Returns:
            True если оптимизация успешна
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # VACUUM для дефрагментации
                cursor.execute("VACUUM")
                logger.info("VACUUM выполнен")
                
                # ANALYZE для обновления статистики
                cursor.execute("ANALYZE")
                logger.info("ANALYZE выполнен")
                
                return True
        
        except Exception as e:
            logger.error(f"Ошибка оптимизации БД: {e}")
            return False

# Декораторы для автоматической обработки ошибок БД
def with_database_error_handling(severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                               should_retry: bool = True):
    """Декоратор для обработки ошибок БД"""
    return with_error_handling(
        category=ErrorCategory.DATABASE,
        severity=severity,
        should_retry=should_retry
    )

def with_database_retry(max_retries: Optional[int] = None):
    """Декоратор для повторных попыток операций БД"""
    return with_retry(
        category=ErrorCategory.DATABASE,
        max_retries=max_retries
    )

def with_database_transaction(db_error_handler: DatabaseErrorHandler):
    """
    Декоратор для выполнения операций в безопасной транзакции
    
    Args:
        db_error_handler: Экземпляр DatabaseErrorHandler
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Предполагаем, что первый аргумент - это соединение с БД
            if args and isinstance(args[0], sqlite3.Connection):
                conn = args[0]
                with db_error_handler.safe_transaction(conn):
                    return func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        return wrapper
    return decorator