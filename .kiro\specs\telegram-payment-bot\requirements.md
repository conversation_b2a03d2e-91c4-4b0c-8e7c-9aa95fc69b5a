# Документ требований

## Введение

Данная функция реализует комплексную систему Telegram-бота, которая интегрируется с платежной системой Lava.top для управления платными подписками на приватный Telegram-канал. Система обрабатывает покупку подписок, верификацию платежей, автоматическое управление доступом к каналу и отслеживание жизненного цикла подписок с несколькими способами оплаты и тарифными планами.

## Требования

### Требование 1

**Пользовательская история:** Как потенциальный подписчик, я хочу приобрести доступ к каналу через Telegram-бота с несколькими вариантами подписки, чтобы выбрать план, который лучше всего подходит моим потребностям.

#### Критерии приемки

1. КОГДА пользователь отправляет команду "/купить подписку" ТО бот ДОЛЖЕН отобразить 4 тарифных плана (1, 3, 6, 12 месяцев)
2. КОГДА пользователь выбирает тарифный план ТО бот ДОЛЖЕН отобразить 3 варианта способов оплаты (Карта РФ, Карта иностранного банка, Криптовалюта)
3. КОГДА пользователь выбирает способ оплаты ТО бот ДОЛЖЕН сгенерировать уникальную ссылку для оплаты через Lava.top API
4. КОГДА ссылка для оплаты сгенерирована ТО бот ДОЛЖЕН отправить URL оплаты пользователю с временем истечения
5. ЕСЛИ генерация ссылки для оплаты не удалась ТО бот ДОЛЖЕН уведомить пользователя и предложить попробовать снова

### Требование 2

**Пользовательская история:** Как пользователь, я хочу совершать платежи различными способами, поддерживаемыми Lava.top, чтобы платить предпочитаемым способом оплаты.

#### Критерии приемки

1. КОГДА пользователь выбирает оплату картой РФ ТО система ДОЛЖНА создать счет Lava.top с конфигурацией для российских карт
2. КОГДА пользователь выбирает оплату иностранной картой ТО система ДОЛЖНА создать счет Lava.top с конфигурацией для международных карт
3. КОГДА пользователь выбирает оплату криптовалютой ТО система ДОЛЖНА создать счет Lava.top с конфигурацией для криптовалют
4. КОГДА платеж инициирован ТО система ДОЛЖНА сохранить запись о платеже с уникальным order_id в формате "tg_{user_id}_{timestamp}"
5. КОГДА платеж завершен ТО Lava.top ДОЛЖНА отправить webhook на конечную точку системы

### Требование 3

**Пользовательская история:** Как подписчик, я хочу автоматический доступ к приватному каналу после успешной оплаты, чтобы не ждать ручного одобрения.

#### Критерии приемки

1. КОГДА webhook подтверждает успешный платеж ТО система ДОЛЖНА проверить подлинность платежа
2. КОГДА платеж проверен ТО система ДОЛЖНА сгенерировать временную пригласительную ссылку для приватного канала
3. КОГДА пригласительная ссылка создана ТО система ДОЛЖНА отправить ссылку пользователю через Telegram
4. КОГДА пользователь присоединяется к каналу ТО система ДОЛЖНА записать дату начала подписки и истечения
5. ЕСЛИ у пользователя уже есть активная подписка ТО система ДОЛЖНА продлить существующий период подписки

### Требование 4

**Пользовательская история:** Как подписчик, я хочу проверять статус своей подписки и получать уведомления об истечении, чтобы эффективно управлять своим доступом.

#### Критерии приемки

1. КОГДА пользователь отправляет команду "/статус подписки" ТО бот ДОЛЖЕН отобразить текущий статус подписки, дату истечения и оставшиеся дни
2. КОГДА до истечения подписки остается 7 дней ТО система ДОЛЖНА отправить предупреждение об истечении
3. КОГДА до истечения подписки остается 1 день ТО система ДОЛЖНА отправить финальное предупреждение об истечении
4. КОГДА подписка истекает ТО система ДОЛЖНА автоматически удалить пользователя из приватного канала
5. ЕСЛИ у пользователя нет активной подписки ТО бот ДОЛЖЕН отобразить варианты покупки подписки

### Требование 5

**Пользовательская история:** Как пользователь, я хочу получать информацию о канале и задавать вопросы, чтобы понимать сервис перед покупкой.

#### Критерии приемки

1. КОГДА пользователь отправляет команду "/о канале" ТО бот ДОЛЖЕН отобразить описание канала, преимущества и примеры контента
2. КОГДА пользователь отправляет команду "/задать вопрос" ТО бот ДОЛЖЕН предоставить контактную информацию или варианты поддержки
3. КОГДА пользователь отправляет команду "/start" ТО бот ДОЛЖЕН отобразить приветственное сообщение с доступными командами
4. КОГДА пользователь отправляет неизвестную команду ТО бот ДОЛЖЕН отобразить справочное сообщение с доступными командами

### Требование 6

**Пользовательская история:** Как системный администратор, я хочу надежное сохранение данных и обработку ошибок, чтобы система работала стабильно и данные пользователей были защищены.

#### Критерии приемки

1. КОГДА происходит любая операция с базой данных ТО система ДОЛЖНА использовать SQLite с правильной обработкой транзакций
2. КОГДА получен webhook ТО система ДОЛЖНА проверить подпись и предотвратить дублированную обработку
3. КОГДА платеж не удался или истек ТО система ДОЛЖНА очистить записи о незавершенных платежах
4. КОГДА система сталкивается с ошибками ТО система ДОЛЖНА логировать ошибки и продолжать работу
5. ЕСЛИ база данных повреждена ТО система ДОЛЖНА создать резервную копию и попытаться восстановить

### Требование 7

**Пользовательская история:** Как системный администратор, я хочу административные элементы управления подписками, чтобы обрабатывать особые случаи и мониторить состояние системы.

#### Критерии приемки

1. КОГДА администратор отправляет административную команду ТО система ДОЛЖНА проверить права администратора через белый список ID пользователей
2. КОГДА администратор запрашивает информацию о пользователе ТО система ДОЛЖНА отобразить детали подписки пользователя и историю платежей
3. КОГДА администратор вручную предоставляет подписку ТО система ДОЛЖНА добавить пользователя в канал и обновить базу данных
4. КОГДА администратор вручную отзывает подписку ТО система ДОЛЖНА удалить пользователя из канала и обновить базу данных
5. КОГДА администратор запрашивает статистику системы ТО система ДОЛЖНА отобразить активные подписки, доходы и количество ошибок

### Требование 8

**Пользовательская история:** Как оператор системы, я хочу автоматическое управление жизненным циклом подписок, чтобы контроль доступа поддерживался без ручного вмешательства.

#### Критерии приемки

1. КОГДА система запускается ТО система ДОЛЖНА проверить все активные подписки на истечение
2. КОГДА подписка истекает ТО система ДОЛЖНА удалить пользователя из канала в течение 1 часа
3. КОГДА платеж пользователя возвращен ТО система ДОЛЖНА немедленно отозвать доступ к каналу
4. КОГДА система обнаруживает дублированные платежи ТО система ДОЛЖНА предотвратить двойное продление подписки
5. ЕСЛИ приглашение в канал не удалось ТО система ДОЛЖНА повторить попытку до 3 раз перед уведомлением администратора