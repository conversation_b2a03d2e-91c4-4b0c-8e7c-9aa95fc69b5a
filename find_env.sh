#!/bin/bash

# Server details
SERVER_IP="**************"
USERNAME="ubuntu"
PASSWORD="dkomqgTaijxro7in^bxd"

# Function to run SSH command
run_ssh() {
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_IP} "$1"
}

echo "1. Checking home directory..."
run_ssh "ls -la ~/"

echo -e "\n2. Checking for any .env files..."
run_ssh "sudo find /home -name '.env' -type f 2>/dev/null || echo 'No .env files found in /home'"

echo -e "\n3. Checking for running services..."
run_ssh "ps aux | grep -E 'python|node|telegram|bot' | grep -v grep"

echo -e "\n4. Checking systemd services..."
run_ssh "sudo systemctl list-units --type=service | grep -E 'bot|telegram|node|python'"

echo -e "\n5. Checking web root directories..."
run_ssh "ls -la /var/www/ 2>/dev/null || echo '/var/www/ not found'"
run_ssh "ls -la /srv/ 2>/dev/null || echo '/srv/ not found'"

echo -e "\n6. Checking nginx configuration..."
run_ssh "ls -la /etc/nginx/sites-available/ 2>/dev/null || echo 'Nginx config not found'"
run_ssh "[ -f /etc/nginx/sites-enabled/default ] && cat /etc/nginx/sites-enabled/default | grep -i root || echo 'No root directory found in default nginx config'"
