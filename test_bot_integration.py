#!/usr/bin/env python3
"""
Простой тест интеграции Telegram бота
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Тестирует импорты всех необходимых модулей"""
    print("=== Тест импортов ===")
    
    try:
        from app.services.payment_service import PaymentService
        print("✅ PaymentService импортирован")
    except Exception as e:
        print(f"❌ Ошибка импорта PaymentService: {e}")
        return False
    
    try:
        from app.services.bot_handler import TelegramBotHandler
        print("✅ TelegramBotHandler импортирован")
    except Exception as e:
        print(f"❌ Ошибка импорта TelegramBotHandler: {e}")
        return False
    
    try:
        from app.services.notification_service import NotificationService
        print("✅ NotificationService импортирован")
    except Exception as e:
        print(f"❌ Ошибка импорта NotificationService: {e}")
        return False
    
    try:
        from app.models.database import DatabaseService
        print("✅ DatabaseService импортирован")
    except Exception as e:
        print(f"❌ Ошибка импорта DatabaseService: {e}")
        return False
    
    try:
        from config import Config
        print("✅ Config импортирован")
    except Exception as e:
        print(f"❌ Ошибка импорта Config: {e}")
        return False
    
    return True

def test_services_initialization():
    """Тестирует инициализацию сервисов"""
    print("\n=== Тест инициализации сервисов ===")
    
    try:
        from app.services.payment_service import PaymentService
        from app.models.database import DatabaseService
        
        # Тестируем DatabaseService
        db_service = DatabaseService()
        print("✅ DatabaseService инициализирован")
        
        # Тестируем PaymentService
        payment_service = PaymentService()
        print("✅ PaymentService инициализирован")
        
        # Проверяем доступные методы оплаты
        payment_methods = payment_service.get_available_payment_methods()
        print(f"✅ Доступные способы оплаты: {list(payment_methods.keys())}")
        
        # Проверяем тарифные планы
        plans = payment_service.get_subscription_plans()
        print(f"✅ Доступные планы: {list(plans.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка инициализации сервисов: {e}")
        return False

def test_database_operations():
    """Тестирует основные операции с базой данных"""
    print("\n=== Тест операций с базой данных ===")
    
    try:
        from app.models.database import DatabaseService
        from decimal import Decimal
        
        db_service = DatabaseService()
        
        # Тестируем создание пользователя
        test_telegram_id = 999888777
        
        existing_user = db_service.get_user_by_telegram_id(test_telegram_id)
        if existing_user:
            print(f"✅ Пользователь уже существует: ID {existing_user.id}")
            user = existing_user
        else:
            user = db_service.create_user(
                telegram_id=test_telegram_id,
                username='test_bot_user',
                first_name='Test',
                last_name='Bot'
            )
            print(f"✅ Пользователь создан: ID {user.id}")
        
        # Тестируем создание платежа
        test_order_id = f"test_bot_{int(datetime.now().timestamp())}"
        
        payment = db_service.create_payment(
            user_id=user.id,
            lava_invoice_id=test_order_id,
            amount=Decimal('299.00'),
            payment_method='card_ru',
            payment_url='https://example.com/pay',
            expires_at=datetime.now()
        )
        print(f"✅ Платеж создан: ID {payment.id}")
        
        # Тестируем создание подписки
        subscription = db_service.create_subscription(
            user_id=user.id,
            plan_type='monthly',
            months=1
        )
        print(f"✅ Подписка создана: ID {subscription.id}")
        
        # Проверяем активную подписку
        active_subscription = db_service.get_user_active_subscription(user.id)
        if active_subscription:
            print(f"✅ Активная подписка найдена: до {active_subscription.end_date}")
            print(f"   Активна: {active_subscription.is_active()}")
            print(f"   Дней осталось: {active_subscription.days_until_expiry()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка операций с БД: {e}")
        return False

def test_payment_creation():
    """Тестирует создание платежа через PaymentService"""
    print("\n=== Тест создания платежа ===")
    
    try:
        from app.services.payment_service import PaymentService
        
        payment_service = PaymentService()
        
        # Тестируем создание счета
        test_user_id = 999888777
        test_plan_months = 1
        test_payment_method = 'card_ru'
        
        print(f"🔄 Создаем счет для пользователя {test_user_id}")
        print(f"   План: {test_plan_months} месяц")
        print(f"   Способ оплаты: {test_payment_method}")
        
        invoice_result = payment_service.create_invoice(test_user_id, test_plan_months, test_payment_method)
        
        if invoice_result['success']:
            print(f"✅ Счет создан успешно:")
            print(f"   Order ID: {invoice_result['order_id']}")
            print(f"   Invoice ID: {invoice_result['invoice_id']}")
            print(f"   Сумма: {invoice_result['amount']} {invoice_result['currency']}")
            print(f"   URL: {invoice_result['payment_url'][:50]}...")
            return True
        else:
            print(f"❌ Ошибка создания счета: {invoice_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Исключение при создании платежа: {e}")
        return False

def test_notification_methods():
    """Тестирует методы уведомлений (без реальной отправки)"""
    print("\n=== Тест методов уведомлений ===")
    
    try:
        # Создаем mock класс для тестирования методов
        class MockTelegramBotHandler:
            def __init__(self):
                self.notifications = []
            
            def send_payment_success_notification(self, user_id, order_id, amount, plan_months):
                self.notifications.append(f"success_{user_id}_{order_id}")
                print(f"✅ Mock: Уведомление об успешной оплате для {user_id}")
                return True
            
            def send_payment_failed_notification(self, user_id, order_id, error_message=None):
                self.notifications.append(f"failed_{user_id}_{order_id}")
                print(f"✅ Mock: Уведомление о неудачной оплате для {user_id}")
                return True
            
            def send_invite_link_notification(self, user_id, invite_url, subscription_end_date):
                self.notifications.append(f"invite_{user_id}")
                print(f"✅ Mock: Пригласительная ссылка для {user_id}")
                return True
        
        from app.services.notification_service import NotificationService
        from app.models.database import DatabaseService
        
        db_service = DatabaseService()
        mock_bot = MockTelegramBotHandler()
        notification_service = NotificationService(mock_bot, db_service)
        
        print("✅ NotificationService с mock bot инициализирован")
        
        # Тестируем методы уведомлений
        test_user_id = 999888777
        
        # Тест успешной оплаты
        success = mock_bot.send_payment_success_notification(test_user_id, "test_order_123", 299.0, 1)
        if success:
            print("✅ Метод уведомления об успешной оплате работает")
        
        # Тест неудачной оплаты
        success = mock_bot.send_payment_failed_notification(test_user_id, "test_order_456", "Test error")
        if success:
            print("✅ Метод уведомления о неудачной оплате работает")
        
        # Тест пригласительной ссылки
        success = mock_bot.send_invite_link_notification(test_user_id, "https://t.me/+test", datetime.now())
        if success:
            print("✅ Метод отправки пригласительной ссылки работает")
        
        print(f"✅ Всего mock уведомлений: {len(mock_bot.notifications)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка тестирования уведомлений: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🚀 Запуск тестов интеграции Telegram бота")
    print("=" * 60)
    
    tests = [
        ("Импорты", test_imports),
        ("Инициализация сервисов", test_services_initialization),
        ("Операции с БД", test_database_operations),
        ("Создание платежа", test_payment_creation),
        ("Методы уведомлений", test_notification_methods)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Критическая ошибка в тесте '{test_name}': {e}")
            results.append((test_name, False))
    
    # Итоговые результаты
    print(f"\n{'='*60}")
    print("📊 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"{'='*60}")
    print(f"Пройдено тестов: {passed}/{total}")
    
    if passed == total:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        print("💡 Telegram бот готов к использованию")
        
        print(f"\n📋 Что готово:")
        print(f"   • PaymentService - создание счетов через Lava.top API ✅")
        print(f"   • DatabaseService - работа с пользователями и подписками ✅")
        print(f"   • NotificationService - система уведомлений ✅")
        print(f"   • Интеграция с базой данных ✅")
        print(f"   • Создание и обработка платежей ✅")
        
        print(f"\n🚀 Следующие шаги:")
        print(f"   1. Запустить Flask приложение: python app.py")
        print(f"   2. Протестировать реальные платежи")
        print(f"   3. Настроить webhook URL в Lava.top")
        print(f"   4. Протестировать Telegram бота")
        
    else:
        print("❌ НЕКОТОРЫЕ ТЕСТЫ НЕ ПРОШЛИ")
        print("💡 Исправьте ошибки перед запуском бота")
    
    print(f"{'='*60}")

if __name__ == '__main__':
    main()