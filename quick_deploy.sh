#!/bin/bash

# Быстрый скрипт развертывания для копирования в терминал
# Выполнять команды по одной или блоками

echo "=== БЫСТРОЕ РАЗВЕРТЫВАНИЕ TELEGRAM PAYMENT BOT ==="

# БЛОК 1: Обновление системы и установка пакетов
echo "БЛОК 1: Обновление системы..."
sudo apt update && sudo apt upgrade -y
sudo apt install -y python3 python3-pip python3-venv nginx git curl wget htop nano

# БЛОК 2: Создание пользователя и директорий
echo "БЛОК 2: Создание пользователя..."
sudo useradd -m -s /bin/bash telegrambot || echo "Пользователь уже существует"
sudo mkdir -p /home/<USER>/app
sudo mkdir -p /home/<USER>/backups
sudo mkdir -p /var/log/telegram-payment-bot
sudo chown -R telegrambot:telegrambot /home/<USER>
sudo chown -R telegrambot:telegrambot /var/log/telegram-payment-bot

# БЛОК 3: Распаковка проекта (если архив загружен)
echo "БЛОК 3: Распаковка проекта..."
if [ -f "/tmp/telegram_bot_deploy.tar.gz" ]; then
    sudo tar -xzf /tmp/telegram_bot_deploy.tar.gz -C /home/<USER>/app/
    sudo chown -R telegrambot:telegrambot /home/<USER>/app/
    echo "Проект распакован из архива"
else
    echo "Архив не найден. Загрузите файлы проекта в /home/<USER>/app/"
    echo "Нажмите Enter когда файлы будут загружены..."
    read
fi

# БЛОК 4: Настройка Python окружения
echo "БЛОК 4: Настройка Python..."
sudo -u telegrambot python3 -m venv /home/<USER>/venv
sudo -u telegrambot /home/<USER>/venv/bin/pip install --upgrade pip
sudo -u telegrambot /home/<USER>/venv/bin/pip install -r /home/<USER>/app/requirements.txt

# БЛОК 5: Настройка конфигурации
echo "БЛОК 5: Настройка конфигурации..."
cd /home/<USER>/app
sudo -u telegrambot cp .env.production .env
sudo -u telegrambot sed -i 's|WEBHOOK_URL=.*|WEBHOOK_URL=http://**************/webhook|g' .env

# БЛОК 6: Создание systemd сервиса
echo "БЛОК 6: Создание systemd сервиса..."
sudo tee /etc/systemd/system/telegram-payment-bot.service > /dev/null << 'EOF'
[Unit]
Description=Telegram Payment Bot
After=network.target

[Service]
Type=exec
User=telegrambot
Group=telegrambot
WorkingDirectory=/home/<USER>/app
Environment=PATH=/home/<USER>/venv/bin
Environment=FLASK_ENV=production
ExecStart=/home/<USER>/venv/bin/gunicorn --config gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=telegram-payment-bot

[Install]
WantedBy=multi-user.target
EOF

# БЛОК 7: Настройка Nginx
echo "БЛОК 7: Настройка Nginx..."
sudo tee /etc/nginx/sites-available/telegram-payment-bot > /dev/null << 'EOF'
server {
    listen 80;
    server_name _;

    client_max_body_size 10M;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /webhook {
        proxy_pass http://127.0.0.1:5000/webhook;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        access_log off;
    }

    access_log /var/log/nginx/telegram-payment-bot.access.log;
    error_log /var/log/nginx/telegram-payment-bot.error.log;
}
EOF

sudo ln -sf /etc/nginx/sites-available/telegram-payment-bot /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# БЛОК 8: Запуск сервисов
echo "БЛОК 8: Запуск сервисов..."
sudo systemctl daemon-reload
sudo systemctl enable telegram-payment-bot
sudo systemctl enable nginx
sudo nginx -t
sudo systemctl restart nginx
sudo systemctl start telegram-payment-bot

# БЛОК 9: Проверка
echo "БЛОК 9: Проверка работы..."
sleep 5
echo "=== Статус сервисов ==="
sudo systemctl status telegram-payment-bot --no-pager
sudo systemctl status nginx --no-pager

echo ""
echo "=== Проверка портов ==="
sudo netstat -tlnp | grep -E ':80|:5000'

echo ""
echo "=== Тест health check ==="
curl -s http://localhost/health || echo "Health check недоступен"

echo ""
echo "=== РАЗВЕРТЫВАНИЕ ЗАВЕРШЕНО ==="
echo "Веб-интерфейс: http://**************"
echo "Webhook URL: http://**************/webhook"
echo "Health check: http://**************/health"
echo ""
echo "Полезные команды:"
echo "sudo systemctl status telegram-payment-bot"
echo "sudo journalctl -u telegram-payment-bot -f"
echo "sudo systemctl restart telegram-payment-bot"
echo "curl http://**************/health"