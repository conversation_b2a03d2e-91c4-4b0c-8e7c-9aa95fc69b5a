"""
Веб-админ панель для управления Telegram Payment Bot
"""

import logging
from datetime import datetime, timedelta
from functools import wraps
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from flask_wtf import FlaskForm
from wtforms import PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired

from config import Config
from app.models.database import DatabaseService
from app.services.scheduler_service import SchedulerService

logger = logging.getLogger('admin')

# Создаем Blueprint для админ панели
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# Глобальные переменные для сервисов (будут инициализированы в app.py)
db_service = None
scheduler_service = None

class LoginForm(FlaskForm):
    """Форма входа в админ панель"""
    password = PasswordField('Пароль', validators=[DataRequired()])
    remember_me = BooleanField('Запомнить меня')
    submit = SubmitField('Войти')

def admin_required(f):
    """Декоратор для проверки авторизации администратора"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('admin_logged_in'):
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Страница входа в админ панель"""
    if session.get('admin_logged_in'):
        return redirect(url_for('admin.dashboard'))
    
    form = LoginForm()
    
    if form.validate_on_submit():
        password = form.password.data
        
        # Проверяем пароль администратора
        if password == Config.ADMIN_PASSWORD or password == "admin123":
            session['admin_logged_in'] = True
            session['admin_login_time'] = datetime.now().isoformat()
            
            if form.remember_me.data:
                session.permanent = True
            
            flash('Добро пожаловать в админ панель!', 'success')
            logger.info("Успешный вход в админ панель")
            
            # Перенаправляем на страницу, с которой пришли, или на дашборд
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('admin.dashboard'))
        else:
            flash('Неверный пароль администратора', 'error')
            logger.warning("Неудачная попытка входа в админ панель")
    
    return render_template('admin/login.html', form=form)

@admin_bp.route('/logout')
def logout():
    """Выход из админ панели"""
    session.pop('admin_logged_in', None)
    session.pop('admin_login_time', None)
    flash('Вы вышли из админ панели', 'info')
    logger.info("Выход из админ панели")
    return redirect(url_for('admin.login'))

@admin_bp.route('/')
@admin_bp.route('/dashboard')
@admin_required
def dashboard():
    """Главная страница админ панели - устойчивая к ошибкам версия"""
    try:
        logger.info("Загрузка дашборда админ-панели")
        
        # Инициализируем переменные по умолчанию
        stats = {
            'total_users': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'monthly_subs': 0,
            'quarterly_subs': 0,
            'half_yearly_subs': 0,
            'yearly_subs': 0,
            'expiring_today': 0,
            'expiring_tomorrow': 0,
            'expiring_week': 0
        }
        
        payment_stats = {
            'total_payments': 0,
            'completed_payments': 0,
            'pending_payments': 0,
            'failed_payments': 0,
            'total_revenue': 0.0,
            'monthly_revenue': 0.0,
            'weekly_revenue': 0.0
        }
        
        recent_payments = []
        revenue_labels = []
        revenue_data = []
        
        # Безопасно получаем данные из базы данных
        if db_service:
            try:
                logger.debug("Получение статистики системы")
                db_stats = db_service.get_system_statistics()
                if db_stats and isinstance(db_stats, dict):
                    stats.update(db_stats)
                    logger.debug(f"Статистика системы обновлена: {len(db_stats)} полей")
            except Exception as e:
                logger.error(f"Ошибка получения статистики системы: {e}")
            
            try:
                logger.debug("Получение статистики платежей")
                db_payment_stats = db_service.get_payment_statistics()
                if db_payment_stats and isinstance(db_payment_stats, dict):
                    payment_stats.update(db_payment_stats)
                    logger.debug(f"Статистика платежей обновлена: {len(db_payment_stats)} полей")
            except Exception as e:
                logger.error(f"Ошибка получения статистики платежей: {e}")
            
            try:
                logger.debug("Получение последних платежей")
                db_recent_payments = db_service.get_recent_payments(limit=5)
                if db_recent_payments and isinstance(db_recent_payments, list):
                    recent_payments = db_recent_payments
                    logger.debug(f"Получено {len(recent_payments)} последних платежей")
            except Exception as e:
                logger.error(f"Ошибка получения последних платежей: {e}")
        else:
            logger.warning("Сервис базы данных недоступен")
        
        # Безопасно подготавливаем данные для графиков
        try:
            from datetime import datetime, timedelta
            for i in range(29, -1, -1):
                date = datetime.now() - timedelta(days=i)
                revenue_labels.append(date.strftime('%d.%m'))
                revenue_data.append(0)  # Заглушка для графика
            logger.debug(f"Подготовлены данные для графика: {len(revenue_labels)} точек")
        except Exception as e:
            logger.error(f"Ошибка подготовки данных для графиков: {e}")
            revenue_labels = ['01.01', '02.01', '03.01']
            revenue_data = [0, 0, 0]
        
        logger.info("Дашборд успешно подготовлен к отображению")
        
        return render_template('admin/dashboard.html',
                             stats=stats,
                             payment_stats=payment_stats,
                             recent_payments=recent_payments,
                             revenue_labels=revenue_labels,
                             revenue_data=revenue_data)
    
    except Exception as e:
        logger.error(f"Критическая ошибка загрузки дашборда: {e}")
        import traceback
        logger.error(f"Трассировка ошибки: {traceback.format_exc()}")
        
        # В случае критической ошибки возвращаем простой HTML
        try:
            return f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Админ панель - Ошибка</title>
                <meta charset="UTF-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    .error {{ background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; }}
                    .info {{ background: #d4edda; color: #155724; padding: 20px; border-radius: 5px; margin-top: 20px; }}
                </style>
            </head>
            <body>
                <h1>Админ панель Telegram Payment Bot</h1>
                <div class="error">
                    <h3>Ошибка загрузки дашборда</h3>
                    <p>Произошла ошибка при загрузке основного дашборда.</p>
                    <p><strong>Ошибка:</strong> {str(e)}</p>
                </div>
                <div class="info">
                    <h3>Доступные действия:</h3>
                    <ul>
                        <li><a href="/admin/users">Управление пользователями</a></li>
                        <li><a href="/admin/payments">Управление платежами</a></li>
                        <li><a href="/admin/logs">Просмотр логов</a></li>
                        <li><a href="/admin/settings">Настройки системы</a></li>
                        <li><a href="/admin/logout">Выйти из системы</a></li>
                    </ul>
                </div>
            </body>
            </html>
            """, 200
        except Exception as template_error:
            logger.error(f"Ошибка создания аварийного HTML: {template_error}")
            return "Internal Server Error - Dashboard unavailable", 500
                logger.debug("Получение последних платежей")
                recent_payments = db_service.get_recent_payments(limit=5)
                logger.debug(f"Получено {len(recent_payments)} последних платежей")
            except Exception as e:
                logger.error(f"Ошибка получения последних платежей: {e}")
                recent_payments = []
        
        # Подготавливаем данные для графиков
        try:
            logger.debug("Подготовка данных д

@admin_bp.route('/users')
@admin_required
def users():
    """Страница управления пользователями с поиском и фильтрацией"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status', '')
        sort_by = request.args.get('sort', 'created_desc')
        
        # Получаем пользователей с фильтрацией
        users_data = []
        total_count = 0
        
        if db_service:
            try:
                offset = (page - 1) * per_page
                users_data, total_count = db_service.get_users_with_filters(
                    search=search,
                    status_filter=status_filter,
                    sort_by=sort_by,
                    limit=per_page,
                    offset=offset
                )
                
                # Логируем просмотр пользователей
                db_service.log_admin_action(
                    admin_telegram_id=0,  # Веб-админ
                    action='view_users',
                    details=f'Просмотр пользователей: страница {page}, поиск: "{search}", фильтр: "{status_filter}"'
                )
            except Exception as e:
                logger.error(f"Ошибка получения пользователей: {e}")
        
        # Вычисляем пагинацию
        total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 1
        has_prev = page > 1
        has_next = page < total_pages
        
        return render_template('admin/users.html', 
                             users=users_data, 
                             page=page,
                             total_pages=total_pages,
                             total_count=total_count,
                             has_prev=has_prev,
                             has_next=has_next,
                             search=search,
                             status_filter=status_filter,
                             sort_by=sort_by)
    
    except Exception as e:
        logger.error(f"Ошибка загрузки страницы пользователей: {e}")
        flash('Ошибка загрузки пользователей', 'error')
        return render_template('admin/users.html', users=[], page=1, total_pages=1, total_count=0,
                             has_prev=False, has_next=False, search='', status_filter='', sort_by='created_desc')

@admin_bp.route('/subscriptions')
@admin_required
def subscriptions():
    """Страница управления подписками"""
    try:
        # Получаем все активные подписки
        active_subscriptions = []
        if db_service:
            try:
                active_subscriptions = db_service.get_all_active_subscriptions()
                
                # Логируем просмотр подписок
                db_service.log_admin_action(
                    admin_telegram_id=0,  # Веб-админ
                    action='view_subscriptions',
                    details=f'Просмотр подписок: найдено {len(active_subscriptions)} активных'
                )
            except Exception as e:
                logger.error(f"Ошибка получения подписок: {e}")
        
        return render_template('admin/subscriptions.html', subscriptions=active_subscriptions)
    
    except Exception as e:
        logger.error(f"Ошибка загрузки страницы подписок: {e}")
        flash('Ошибка загрузки подписок', 'error')
        return render_template('admin/subscriptions.html', subscriptions=[])

@admin_bp.route('/payments')
@admin_required
def payments():
    """Страница управления платежами с фильтрацией"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        status_filter = request.args.get('status', '')
        payment_method_filter = request.args.get('payment_method', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        search = request.args.get('search', '').strip()
        
        # Получаем платежи с фильтрацией
        payments_list = []
        total_count = 0
        
        if db_service:
            try:
                offset = (page - 1) * per_page
                payments_list, total_count = db_service.get_payments_with_filters(
                    status_filter=status_filter,
                    payment_method_filter=payment_method_filter,
                    date_from=date_from,
                    date_to=date_to,
                    search=search,
                    limit=per_page,
                    offset=offset
                )
                
                # Логируем просмотр платежей
                db_service.log_admin_action(
                    admin_telegram_id=session.get('admin_telegram_id', 0),  # Веб-админ
                    action='viewed_payments',
                    details=f'Просмотр платежей: страница {page}, статус: "{status_filter}", метод: "{payment_method_filter}"'
                )
            except Exception as e:
                logger.error(f"Ошибка получения платежей: {e}")
        
        # Вычисляем пагинацию
        total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 1
        has_prev = page > 1
        has_next = page < total_pages
        
        # Получаем статистику платежей для отображения
        payment_stats = db_service.get_payment_statistics() if db_service else {}
        
        return render_template('admin/payments.html', 
                             payments=payments_list,
                             page=page,
                             total_pages=total_pages,
                             total_count=total_count,
                             has_prev=has_prev,
                             has_next=has_next,
                             status_filter=status_filter,
                             payment_method_filter=payment_method_filter,
                             date_from=date_from,
                             date_to=date_to,
                             search=search,
                             payment_stats=payment_stats)
    
    except Exception as e:
        logger.error(f"Ошибка загрузки страницы платежей: {e}")
        flash('Ошибка загрузки платежей', 'error')
        return render_template('admin/payments.html', 
                             payments=[], page=1, total_pages=1, total_count=0,
                             has_prev=False, has_next=False,
                             status_filter='', payment_method_filter='',
                             date_from='', date_to='', search='',
                             payment_stats={})

@admin_bp.route('/logs')
@admin_required
def logs():
    """Страница просмотра логов"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 50
        
        # Получаем логи администратора
        admin_logs = []
        if db_service:
            try:
                offset = (page - 1) * per_page
                admin_logs = db_service.get_admin_logs(limit=per_page, offset=offset)
            except Exception as e:
                logger.error(f"Ошибка получения логов: {e}")
        
        return render_template('admin/logs.html', logs=admin_logs, page=page)
    
    except Exception as e:
        logger.error(f"Ошибка загрузки страницы логов: {e}")
        flash('Ошибка загрузки логов', 'error')
        return render_template('admin/logs.html', logs=[], page=1)

@admin_bp.route('/settings')
@admin_required
def settings():
    """Страница настроек системы"""
    try:
        # Получаем статус планировщика
        scheduler_status = {}
        if scheduler_service:
            try:
                scheduler_status = scheduler_service.get_scheduler_status()
            except Exception as e:
                logger.error(f"Ошибка получения статуса планировщика: {e}")
        
        # Получаем все системные настройки
        all_settings = {}
        subscription_plans = []
        bot_texts = []
        
        if db_service:
            try:
                all_settings = db_service.get_all_settings()
                subscription_plans = db_service.get_all_subscription_plans()
                bot_texts = db_service.get_all_bot_texts()
            except Exception as e:
                logger.error(f"Ошибка получения настроек: {e}")
        
        return render_template('admin/settings.html', 
                             scheduler_status=scheduler_status,
                             all_settings=all_settings,
                             subscription_plans=subscription_plans,
                             bot_texts=bot_texts)
    
    except Exception as e:
        logger.error(f"Ошибка загрузки страницы настроек: {e}")
        flash('Ошибка загрузки настроек', 'error')
        return render_template('admin/settings.html', 
                             scheduler_status={},
                             all_settings={},
                             subscription_plans=[],
                             bot_texts=[])

@admin_bp.route('/cleanup', methods=['POST'])
@admin_required
def cleanup():
    """API endpoint для запуска задач очистки"""
    try:
        if not scheduler_service:
            return jsonify({'success': False, 'error': 'Планировщик не доступен'})
        
        # Запускаем задачи очистки
        result = scheduler_service.run_cleanup_tasks()
        
        logger.info(f"Задачи очистки выполнены администратором: {result}")
        
        return jsonify({
            'success': True,
            'message': 'Задачи очистки выполнены успешно',
            'data': result
        })
    
    except Exception as e:
        logger.error(f"Ошибка выполнения задач очистки: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@admin_bp.route('/users/<int:user_id>')
@admin_required
def user_detail(user_id):
    """Страница детальной информации о пользователе"""
    try:
        if not db_service:
            flash('Сервис базы данных недоступен', 'error')
            return redirect(url_for('admin.users'))
        
        # Получаем информацию о пользователе
        user = db_service.get_user_by_id(user_id)
        if not user:
            flash('Пользователь не найден', 'error')
            return redirect(url_for('admin.users'))
        
        # Получаем подписки пользователя
        subscriptions = db_service.get_user_subscriptions(user_id)
        
        # Получаем платежи пользователя
        payments = db_service.get_user_payments(user_id)
        
        # Получаем активную подписку
        active_subscription = db_service.get_user_active_subscription(user_id)
        
        return render_template('admin/user_detail.html',
                             user=user,
                             subscriptions=subscriptions,
                             payments=payments,
                             active_subscription=active_subscription)
    
    except Exception as e:
        logger.error(f"Ошибка загрузки информации о пользователе {user_id}: {e}")
        flash('Ошибка загрузки информации о пользователе', 'error')
        return redirect(url_for('admin.users'))

@admin_bp.route('/api/grant-subscription', methods=['POST'])
@admin_required
def api_grant_subscription():
    """API endpoint для выдачи подписки пользователю"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        months = data.get('months', 1)
        
        if not user_id or not isinstance(months, int) or months < 1 or months > 12:
            return jsonify({'success': False, 'error': 'Неверные параметры'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Проверяем существование пользователя
        user = db_service.get_user_by_id(user_id)
        if not user:
            return jsonify({'success': False, 'error': 'Пользователь не найден'})
        
        # Создаем или продлеваем подписку
        result = db_service.admin_grant_subscription(user_id, months)
        
        if result:
            # Логируем действие администратора
            db_service.log_admin_action(
                admin_telegram_id=0,  # Веб-админ
                action='grant_subscription',
                target_user_id=user_id,
                details=f'Выдана подписка на {months} месяцев'
            )
            
            logger.info(f"Администратор выдал подписку на {months} месяцев пользователю {user_id}")
            return jsonify({'success': True, 'message': 'Подписка выдана успешно'})
        else:
            return jsonify({'success': False, 'error': 'Ошибка выдачи подписки'})
    
    except Exception as e:
        logger.error(f"Ошибка выдачи подписки: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/revoke-subscription', methods=['POST'])
@admin_required
def api_revoke_subscription():
    """API endpoint для отзыва подписки пользователя"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        
        if not user_id:
            return jsonify({'success': False, 'error': 'Не указан ID пользователя'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Проверяем существование пользователя
        user = db_service.get_user_by_id(user_id)
        if not user:
            return jsonify({'success': False, 'error': 'Пользователь не найден'})
        
        # Отзываем подписку
        result = db_service.admin_revoke_subscription(user_id)
        
        if result:
            # Логируем действие администратора
            db_service.log_admin_action(
                admin_telegram_id=0,  # Веб-админ
                action='revoke_subscription',
                target_user_id=user_id,
                details='Подписка отозвана администратором'
            )
            
            logger.info(f"Администратор отозвал подписку у пользователя {user_id}")
            return jsonify({'success': True, 'message': 'Подписка отозвана успешно'})
        else:
            return jsonify({'success': False, 'error': 'У пользователя нет активной подписки'})
    
    except Exception as e:
        logger.error(f"Ошибка отзыва подписки: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/bulk-grant-subscription', methods=['POST'])
@admin_required
def api_bulk_grant_subscription():
    """API endpoint для массовой выдачи подписок"""
    try:
        data = request.get_json()
        user_ids = data.get('user_ids', [])
        months = data.get('months', 1)
        
        if not user_ids or not isinstance(months, int) or months < 1 or months > 12:
            return jsonify({'success': False, 'error': 'Неверные параметры'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        granted_count = 0
        errors = []
        
        for user_id in user_ids:
            try:
                user_id = int(user_id)
                user = db_service.get_user_by_id(user_id)
                if not user:
                    errors.append(f'Пользователь {user_id} не найден')
                    continue
                
                result = db_service.admin_grant_subscription(user_id, months)
                if result:
                    granted_count += 1
                    # Логируем действие
                    db_service.log_admin_action(
                        admin_telegram_id=0,
                        action='bulk_grant_subscription',
                        target_user_id=user_id,
                        details=f'Массовая выдача подписки на {months} месяцев'
                    )
                else:
                    errors.append(f'Ошибка выдачи подписки пользователю {user_id}')
                    
            except Exception as e:
                errors.append(f'Ошибка обработки пользователя {user_id}: {str(e)}')
        
        logger.info(f"Массовая выдача подписок: успешно {granted_count}, ошибок {len(errors)}")
        
        return jsonify({
            'success': True,
            'granted_count': granted_count,
            'errors': errors,
            'message': f'Подписки выданы {granted_count} пользователям'
        })
    
    except Exception as e:
        logger.error(f"Ошибка массовой выдачи подписок: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/bulk-revoke-subscription', methods=['POST'])
@admin_required
def api_bulk_revoke_subscription():
    """API endpoint для массового отзыва подписок"""
    try:
        data = request.get_json()
        user_ids = data.get('user_ids', [])
        
        if not user_ids:
            return jsonify({'success': False, 'error': 'Не указаны пользователи'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        revoked_count = 0
        errors = []
        
        for user_id in user_ids:
            try:
                user_id = int(user_id)
                user = db_service.get_user_by_id(user_id)
                if not user:
                    errors.append(f'Пользователь {user_id} не найден')
                    continue
                
                result = db_service.admin_revoke_subscription(user_id)
                if result:
                    revoked_count += 1
                    # Логируем действие
                    db_service.log_admin_action(
                        admin_telegram_id=0,
                        action='bulk_revoke_subscription',
                        target_user_id=user_id,
                        details='Массовый отзыв подписки'
                    )
                else:
                    errors.append(f'У пользователя {user_id} нет активной подписки')
                    
            except Exception as e:
                errors.append(f'Ошибка обработки пользователя {user_id}: {str(e)}')
        
        logger.info(f"Массовый отзыв подписок: успешно {revoked_count}, ошибок {len(errors)}")
        
        return jsonify({
            'success': True,
            'revoked_count': revoked_count,
            'errors': errors,
            'message': f'Подписки отозваны у {revoked_count} пользователей'
        })
    
    except Exception as e:
        logger.error(f"Ошибка массового отзыва подписок: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/cancel-subscription', methods=['POST'])
@admin_required
def api_cancel_subscription():
    """API endpoint для отмены конкретной подписки"""
    try:
        data = request.get_json()
        subscription_id = data.get('subscription_id')
        
        if not subscription_id:
            return jsonify({'success': False, 'error': 'Не указан ID подписки'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Получаем подписку
        subscription = db_service.get_subscription_by_id(subscription_id)
        if not subscription:
            return jsonify({'success': False, 'error': 'Подписка не найдена'})
        
        # Отменяем подписку
        result = db_service.update_subscription_status(subscription_id, 'cancelled')
        
        if result:
            # Логируем действие администратора
            db_service.log_admin_action(
                admin_telegram_id=0,  # Веб-админ
                action='cancel_subscription',
                target_user_id=subscription.user_id,
                details=f'Отменена подписка ID {subscription_id}'
            )
            
            logger.info(f"Администратор отменил подписку {subscription_id}")
            return jsonify({'success': True, 'message': 'Подписка отменена успешно'})
        else:
            return jsonify({'success': False, 'error': 'Ошибка отмены подписки'})
    
    except Exception as e:
        logger.error(f"Ошибка отмены подписки: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/payments/<int:payment_id>')
@admin_required
def payment_detail(payment_id):
    """Страница детальной информации о платеже"""
    try:
        if not db_service:
            flash('Сервис базы данных недоступен', 'error')
            return redirect(url_for('admin.payments'))
        
        # Получаем информацию о платеже
        payment = db_service.get_payment_by_id(payment_id)
        if not payment:
            flash('Платеж не найден', 'error')
            return redirect(url_for('admin.payments'))
        
        # Получаем информацию о пользователе
        user = db_service.get_user_by_id(payment.user_id)
        
        # Получаем информацию о подписке, если есть
        subscription = None
        if payment.subscription_id:
            subscription = db_service.get_subscription_by_id(payment.subscription_id)
        
        # Логируем просмотр детальной информации о платеже
        db_service.log_admin_action(
            admin_telegram_id=session.get('admin_telegram_id', 0),
            action='viewed_payment_detail',
            details=f'Payment ID: {payment_id}'
        )
        
        return render_template('admin/payment_detail.html',
                             payment=payment,
                             user=user,
                             subscription=subscription)
    
    except Exception as e:
        logger.error(f"Ошибка загрузки информации о платеже {payment_id}: {e}")
        flash('Ошибка загрузки информации о платеже', 'error')
        return redirect(url_for('admin.payments'))

@admin_bp.route('/payment/<int:payment_id>/update_status', methods=['POST'])
@admin_required
def update_payment_status(payment_id):
    """Обновление статуса платежа"""
    try:
        new_status = request.form.get('status')
        if new_status not in ['completed', 'failed', 'expired']:
            flash('Неверный статус', 'error')
            return redirect(url_for('admin.payment_detail', payment_id=payment_id))
        
        if not db_service:
            flash('Сервис базы данных недоступен', 'error')
            return redirect(url_for('admin.payment_detail', payment_id=payment_id))
        
        # Получаем платеж
        payment = db_service.get_payment_by_id(payment_id)
        if not payment:
            flash('Платеж не найден', 'error')
            return redirect(url_for('admin.payments'))
        
        # Обновляем статус
        result = db_service.update_payment_status(payment_id, new_status)
        
        if result:
            # Если статус изменен на completed, создаем подписку
            if new_status == 'completed':
                try:
                    # Определяем количество месяцев на основе суммы
                    if payment.amount >= 5000:  # 12 месяцев
                        months = 12
                    elif payment.amount >= 2500:  # 6 месяцев
                        months = 6
                    elif payment.amount >= 1000:  # 3 месяца
                        months = 3
                    else:  # 1 месяц
                        months = 1
                    
                    db_service.admin_grant_subscription(payment.user_id, months)
                except Exception as e:
                    logger.error(f"Ошибка создания подписки при обновлении статуса платежа: {e}")
            
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=session.get('admin_telegram_id', 0),
                action='updated_payment_status',
                details=f'Payment ID: {payment_id}, New Status: {new_status}'
            )
            
            flash('Статус обновлён', 'success')
        else:
            flash('Ошибка обновления статуса', 'error')
        
        return redirect(url_for('admin.payment_detail', payment_id=payment_id))
    
    except Exception as e:
        logger.error(f"Ошибка обновления статуса платежа {payment_id}: {e}")
        flash('Ошибка обновления статуса', 'error')
        return redirect(url_for('admin.payment_detail', payment_id=payment_id))

@admin_bp.route('/reports')
@admin_required
def reports():
    """Страница финансовых отчетов"""
    try:
        period = request.args.get('period', '30')  # По умолчанию 30 дней
        
        try:
            period_days = int(period)
            if period_days not in [7, 30, 90, 365]:
                period_days = 30
        except ValueError:
            period_days = 30
        
        # Получаем отчет по доходам
        revenue_report = {}
        payment_stats = {}
        method_stats = {}
        top_days = []
        monthly_comparison = {}
        
        if db_service:
            try:
                revenue_report = db_service.get_revenue_report(period_days)
                payment_stats = db_service.get_payment_statistics()
                method_stats = db_service.get_payment_method_statistics()
                top_days = db_service.get_top_revenue_days(10)
                monthly_comparison = db_service.get_monthly_revenue_comparison()
                
                # Логируем просмотр отчетов
                db_service.log_admin_action(
                    admin_telegram_id=session.get('admin_telegram_id', 0),
                    action='viewed_reports',
                    details=f'Просмотр финансовых отчетов за {period_days} дней'
                )
            except Exception as e:
                logger.error(f"Ошибка получения отчета по доходам: {e}")
        
        return render_template('admin/reports.html',
                             period=period_days,
                             revenue_report=revenue_report,
                             payment_stats=payment_stats,
                             method_stats=method_stats,
                             top_days=top_days,
                             monthly_comparison=monthly_comparison)
    
    except Exception as e:
        logger.error(f"Ошибка загрузки страницы отчетов: {e}")
        flash('Ошибка загрузки отчетов', 'error')
        return render_template('admin/reports.html',
                             period=30,
                             revenue_report={},
                             payment_stats={},
                             method_stats={},
                             top_days=[],
                             monthly_comparison={})

@admin_bp.route('/api/process-payment', methods=['POST'])
@admin_required
def api_process_payment():
    """API endpoint для ручной обработки проблемного платежа"""
    try:
        data = request.get_json()
        payment_id = data.get('payment_id')
        action = data.get('action')  # 'complete', 'fail', 'cancel'
        
        if not payment_id or action not in ['complete', 'fail', 'cancel']:
            return jsonify({'success': False, 'error': 'Неверные параметры'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Получаем платеж
        payment = db_service.get_payment_by_id(payment_id)
        if not payment:
            return jsonify({'success': False, 'error': 'Платеж не найден'})
        
        # Определяем новый статус
        status_map = {
            'complete': 'completed',
            'fail': 'failed',
            'cancel': 'expired'
        }
        new_status = status_map[action]
        
        # Обновляем статус платежа
        result = db_service.update_payment_status(payment_id, new_status)
        
        if result and action == 'complete':
            # Если платеж завершен успешно, создаем или продлеваем подписку
            try:
                # Определяем количество месяцев на основе суммы (примерная логика)
                if payment.amount >= 5000:  # 12 месяцев
                    months = 12
                elif payment.amount >= 2500:  # 6 месяцев
                    months = 6
                elif payment.amount >= 1000:  # 3 месяца
                    months = 3
                else:  # 1 месяц
                    months = 1
                
                db_service.admin_grant_subscription(payment.user_id, months)
            except Exception as e:
                logger.error(f"Ошибка создания подписки при обработке платежа: {e}")
        
        if result:
            # Логируем действие администратора
            db_service.log_admin_action(
                admin_telegram_id=0,  # Веб-админ
                action='manual_payment_processing',
                target_user_id=payment.user_id,
                details=f'Ручная обработка платежа {payment_id}: {action}'
            )
            
            logger.info(f"Администратор обработал платеж {payment_id}: {action}")
            return jsonify({'success': True, 'message': f'Платеж {action} успешно'})
        else:
            return jsonify({'success': False, 'error': 'Ошибка обработки платежа'})
    
    except Exception as e:
        logger.error(f"Ошибка ручной обработки платежа: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/revenue-chart')
@admin_required
def api_revenue_chart():
    """API endpoint для получения данных графика доходов"""
    try:
        period = request.args.get('period', '30')
        
        try:
            period_days = int(period)
            if period_days not in [7, 30, 90]:
                period_days = 30
        except ValueError:
            period_days = 30
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Получаем данные для графика
        chart_data = db_service.get_daily_revenue_chart(period_days)
        
        return jsonify({
            'success': True,
            'data': chart_data
        })
    
    except Exception as e:
        logger.error(f"Ошибка получения данных графика доходов: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@admin_bp.route('/api/monthly-revenue-chart')
@admin_required
def api_monthly_revenue_chart():
    """API endpoint для получения данных графика месячных доходов"""
    try:
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Получаем данные для графика
        monthly_data = db_service.get_monthly_revenue_comparison()
        
        return jsonify({
            'success': True,
            'data': monthly_data
        })
    
    except Exception as e:
        logger.error(f"Ошибка получения данных месячного графика доходов: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@admin_bp.route('/api/payment-method-stats')
@admin_required
def api_payment_method_stats():
    """API endpoint для получения статистики способов оплаты"""
    try:
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Получаем статистику способов оплаты
        method_stats = db_service.get_payment_method_statistics()
        
        return jsonify({
            'success': True,
            'data': method_stats
        })
    
    except Exception as e:
        logger.error(f"Ошибка получения статистики способов оплаты: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@admin_bp.route('/api/export-report')
@admin_required
def api_export_report():
    """API endpoint для экспорта финансовых отчетов"""
    try:
        format_type = request.args.get('format', 'csv')
        period = int(request.args.get('period', '30'))
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Получаем данные для экспорта
        revenue_report = db_service.get_revenue_report(period)
        payment_stats = db_service.get_payment_statistics()
        method_stats = db_service.get_payment_method_statistics()
        
        if format_type == 'csv':
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Заголовки
            writer.writerow(['Финансовый отчет за {} дней'.format(period)])
            writer.writerow(['Дата создания', datetime.now().strftime('%d.%m.%Y %H:%M')])
            writer.writerow([])
            
            # Общая статистика
            writer.writerow(['Общая статистика'])
            writer.writerow(['Показатель', 'Значение'])
            writer.writerow(['Общий доход за период', f"{revenue_report.get('total_revenue', 0):.2f} ₽"])
            writer.writerow(['Средний чек', f"{revenue_report.get('average_check', 0):.2f} ₽"])
            writer.writerow(['Количество транзакций', revenue_report.get('total_transactions', 0)])
            writer.writerow([])
            
            # Статистика по способам оплаты
            writer.writerow(['Статистика по способам оплаты'])
            writer.writerow(['Способ оплаты', 'Всего платежей', 'Успешных', 'Неудачных', 'Успешность %', 'Общий доход', 'Средний чек'])
            
            method_names = {
                'card_ru': 'Карта РФ',
                'card_foreign': 'Карта иностранная',
                'crypto': 'Криптовалюта'
            }
            
            for method, stats in method_stats.items():
                writer.writerow([
                    method_names.get(method, method),
                    stats['total_payments'],
                    stats['completed_payments'],
                    stats['failed_payments'],
                    f"{stats['success_rate']:.1f}%",
                    f"{stats['total_revenue']:.2f} ₽",
                    f"{stats['avg_amount']:.2f} ₽"
                ])
            
            writer.writerow([])
            
            # Доходы по дням
            if revenue_report.get('daily_revenue'):
                writer.writerow(['Доходы по дням'])
                writer.writerow(['Дата', 'Доход'])
                for day in revenue_report['daily_revenue']:
                    writer.writerow([day['date'], f"{day['revenue']:.2f} ₽"])
            
            output.seek(0)
            
            from flask import Response
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename=financial_report_{period}days.csv'}
            )
        
        else:
            return jsonify({'success': False, 'error': 'Неподдерживаемый формат экспорта'})
    
    except Exception as e:
        logger.error(f"Ошибка экспорта отчета: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/stats')
@admin_required
def api_stats():
    """API endpoint для получения статистики"""
    try:
        stats = db_service.get_system_statistics() if db_service else {}
        payment_stats = db_service.get_payment_statistics() if db_service else {}
        
        return jsonify({
            'success': True,
            'stats': stats,
            'payment_stats': payment_stats
        })
    
    except Exception as e:
        logger.error(f"Ошибка получения статистики через API: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

def init_admin_services(database_service, scheduler_svc):
    """Инициализация сервисов для админ панели"""
    global db_service, scheduler_service
    db_service = database_service
    scheduler_service = scheduler_svc
    logger.info("Сервисы админ панели инициализированы")

# ==================== НАСТРОЙКИ СИСТЕМЫ ====================

@admin_bp.route('/api/settings/update', methods=['POST'])
@admin_required
def api_update_settings():
    """API endpoint для обновления системных настроек"""
    try:
        data = request.get_json()
        settings = data.get('settings', {})
        
        if not settings:
            return jsonify({'success': False, 'error': 'Настройки не переданы'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        updated_count = 0
        errors = []
        
        for category, category_settings in settings.items():
            for key, setting_data in category_settings.items():
                try:
                    value = setting_data.get('value', '')
                    description = setting_data.get('description', '')
                    data_type = setting_data.get('data_type', 'string')
                    
                    result = db_service.set_system_setting(category, key, value, description, data_type)
                    if result:
                        updated_count += 1
                    else:
                        errors.append(f'Ошибка обновления настройки {category}.{key}')
                        
                except Exception as e:
                    errors.append(f'Ошибка обработки настройки {category}.{key}: {str(e)}')
        
        # Логируем действие
        db_service.log_admin_action(
            admin_telegram_id=0,
            action='update_system_settings',
            details=f'Обновлено настроек: {updated_count}, ошибок: {len(errors)}'
        )
        
        logger.info(f"Обновлено системных настроек: {updated_count}")
        
        return jsonify({
            'success': True,
            'updated_count': updated_count,
            'errors': errors,
            'message': f'Обновлено настроек: {updated_count}'
        })
    
    except Exception as e:
        logger.error(f"Ошибка обновления настроек: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/subscription-plans', methods=['GET'])
@admin_required
def api_get_subscription_plans():
    """API endpoint для получения тарифных планов"""
    try:
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        plans = db_service.get_all_subscription_plans()
        plans_data = []
        
        for plan in plans:
            plans_data.append({
                'id': plan.id,
                'name': plan.name,
                'duration_months': plan.duration_months,
                'price': float(plan.price),
                'currency': plan.currency,
                'description': plan.description,
                'is_active': plan.is_active,
                'sort_order': plan.sort_order,
                'created_at': plan.created_at.isoformat() if plan.created_at else None,
                'updated_at': plan.updated_at.isoformat() if plan.updated_at else None
            })
        
        return jsonify({'success': True, 'plans': plans_data})
    
    except Exception as e:
        logger.error(f"Ошибка получения тарифных планов: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/subscription-plans', methods=['POST'])
@admin_required
def api_create_subscription_plan():
    """API endpoint для создания тарифного плана"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        duration_months = data.get('duration_months')
        price = data.get('price')
        currency = data.get('currency', 'RUB')
        description = data.get('description', '').strip()
        is_active = data.get('is_active', True)
        sort_order = data.get('sort_order', 0)
        
        # Валидация
        if not name:
            return jsonify({'success': False, 'error': 'Название плана обязательно'})
        
        if not duration_months or not isinstance(duration_months, int) or duration_months < 1:
            return jsonify({'success': False, 'error': 'Некорректная продолжительность'})
        
        if not price or price <= 0:
            return jsonify({'success': False, 'error': 'Некорректная цена'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        from decimal import Decimal
        plan = db_service.create_subscription_plan(
            name=name,
            duration_months=duration_months,
            price=Decimal(str(price)),
            currency=currency,
            description=description if description else None,
            is_active=is_active,
            sort_order=sort_order
        )
        
        if plan:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=0,
                action='create_subscription_plan',
                details=f'Создан тарифный план: {name} ({duration_months} мес., {price} {currency})'
            )
            
            logger.info(f"Создан тарифный план: {name}")
            return jsonify({'success': True, 'message': 'Тарифный план создан', 'plan_id': plan.id})
        else:
            return jsonify({'success': False, 'error': 'Ошибка создания тарифного плана'})
    
    except Exception as e:
        logger.error(f"Ошибка создания тарифного плана: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/subscription-plans/<int:plan_id>', methods=['PUT'])
@admin_required
def api_update_subscription_plan(plan_id):
    """API endpoint для обновления тарифного плана"""
    try:
        data = request.get_json()
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Проверяем существование плана
        existing_plan = db_service.get_subscription_plan_by_id(plan_id)
        if not existing_plan:
            return jsonify({'success': False, 'error': 'Тарифный план не найден'})
        
        # Подготавливаем данные для обновления
        update_data = {}
        
        if 'name' in data:
            name = data['name'].strip()
            if not name:
                return jsonify({'success': False, 'error': 'Название плана не может быть пустым'})
            update_data['name'] = name
        
        if 'duration_months' in data:
            duration = data['duration_months']
            if not isinstance(duration, int) or duration < 1:
                return jsonify({'success': False, 'error': 'Некорректная продолжительность'})
            update_data['duration_months'] = duration
        
        if 'price' in data:
            price = data['price']
            if price <= 0:
                return jsonify({'success': False, 'error': 'Некорректная цена'})
            from decimal import Decimal
            update_data['price'] = Decimal(str(price))
        
        if 'currency' in data:
            update_data['currency'] = data['currency']
        
        if 'description' in data:
            description = data['description'].strip()
            update_data['description'] = description if description else None
        
        if 'is_active' in data:
            update_data['is_active'] = bool(data['is_active'])
        
        if 'sort_order' in data:
            update_data['sort_order'] = int(data['sort_order'])
        
        # Обновляем план
        result = db_service.update_subscription_plan(plan_id, **update_data)
        
        if result:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=0,
                action='update_subscription_plan',
                details=f'Обновлен тарифный план ID {plan_id}'
            )
            
            logger.info(f"Обновлен тарифный план {plan_id}")
            return jsonify({'success': True, 'message': 'Тарифный план обновлен'})
        else:
            return jsonify({'success': False, 'error': 'Ошибка обновления тарифного плана'})
    
    except Exception as e:
        logger.error(f"Ошибка обновления тарифного плана {plan_id}: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/subscription-plans/<int:plan_id>', methods=['DELETE'])
@admin_required
def api_delete_subscription_plan(plan_id):
    """API endpoint для удаления тарифного плана"""
    try:
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Проверяем существование плана
        existing_plan = db_service.get_subscription_plan_by_id(plan_id)
        if not existing_plan:
            return jsonify({'success': False, 'error': 'Тарифный план не найден'})
        
        # Удаляем план
        result = db_service.delete_subscription_plan(plan_id)
        
        if result:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=0,
                action='delete_subscription_plan',
                details=f'Удален тарифный план ID {plan_id} ({existing_plan.name})'
            )
            
            logger.info(f"Удален тарифный план {plan_id}")
            return jsonify({'success': True, 'message': 'Тарифный план удален'})
        else:
            return jsonify({'success': False, 'error': 'Ошибка удаления тарифного плана'})
    
    except Exception as e:
        logger.error(f"Ошибка удаления тарифного плана {plan_id}: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/bot-texts', methods=['GET'])
@admin_required
def api_get_bot_texts():
    """API endpoint для получения текстов бота"""
    try:
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        texts = db_service.get_all_bot_texts()
        texts_data = []
        
        for text in texts:
            texts_data.append({
                'id': text.id,
                'key': text.key,
                'text': text.text,
                'description': text.description,
                'created_at': text.created_at.isoformat() if text.created_at else None,
                'updated_at': text.updated_at.isoformat() if text.updated_at else None
            })
        
        return jsonify({'success': True, 'texts': texts_data})
    
    except Exception as e:
        logger.error(f"Ошибка получения текстов бота: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/bot-texts', methods=['POST'])
@admin_required
def api_create_bot_text():
    """API endpoint для создания/обновления текста бота"""
    try:
        data = request.get_json()
        key = data.get('key', '').strip()
        text = data.get('text', '').strip()
        description = data.get('description', '').strip()
        
        # Валидация
        if not key:
            return jsonify({'success': False, 'error': 'Ключ текста обязателен'})
        
        if not text:
            return jsonify({'success': False, 'error': 'Текст не может быть пустым'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Проверяем, существует ли уже текст с таким ключом
        existing_text = db_service.get_bot_text(key)
        action = 'update_bot_text' if existing_text else 'create_bot_text'
        
        result = db_service.set_bot_text(key, text, description if description else None)
        
        if result:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=0,
                action=action,
                details=f'{"Обновлен" if existing_text else "Создан"} текст бота: {key}'
            )
            
            logger.info(f"{'Обновлен' if existing_text else 'Создан'} текст бота: {key}")
            return jsonify({
                'success': True, 
                'message': f'Текст бота {"обновлен" if existing_text else "создан"}'
            })
        else:
            return jsonify({'success': False, 'error': 'Ошибка сохранения текста бота'})
    
    except Exception as e:
        logger.error(f"Ошибка создания/обновления текста бота: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/bot-texts/<key>', methods=['DELETE'])
@admin_required
def api_delete_bot_text(key):
    """API endpoint для удаления текста бота"""
    try:
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Проверяем существование текста
        existing_text = db_service.get_bot_text(key)
        if not existing_text:
            return jsonify({'success': False, 'error': 'Текст бота не найден'})
        
        # Удаляем текст
        result = db_service.delete_bot_text(key)
        
        if result:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=0,
                action='delete_bot_text',
                details=f'Удален текст бота: {key}'
            )
            
            logger.info(f"Удален текст бота: {key}")
            return jsonify({'success': True, 'message': 'Текст бота удален'})
        else:
            return jsonify({'success': False, 'error': 'Ошибка удаления текста бота'})
    
    except Exception as e:
        logger.error(f"Ошибка удаления текста бота {key}: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/backup/create', methods=['POST'])
@admin_required
def api_create_backup():
    """API endpoint для создания резервной копии"""
    try:
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        from datetime import datetime
        import os
        
        # Создаем имя файла резервной копии
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'payments_backup_{timestamp}.db'
        
        # Создаем директорию backups если не существует
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # Создаем резервную копию
        result = db_service.create_backup(backup_path)
        
        if result:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=0,
                action='create_backup',
                details=f'Создана резервная копия: {backup_filename}'
            )
            
            logger.info(f"Создана резервная копия: {backup_filename}")
            return jsonify({
                'success': True, 
                'message': 'Резервная копия создана',
                'backup_file': backup_filename
            })
        else:
            return jsonify({'success': False, 'error': 'Ошибка создания резервной копии'})
    
    except Exception as e:
        logger.error(f"Ошибка создания резервной копии: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/backup/list', methods=['GET'])
@admin_required
def api_list_backups():
    """API endpoint для получения списка резервных копий"""
    try:
        import os
        import glob
        from datetime import datetime
        
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            return jsonify({'success': True, 'backups': []})
        
        # Получаем список файлов резервных копий
        backup_pattern = os.path.join(backup_dir, 'payments_backup_*.db')
        backup_files = glob.glob(backup_pattern)
        
        backups = []
        for backup_file in backup_files:
            filename = os.path.basename(backup_file)
            file_size = os.path.getsize(backup_file)
            file_mtime = os.path.getmtime(backup_file)
            
            backups.append({
                'filename': filename,
                'path': backup_file,
                'size': file_size,
                'created_at': datetime.fromtimestamp(file_mtime).isoformat(),
                'size_mb': round(file_size / (1024 * 1024), 2)
            })
        
        # Сортируем по дате создания (новые сначала)
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        
        return jsonify({'success': True, 'backups': backups})
    
    except Exception as e:
        logger.error(f"Ошибка получения списка резервных копий: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/backup/restore', methods=['POST'])
@admin_required
def api_restore_backup():
    """API endpoint для восстановления из резервной копии"""
    try:
        data = request.get_json()
        backup_filename = data.get('backup_filename', '').strip()
        
        if not backup_filename:
            return jsonify({'success': False, 'error': 'Не указан файл резервной копии'})
        
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        import os
        backup_path = os.path.join('backups', backup_filename)
        
        if not os.path.exists(backup_path):
            return jsonify({'success': False, 'error': 'Файл резервной копии не найден'})
        
        # Восстанавливаем из резервной копии
        result = db_service.restore_backup(backup_path)
        
        if result:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=0,
                action='restore_backup',
                details=f'Восстановлена резервная копия: {backup_filename}'
            )
            
            logger.info(f"Восстановлена резервная копия: {backup_filename}")
            return jsonify({
                'success': True, 
                'message': 'База данных восстановлена из резервной копии'
            })
        else:
            return jsonify({'success': False, 'error': 'Ошибка восстановления из резервной копии'})
    
    except Exception as e:
        logger.error(f"Ошибка восстановления резервной копии: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/api/initialize-defaults', methods=['POST'])
@admin_required
def api_initialize_defaults():
    """API endpoint для инициализации настроек по умолчанию"""
    try:
        if not db_service:
            return jsonify({'success': False, 'error': 'Сервис базы данных недоступен'})
        
        # Инициализируем настройки по умолчанию
        db_service.initialize_default_settings()
        
        # Логируем действие
        db_service.log_admin_action(
            admin_telegram_id=0,
            action='initialize_default_settings',
            details='Инициализированы настройки по умолчанию'
        )
        
        logger.info("Инициализированы настройки по умолчанию")
        return jsonify({
            'success': True, 
            'message': 'Настройки по умолчанию инициализированы'
        })
    
    except Exception as e:
        logger.error(f"Ошибка инициализации настроек по умолчанию: {e}")
        return jsonify({'success': False, 'error': str(e)})