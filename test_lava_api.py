#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы Lava.top API
"""

import os
import sys
import time
import requests
import json
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

# Получаем настройки
LAVA_API_KEY = os.getenv('LAVA_API_KEY')
LAVA_SECRET_KEY = os.getenv('LAVA_SECRET_KEY')
LAVA_BASE_URL = os.getenv('LAVA_BASE_URL')

print("=== Проверка переменных окружения ===")
print(f"LAVA_BASE_URL: {LAVA_BASE_URL}")
print(f"LAVA_API_KEY: {'✅ Установлен' if LAVA_API_KEY and LAVA_API_KEY != 'your_lava_api_key_here' else '❌ Не установлен или заглушка'}")
print(f"LAVA_SECRET_KEY: {'✅ Установлен' if LAVA_SECRET_KEY and LAVA_SECRET_KEY != 'your_lava_secret_key_here' else '❌ Не установлен или заглушка'}")
print()

if not LAVA_API_KEY or LAVA_API_KEY == 'your_lava_api_key_here':
    print("❌ LAVA_API_KEY не установлен!")
    exit(1)

# Тестируем разные способы авторизации
def test_auth_methods():
    print("=== Тестирование способов авторизации ===")
    
    # Способ 1: API ключ в заголовке Authorization
    headers1 = {
        'Authorization': f'Bearer {LAVA_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # Способ 2: API ключ в заголовке X-API-Key
    headers2 = {
        'X-API-Key': LAVA_API_KEY,
        'Content-Type': 'application/json'
    }
    
    # Способ 3: API ключ в заголовке Authorization без Bearer
    headers3 = {
        'Authorization': LAVA_API_KEY,
        'Content-Type': 'application/json'
    }
    
    # Способ 4: API ключ в параметрах запроса
    params4 = {'api_key': LAVA_API_KEY}
    headers4 = {'Content-Type': 'application/json'}
    
    methods = [
        ("Bearer в Authorization", headers1, {}),
        ("X-API-Key заголовок", headers2, {}),
        ("Authorization без Bearer", headers3, {}),
        ("api_key в параметрах", headers4, params4)
    ]
    
    test_url = f"{LAVA_BASE_URL}/api/v1/invoices"
    
    for method_name, headers, params in methods:
        print(f"🔄 Тестируем: {method_name}")
        try:
            response = requests.get(test_url, headers=headers, params=params, timeout=10)
            print(f"   Статус: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ Успешно! Найден правильный способ авторизации")
                return headers, params
            elif response.status_code == 401:
                print(f"   ❌ Ошибка авторизации")
            elif response.status_code == 404:
                print(f"   ❌ Эндпоинт не найден")
            else:
                print(f"   ⚠️  Неожиданный статус: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Ошибка запроса: {e}")
        print()
    
    return None, None

# Тестируем создание счета с разными способами авторизации
def test_invoice_creation():
    print("=== Тест создания счета ===")
    
    # Пробуем разные способы авторизации
    auth_headers, auth_params = test_auth_methods()
    
    if not auth_headers:
        print("❌ Не удалось найти рабочий способ авторизации")
        return
    
    # Данные для создания счета согласно Lava API v2
    invoice_data = {
        "orderId": f"test_{int(time.time())}",
        "sum": 299.0,
        "currency": "RUB",
        "offerId": "5b34c4d5-56a8-4d12-b666-ef6f6649ad13",
        "email": "<EMAIL>",
        "description": "Тестовый платеж - подписка на канал",
        "successUrl": "https://example.com/success",
        "failUrl": "https://example.com/fail",
        "hookUrl": "https://example.com/webhook"
    }
    
    create_url = f"{LAVA_BASE_URL}/api/v2/invoice"
    
    try:
        response = requests.post(
            create_url, 
            json=invoice_data, 
            headers=auth_headers,
            params=auth_params,
            timeout=10
        )
        
        print(f"🔄 Создание счета: POST {create_url}")
        print(f"   Статус: {response.status_code}")
        
        if response.status_code == 200 or response.status_code == 201:
            print("   ✅ Счет успешно создан!")
            try:
                result = response.json()
                print(f"   📄 Ответ: {result}")
                return result
            except:
                print(f"   📄 Ответ (текст): {response.text}")
        else:
            print(f"   ❌ Ошибка {response.status_code}")
            print(f"   📄 Ответ: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Ошибка запроса: {e}")

def test_lava_api():
    """Тестирует подключение к Lava.top API"""
    
    # Получаем настройки из .env
    api_key = os.getenv('LAVA_API_KEY')
    secret_key = os.getenv('LAVA_SECRET_KEY')
    base_url = os.getenv('LAVA_BASE_URL', 'https://api.lava.top')
    
    print("=== Тест Lava.top API ===")
    print(f"Base URL: {base_url}")
    print(f"API Key: {api_key[:10]}..." if api_key else "API Key: НЕ УСТАНОВЛЕН")
    print(f"Secret Key: {'УСТАНОВЛЕН' if secret_key and secret_key != 'your_lava_secret_key_here' else 'НЕ УСТАНОВЛЕН ИЛИ ЗАГЛУШКА'}")
    print()
    
    # Проверяем настройки
    if not api_key:
        print("❌ ОШИБКА: LAVA_API_KEY не установлен в .env файле")
        return False
        
    if not secret_key or secret_key == 'your_lava_secret_key_here':
        print("❌ ОШИБКА: LAVA_SECRET_KEY не установлен или используется заглушка")
        print("   Необходимо получить настоящий секретный ключ в личном кабинете lava.top")
        return False
    
    # Тестируем создание счета
    print("🔄 Тестируем создание счета...")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    test_data = {
        'sum': 299.0,
        'orderId': f'test_{int(time.time())}',
        'shopId': 'test_shop',
        'hookUrl': 'https://example.com/webhook'
    }
    
    try:
        # Используем исправленный эндпоинт
        response = requests.post(
            f"{base_url}/api/v2/invoice",
            headers=headers,
            json=test_data,
            timeout=10
        )
        
        print(f"Статус ответа: {response.status_code}")
        print(f"Заголовки ответа: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Успешно! Ответ API:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ Ошибка {response.status_code}")
            try:
                error_data = response.json()
                print("Ответ с ошибкой:")
                print(json.dumps(error_data, indent=2, ensure_ascii=False))
            except:
                print("Текст ответа:")
                print(response.text)
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Ошибка сети: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def test_api_endpoints():
    """Тестирует доступность различных эндпоинтов"""
    
    base_url = os.getenv('LAVA_BASE_URL', 'https://api.lava.top')
    api_key = os.getenv('LAVA_API_KEY')
    
    if not api_key:
        print("❌ API ключ не найден")
        return
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    endpoints_to_test = [
        ('GET', '/api/v2/offers', 'Получение списка офферов'),
        ('GET', '/api/v1/invoices', 'Получение списка счетов'),
        ('GET', '/api/v2/products', 'Получение списка продуктов'),
    ]
    
    print("\n=== Тест доступности эндпоинтов ===")
    
    for method, endpoint, description in endpoints_to_test:
        try:
            print(f"🔄 {description}: {method} {endpoint}")
            
            if method == 'GET':
                response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
            else:
                response = requests.request(method, f"{base_url}{endpoint}", headers=headers, timeout=10)
            
            print(f"   Статус: {response.status_code}")
            
            if response.status_code in [200, 201]:
                print("   ✅ Доступен")
                try:
                    data = response.json()
                    if endpoint == '/api/v2/offers' and data:
                        print("   📋 Доступные офферы:")
                        if isinstance(data, list):
                            for offer in data[:3]:  # Показываем первые 3
                                print(f"      ID: {offer.get('id', 'N/A')}, Название: {offer.get('name', 'N/A')}")
                        elif isinstance(data, dict) and 'data' in data:
                            for offer in data['data'][:3]:
                                print(f"      ID: {offer.get('id', 'N/A')}, Название: {offer.get('name', 'N/A')}")
                    elif endpoint == '/api/v2/products' and data:
                        print("   📋 Доступные продукты:")
                        if isinstance(data, list):
                            for product in data[:3]:  # Показываем первые 3
                                print(f"      ID: {product.get('id', 'N/A')}, Название: {product.get('name', 'N/A')}")
                        elif isinstance(data, dict) and 'data' in data:
                            for product in data['data'][:3]:
                                print(f"      ID: {product.get('id', 'N/A')}, Название: {product.get('name', 'N/A')}")
                        elif isinstance(data, dict):
                            print(f"      Структура ответа: {list(data.keys())}")
                    elif endpoint == '/api/v1/invoices' and data:
                        print("   📋 Структура ответа счетов:")
                        if isinstance(data, dict):
                            print(f"      Ключи: {list(data.keys())}")
                        elif isinstance(data, list):
                            print(f"      Количество счетов: {len(data)}")
                except:
                    pass
            elif response.status_code == 401:
                print("   ⚠️  Ошибка авторизации (проверьте API ключ)")
            elif response.status_code == 404:
                print("   ❌ Эндпоинт не найден")
            else:
                print(f"   ⚠️  Статус {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Ошибка: {e}")

if __name__ == '__main__':
    test_auth_methods()
    test_invoice_creation()
    test_api_endpoints()
    
    print("\n" + "="*50)
    print("🔍 Если все еще есть проблемы:")
    print("1. Проверьте, что API ключ создан в личном кабинете lava.top")
    print("2. Убедитесь, что у вас есть права на использование API")
    print("3. Проверьте документацию: https://gate.lava.top/docs")
    print("4. Возможно, нужно активировать API в настройках аккаунта")