@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Setting up telegrambot user and directory structure...
echo.
echo Creating telegrambot user:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S useradd -m -s /bin/bash telegrambot || echo 'User already exists'"
echo.
echo Creating app directory:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S mkdir -p /home/<USER>/app"
echo.
echo Copying files from /opt/telegram-payment-bot/app/ to /home/<USER>/app/:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S cp -r /opt/telegram-payment-bot/app/* /home/<USER>/app/ 2>/dev/null || echo 'No files to copy from /opt/telegram-payment-bot/app/'"
echo.
echo Uploading all project files to server:
echo Done with setup commands.