@echo off
echo 🚀 Автоматическая установка Rinadzhi Bot на сервер
echo ================================================
echo.

set SERVER=ubuntu@195.49.212.172
set PASSWORD=dkomqgTaijxro7in^^bxd

echo 📋 Шаг 1: Проверка подключения к серверу...
plink -ssh %SERVER% -pw %PASSWORD% "echo 'Подключение успешно!'"
if errorlevel 1 (
    echo ❌ Ошибка подключения к серверу
    pause
    exit /b 1
)
echo ✅ Подключение к серверу работает

echo.
echo 📋 Шаг 2: Создание пользователя telegrambot...
plink -ssh %SERVER% -pw %PASSWORD% "sudo useradd -m -s /bin/bash telegrambot 2>/dev/null || echo 'Пользователь уже существует'"
echo ✅ Пользователь telegrambot готов

echo.
echo 📋 Шаг 3: Создание директорий...
plink -ssh %SERVER% -pw %PASSWORD% "sudo mkdir -p /home/<USER>/app"
plink -ssh %SERVER% -pw %PASSWORD% "sudo mkdir -p /var/log/telegram-payment-bot"
plink -ssh %SERVER% -pw %PASSWORD% "sudo chown telegrambot:telegrambot /home/<USER>/app"
plink -ssh %SERVER% -pw %PASSWORD% "sudo chown telegrambot:telegrambot /var/log/telegram-payment-bot"
echo ✅ Директории созданы

echo.
echo 📋 Шаг 4: Установка Python и зависимостей...
plink -ssh %SERVER% -pw %PASSWORD% "sudo apt update && sudo apt install -y python3 python3-pip python3-venv"
echo ✅ Python установлен

echo.
echo 📋 Шаг 5: Создание виртуального окружения...
plink -ssh %SERVER% -pw %PASSWORD% "cd /home/<USER>/app && sudo -u telegrambot python3 -m venv venv"
plink -ssh %SERVER% -pw %PASSWORD% "sudo -u telegrambot /home/<USER>/app/venv/bin/pip install --upgrade pip"
plink -ssh %SERVER% -pw %PASSWORD% "sudo -u telegrambot /home/<USER>/app/venv/bin/pip install pyTelegramBotAPI requests"
echo ✅ Виртуальное окружение и зависимости установлены

echo.
echo 📋 Шаг 6: Загрузка файлов бота...

echo Создание rinadzhi_bot.py...
plink -ssh %SERVER% -pw %PASSWORD% "cat > /tmp/rinadzhi_bot.py" < server_files/rinadzhi_bot.py

echo Создание config.py...
plink -ssh %SERVER% -pw %PASSWORD% "cat > /tmp/config.py" < server_files/config.py

echo Создание init_database.py...
plink -ssh %SERVER% -pw %PASSWORD% "cat > /tmp/init_database.py" < server_files/init_database.py

echo Копирование файлов в рабочую директорию...
plink -ssh %SERVER% -pw %PASSWORD% "sudo cp /tmp/rinadzhi_bot.py /home/<USER>/app/"
plink -ssh %SERVER% -pw %PASSWORD% "sudo cp /tmp/config.py /home/<USER>/app/"
plink -ssh %SERVER% -pw %PASSWORD% "sudo cp /tmp/init_database.py /home/<USER>/app/"

echo Установка прав доступа...
plink -ssh %SERVER% -pw %PASSWORD% "sudo chown -R telegrambot:telegrambot /home/<USER>/app/"
plink -ssh %SERVER% -pw %PASSWORD% "sudo chmod +x /home/<USER>/app/rinadzhi_bot.py"
echo ✅ Файлы загружены и настроены

echo.
echo 📋 Шаг 7: Инициализация базы данных...
plink -ssh %SERVER% -pw %PASSWORD% "sudo -u telegrambot /home/<USER>/app/venv/bin/python /home/<USER>/app/init_database.py"
echo ✅ База данных инициализирована

echo.
echo 📋 Шаг 8: Создание systemd сервиса...
plink -ssh %SERVER% -pw %PASSWORD% "sudo tee /etc/systemd/system/rinadzhi-bot.service > /dev/null << 'EOF'
[Unit]
Description=Rinadzhi Telegram Payment Bot
After=network.target

[Service]
Type=simple
User=telegrambot
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/venv/bin/python rinadzhi_bot.py
Restart=always
RestartSec=10
StandardOutput=append:/var/log/telegram-payment-bot/rinadzhi_bot.log
StandardError=append:/var/log/telegram-payment-bot/rinadzhi_bot.log

[Install]
WantedBy=multi-user.target
EOF"

plink -ssh %SERVER% -pw %PASSWORD% "sudo systemctl daemon-reload"
echo ✅ Systemd сервис создан

echo.
echo 📋 Шаг 9: Запуск бота...
plink -ssh %SERVER% -pw %PASSWORD% "sudo systemctl start rinadzhi-bot"
plink -ssh %SERVER% -pw %PASSWORD% "sudo systemctl enable rinadzhi-bot"
echo ✅ Бот запущен и добавлен в автозагрузку

echo.
echo 📋 Шаг 10: Проверка статуса...
plink -ssh %SERVER% -pw %PASSWORD% "sudo systemctl status rinadzhi-bot --no-pager"

echo.
echo 🎉 УСТАНОВКА ЗАВЕРШЕНА!
echo ========================
echo.
echo 📊 Управление ботом:
echo    plink -ssh %SERVER% -pw %PASSWORD% "sudo systemctl status rinadzhi-bot"
echo    plink -ssh %SERVER% -pw %PASSWORD% "sudo systemctl restart rinadzhi-bot"
echo    plink -ssh %SERVER% -pw %PASSWORD% "sudo systemctl stop rinadzhi-bot"
echo.
echo 📋 Просмотр логов:
echo    plink -ssh %SERVER% -pw %PASSWORD% "sudo tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log"
echo.
echo ✅ Бот готов к использованию!
echo 🤖 Протестируйте бота: https://t.me/rinadzhi_bot
echo.
pause
