# Update admin panel on server - based on working restart_services.ps1
param(
    [string]$ServerIP = "**************",
    [string]$Username = "ubuntu"
)

Write-Host "Preparing admin panel update..." -ForegroundColor Green

# Create archive
Write-Host "Creating archive..." -ForegroundColor Yellow
if (Test-Path "admin_panel_fix.zip") {
    Remove-Item "admin_panel_fix.zip" -Force
}
Compress-Archive -Path "app\admin.py", "templates\*" -DestinationPath "admin_panel_fix.zip" -Force
Write-Host "Archive created successfully" -ForegroundColor Green

# Get password securely
$SecurePassword = Read-Host "Enter server password" -AsSecureString
$Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))

Write-Host "Uploading archive to server..." -ForegroundColor Yellow

# Upload file
$UploadFile = @"
echo 'File upload completed'
"@

# First upload the file using pscp
& pscp -pw $Password admin_panel_fix.zip $Username@$ServerIP:/home/<USER>/

Write-Host "Stopping telegram-bot service..." -ForegroundColor Yellow

# Stop service
$StopBot = @"
echo '$Password' | sudo -S systemctl stop telegram-bot
"@

echo $StopBot | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host "Creating backup and updating files..." -ForegroundColor Yellow

# Update files
$UpdateFiles = @"
cd /home/<USER>/telegram_bot
echo '$Password' | sudo -S cp -r app templates backup_`$(date +%Y%m%d_%H%M%S)
unzip -o /home/<USER>/admin_panel_fix.zip
echo '$Password' | sudo -S chown -R ubuntu:ubuntu app templates
echo '$Password' | sudo -S chmod -R 755 app templates
"@

echo $UpdateFiles | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host "Starting telegram-bot service..." -ForegroundColor Yellow

# Start service
$StartBot = @"
echo '$Password' | sudo -S systemctl start telegram-bot
"@

echo $StartBot | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host "Checking service status..." -ForegroundColor Yellow

# Check status
$CheckStatus = @"
echo '$Password' | sudo -S systemctl status telegram-bot --no-pager -l
"@

echo $CheckStatus | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host "Verifying updates..." -ForegroundColor Yellow

# Verify updates
$VerifyUpdates = @"
cd /home/<USER>/telegram_bot
if grep -q 'safe_url_for' app/admin.py; then echo 'admin.py updated: OK'; else echo 'admin.py update: FAILED'; fi
if grep -q 'safe_url_for' templates/base.html; then echo 'templates updated: OK'; else echo 'templates update: FAILED'; fi
"@

echo $VerifyUpdates | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host "Admin panel update completed!" -ForegroundColor Green
Write-Host ""
Write-Host "TEST NOW:" -ForegroundColor Cyan
Write-Host "1. Open: https://$ServerIP/admin/login" -ForegroundColor White
Write-Host "2. Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "If issues persist, check logs with:" -ForegroundColor Yellow
Write-Host "ssh $Username@$ServerIP 'sudo journalctl -u telegram-bot -f'" -ForegroundColor White