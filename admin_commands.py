#!/usr/bin/env python3
"""
Административные команды для Telegram Payment Bot
"""

import sys
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from app.models.database import DatabaseService, DatabaseManager

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('admin_commands')

def show_statistics():
    """Показать общую статистику системы"""
    try:
        db_manager = DatabaseManager()
        db_service = DatabaseService(db_manager)
        
        print("=" * 60)
        print("СТАТИСТИКА СИСТЕМЫ")
        print("=" * 60)
        
        # Общая статистика
        stats = db_service.get_system_statistics()
        print(f"Всего пользователей: {stats['total_users']}")
        print(f"Новых пользователей за 30 дней: {stats['new_users_30d']}")
        print(f"Активных подписок: {stats['active_subscriptions']}")
        print(f"Общий доход: {stats['total_revenue']} RUB")
        
        print("\n" + "-" * 40)
        print("СТАТИСТИКА ПЛАТЕЖЕЙ")
        print("-" * 40)
        
        # Статистика платежей
        payment_stats = db_service.get_payment_statistics()
        for status, data in payment_stats['by_status'].items():
            print(f"{status.upper()}: {data['count']} платежей на сумму {data['total']} RUB")
        
        print(f"\nЗа последние 30 дней: {payment_stats['last_30_days']['count']} платежей на сумму {payment_stats['last_30_days']['total']} RUB")
        
        print("\n" + "-" * 40)
        print("СТАТИСТИКА ПОДПИСОК")
        print("-" * 40)
        
        # Статистика подписок
        sub_stats = db_service.get_subscription_statistics()
        print(f"Активных: {sub_stats['active']}")
        print(f"Истекших: {sub_stats['expired']}")
        print(f"Отмененных: {sub_stats['cancelled']}")
        print(f"Новых за 30 дней: {sub_stats['new_30d']}")
        
        if sub_stats['plan_types']:
            print("\nПо типам планов:")
            for plan_type, count in sub_stats['plan_types'].items():
                print(f"  {plan_type}: {count}")
        
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"Ошибка получения статистики: {e}")
        print(f"Ошибка: {e}")

def show_revenue_report(days=30):
    """Показать отчет по доходам за указанный период"""
    try:
        db_manager = DatabaseManager()
        db_service = DatabaseService(db_manager)
        
        print("=" * 60)
        print(f"ОТЧЕТ ПО ДОХОДАМ ЗА {days} ДНЕЙ")
        print("=" * 60)
        
        report = db_service.get_revenue_report(days)
        
        print(f"Общий доход: {report['total_revenue']} RUB")
        print(f"Количество платежей: {report['total_payments']}")
        print(f"Средний чек: {report['avg_payment']} RUB")
        
        if report['payment_methods']:
            print("\nПо способам оплаты:")
            for method_data in report['payment_methods']:
                print(f"  {method_data['method']}: {method_data['revenue']} RUB ({method_data['count']} платежей)")
        
        if report['daily_data']:
            print(f"\nПоследние 10 дней:")
            for day_data in report['daily_data'][-10:]:
                print(f"  {day_data['date']}: {day_data['revenue']} RUB ({day_data['count']} платежей)")
        
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"Ошибка получения отчета по доходам: {e}")
        print(f"Ошибка: {e}")

def show_admin_logs(limit=50):
    """Показать последние логи администратора"""
    try:
        db_manager = DatabaseManager()
        db_service = DatabaseService(db_manager)
        
        print("=" * 80)
        print(f"ПОСЛЕДНИЕ {limit} ЛОГОВ АДМИНИСТРАТОРА")
        print("=" * 80)
        
        logs = db_service.get_admin_logs(limit=limit)
        
        if not logs:
            print("Логи не найдены")
            return
        
        for log in logs:
            timestamp = log.created_at.strftime('%d.%m.%Y %H:%M:%S') if log.created_at else 'N/A'
            admin_id = 'Веб-админ' if log.admin_telegram_id == 0 else f'Admin {log.admin_telegram_id}'
            target = f'User {log.target_user_id}' if log.target_user_id else 'N/A'
            details = log.details or 'N/A'
            
            print(f"{timestamp} | {admin_id} | {log.action} | {target}")
            if details != 'N/A':
                print(f"  Детали: {details}")
            print("-" * 80)
        
    except Exception as e:
        logger.error(f"Ошибка получения логов: {e}")
        print(f"Ошибка: {e}")

def grant_subscription(user_id, months):
    """Выдать подписку пользователю"""
    try:
        db_manager = DatabaseManager()
        db_service = DatabaseService(db_manager)
        
        # Проверяем существование пользователя
        user = db_service.get_user_by_id(user_id)
        if not user:
            print(f"Пользователь с ID {user_id} не найден")
            return
        
        # Выдаем подписку
        result = db_service.admin_grant_subscription(user_id, months)
        
        if result:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=999999,  # CLI админ
                action='grant_subscription_cli',
                target_user_id=user_id,
                details=f'Выдана подписка на {months} месяцев через CLI'
            )
            
            print(f"Подписка на {months} месяцев успешно выдана пользователю {user_id}")
            print(f"Пользователь: {user.first_name or ''} {user.last_name or ''} (@{user.username or 'N/A'})")
        else:
            print(f"Ошибка выдачи подписки пользователю {user_id}")
        
    except Exception as e:
        logger.error(f"Ошибка выдачи подписки: {e}")
        print(f"Ошибка: {e}")

def revoke_subscription(user_id):
    """Отозвать подписку пользователя"""
    try:
        db_manager = DatabaseManager()
        db_service = DatabaseService(db_manager)
        
        # Проверяем существование пользователя
        user = db_service.get_user_by_id(user_id)
        if not user:
            print(f"Пользователь с ID {user_id} не найден")
            return
        
        # Отзываем подписку
        result = db_service.admin_revoke_subscription(user_id)
        
        if result:
            # Логируем действие
            db_service.log_admin_action(
                admin_telegram_id=999999,  # CLI админ
                action='revoke_subscription_cli',
                target_user_id=user_id,
                details='Подписка отозвана через CLI'
            )
            
            print(f"Подписка успешно отозвана у пользователя {user_id}")
            print(f"Пользователь: {user.first_name or ''} {user.last_name or ''} (@{user.username or 'N/A'})")
        else:
            print(f"У пользователя {user_id} нет активной подписки для отзыва")
        
    except Exception as e:
        logger.error(f"Ошибка отзыва подписки: {e}")
        print(f"Ошибка: {e}")

def show_help():
    """Показать справку по командам"""
    print("=" * 60)
    print("АДМИНИСТРАТИВНЫЕ КОМАНДЫ")
    print("=" * 60)
    print("python admin_commands.py stats                    - Общая статистика")
    print("python admin_commands.py revenue [days]          - Отчет по доходам")
    print("python admin_commands.py logs [limit]            - Логи администратора")
    print("python admin_commands.py grant <user_id> <months> - Выдать подписку")
    print("python admin_commands.py revoke <user_id>        - Отозвать подписку")
    print("python admin_commands.py help                    - Эта справка")
    print("=" * 60)

def main():
    """Главная функция обработки команд"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == 'stats':
        show_statistics()
    
    elif command == 'revenue':
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 30
        show_revenue_report(days)
    
    elif command == 'logs':
        limit = int(sys.argv[2]) if len(sys.argv) > 2 else 50
        show_admin_logs(limit)
    
    elif command == 'grant':
        if len(sys.argv) < 4:
            print("Использование: python admin_commands.py grant <user_id> <months>")
            return
        
        try:
            user_id = int(sys.argv[2])
            months = int(sys.argv[3])
            
            if months < 1 or months > 12:
                print("Количество месяцев должно быть от 1 до 12")
                return
            
            grant_subscription(user_id, months)
        except ValueError:
            print("user_id и months должны быть числами")
    
    elif command == 'revoke':
        if len(sys.argv) < 3:
            print("Использование: python admin_commands.py revoke <user_id>")
            return
        
        try:
            user_id = int(sys.argv[2])
            revoke_subscription(user_id)
        except ValueError:
            print("user_id должен быть числом")
    
    elif command == 'help':
        show_help()
    
    else:
        print(f"Неизвестная команда: {command}")
        show_help()

if __name__ == '__main__':
    main()