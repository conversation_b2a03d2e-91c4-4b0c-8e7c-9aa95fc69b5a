#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rinadzhi Telegram Payment Bot
Final version with proper Russian interface and emojis
"""

import sys
import os
import logging
import time
import json
import sqlite3
import requests
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

# Add current directory to path
sys.path.insert(0, "/home/<USER>/app")

try:
    from config import Config
except ImportError:
    # Fallback configuration if config.py is not found
    class Config:
        TELEGRAM_BOT_TOKEN = "**********************************************"
        LAVA_API_KEY = "your_lava_api_key_here"
        LAVA_SECRET_KEY = "your_lava_secret_key_here"
import telebot
from telebot import types

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("rinadzhi_bot")

# Create bot instance
bot = telebot.TeleBot(Config.TELEGRAM_BOT_TOKEN)

# Bot configuration
CHANNEL_ID = "@rinadzhi_channel"  # Replace with your channel ID
ADMIN_IDS = [6354186398]  # Replace with admin IDs

# Subscription plans (1/3/6/12 months)
SUBSCRIPTION_PLANS = {
    "1month": {
        "name": "1 месяц",
        "price": 299,
        "duration_days": 30,
        "emoji": "📅",
        "description": "Базовый доступ на месяц"
    },
    "3months": {
        "name": "3 месяца", 
        "price": 799,
        "duration_days": 90,
        "emoji": "🗓️",
        "description": "Популярный тариф со скидкой"
    },
    "6months": {
        "name": "6 месяцев",
        "price": 1399,
        "duration_days": 180,
        "emoji": "📆", 
        "description": "Выгодный полугодовой доступ"
    },
    "12months": {
        "name": "12 месяцев",
        "price": 2499,
        "duration_days": 365,
        "emoji": "🗓️",
        "description": "Максимальная выгода на год"
    }
}

# Payment methods (3 types as requested)
PAYMENT_METHODS = {
    "card_ru": {
        "name": "Карта РФ",
        "emoji": "💳",
        "description": "Оплата российской картой"
    },
    "card_foreign": {
        "name": "Карта иностранного банка",
        "emoji": "🌍", 
        "description": "Оплата зарубежной картой"
    },
    "crypto": {
        "name": "Криптовалюта",
        "emoji": "₿",
        "description": "Оплата криптовалютой"
    }
}

# Database functions
def get_db_connection():
    """Get database connection"""
    return sqlite3.connect("/home/<USER>/app/payments.db")

def get_user_subscription(user_id: int) -> Optional[Dict]:
    """Get users active subscription"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM subscriptions 
            WHERE user_id = ? AND is_active = 1 AND end_date > datetime("now")
            ORDER BY end_date DESC LIMIT 1
        """, (user_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                "id": result[0],
                "user_id": result[1],
                "plan_type": result[2],
                "start_date": result[3],
                "end_date": result[4],
                "is_active": result[5]
            }
        return None
    except Exception as e:
        logger.error(f"Error getting user subscription: {e}")
        return None

def create_user_if_not_exists(user_id: int, username: str = None, first_name: str = None, last_name: str = None):
    """Create user in database if not exists"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR IGNORE INTO users (user_id, username, first_name, last_name)
            VALUES (?, ?, ?, ?)
        """, (user_id, username, first_name, last_name))
        
        conn.commit()
        conn.close()
    except Exception as e:
        logger.error(f"Error creating user: {e}")

# Lava.top integration functions
def create_lava_payment(amount: float, plan_id: str, method: str, user_id: int) -> Optional[str]:
    """Create payment link via Lava.top API"""
    try:
        # Lava.top API configuration
        api_key = Config.LAVA_API_KEY
        secret_key = Config.LAVA_SECRET_KEY
        
        # Payment data
        payment_data = {
            "amount": amount,
            "order_id": f"{user_id}_{plan_id}_{int(time.time())}",
            "hook_url": f"https://your-domain.com/webhook/lava",  # Replace with your webhook URL
            "success_url": f"https://t.me/rinadzhi_bot",
            "fail_url": f"https://t.me/rinadzhi_bot",
            "expire": 3600,  # 1 hour
            "custom_fields": json.dumps({
                "user_id": user_id,
                "plan_id": plan_id,
                "method": method
            })
        }
        
        # Create payment request
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            "https://api.lava.top/business/invoice/create",
            json=payment_data,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                return result.get("data", {}).get("url")
        
        logger.error(f"Lava.top API error: {response.text}")
        return None
        
    except Exception as e:
        logger.error(f"Error creating Lava payment: {e}")
        return None

# Keyboard functions
def create_main_keyboard():
    """Create main menu keyboard with Russian text"""
    keyboard = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    
    btn_buy = types.KeyboardButton("🛒 Купить подписку")
    btn_status = types.KeyboardButton("📊 Статус подписки")
    btn_about = types.KeyboardButton("ℹ️ О канале")
    btn_help = types.KeyboardButton("❓ Задать вопрос")
    
    keyboard.add(btn_buy, btn_status)
    keyboard.add(btn_about, btn_help)
    
    return keyboard

def create_subscription_plans_keyboard():
    """Create subscription plans keyboard with 4 plans"""
    keyboard = types.InlineKeyboardMarkup()
    
    for plan_id, plan in SUBSCRIPTION_PLANS.items():
        # Calculate discount
        discount = ""
        if plan_id == "3months":
            discount = " 🔥 Скидка 33%"
        elif plan_id == "6months":
            discount = " 🔥 Скидка 42%"
        elif plan_id == "12months":
            discount = " 🔥 Скидка 58%"
            
        btn_text = f"{plan['emoji']} {plan['name']} - {plan['price']} ₽{discount}"
        btn = types.InlineKeyboardButton(btn_text, callback_data=f"plan_{plan_id}")
        keyboard.add(btn)
    
    return keyboard

def create_payment_methods_keyboard(plan_id: str):
    """Create payment methods keyboard with 3 options"""
    keyboard = types.InlineKeyboardMarkup()
    
    for method_id, method in PAYMENT_METHODS.items():
        btn_text = f"{method['emoji']} {method['name']}"
        btn = types.InlineKeyboardButton(btn_text, callback_data=f"pay_{plan_id}_{method_id}")
        keyboard.add(btn)
    
    # Back button
    back_btn = types.InlineKeyboardButton("⬅️ Назад к тарифам", callback_data="back_to_plans")
    keyboard.add(back_btn)
    
    return keyboard

# Bot handlers
@bot.message_handler(commands=["start"])
def handle_start(message):
    """Handle /start command"""
    try:
        user_id = message.from_user.id
        username = message.from_user.username or "Unknown"
        first_name = message.from_user.first_name or "User"
        last_name = message.from_user.last_name or ""
        
        logger.info(f"User {user_id} (@{username}) started the bot")
        
        # Create user in database
        create_user_if_not_exists(user_id, username, first_name, last_name)
        
        welcome_text = """🎉 <b>Добро пожаловать в бот канала Ринаджи!</b>

🔥 Здесь вы можете оформить подписку на эксклюзивный контент и получить доступ к закрытому каналу.

📋 <b>Доступные команды:</b>
• Купить подписку - Выбрать тариф и оплатить
• Статус подписки - Проверить активную подписку  
• О канале - Информация о контенте
• Задать вопрос - Связаться с поддержкой

👇 <b>Выберите действие из меню ниже:</b>"""
        
        # Send welcome message with keyboard
        bot.send_message(
            message.chat.id,
            welcome_text,
            reply_markup=create_main_keyboard(),
            parse_mode="HTML"
        )
        
    except Exception as e:
        logger.error(f"Error in start handler: {e}")
        bot.send_message(message.chat.id, "Произошла ошибка. Попробуйте еще раз.")

@bot.message_handler(commands=["buy"])
def handle_buy(message):
    """Handle /buy command"""
    try:
        buy_text = """🛒 <b>Выберите план подписки:</b>

💰 <b>Доступные тарифы:</b>
📅 1 месяц - 299 ₽
🗓️ 3 месяца - 799 ₽ (экономия 33% от месячной оплаты)
📆 6 месяцев - 1,399 ₽ (экономия 42% от месячной оплаты)
🗓️ 12 месяцев - 2,499 ₽ (экономия 58% от месячной оплаты)

✨ <b>Все планы включают:</b>
• Полный доступ к эксклюзивному контенту
• Премиум аналитика и инсайты
• Торговые сигналы и стратегии
• Доступ к активному сообществу
• Обучающие материалы

👇 <b>Выберите подходящий план:</b>"""

        bot.send_message(
            message.chat.id,
            buy_text,
            reply_markup=create_subscription_plans_keyboard(),
            parse_mode="HTML"
        )
    except Exception as e:
        logger.error(f"Error in buy handler: {e}")
        bot.send_message(message.chat.id, "Произошла ошибка. Попробуйте еще раз.")

@bot.message_handler(commands=["status"])
def handle_status(message):
    """Handle /status command"""
    try:
        user_id = message.from_user.id
        subscription = get_user_subscription(user_id)

        if subscription:
            end_date = datetime.strptime(subscription["end_date"], "%Y-%m-%d %H:%M:%S")
            days_left = (end_date - datetime.now()).days

            status_text = f"""📊 <b>Статус вашей подписки</b>

✅ <b>Статус:</b> Активна
📋 <b>План:</b> {subscription["plan_type"]}
📅 <b>Действует до:</b> {end_date.strftime("%d.%m.%Y")}
⏰ <b>Осталось дней:</b> {days_left}

🔥 У вас есть полный доступ к каналу!"""
        else:
            status_text = f"""📊 <b>Статус вашей подписки</b>

❌ <b>Статус:</b> Подписка не активна
💡 <b>Для активации:</b> Используйте команду "Купить подписку"

📅 <b>Дата регистрации:</b> {datetime.now().strftime("%d.%m.%Y")}

🛒 Оформите подписку для доступа к эксклюзивному контенту!"""

        bot.send_message(message.chat.id, status_text, parse_mode="HTML")

    except Exception as e:
        logger.error(f"Error in status handler: {e}")
        bot.send_message(message.chat.id, "Произошла ошибка. Попробуйте еще раз.")

@bot.message_handler(commands=["about"])
def handle_about(message):
    """Handle /about command"""
    try:
        about_text = """📺 <b>О канале Ринаджи</b>

🎯 <b>Что вас ждет:</b>
• 💎 Эксклюзивный контент и рыночная аналитика
• 🔥 Ежедневные обновления и торговые инсайты
• 📈 Профессиональные торговые сигналы
• 👥 Активное сообщество трейдеров
• 🎓 Обучающий контент и вебинары
• 📊 Исследования рынка и аналитика

💰 <b>Тарифные планы:</b>
📅 1 месяц - 299 ₽
🗓️ 3 месяца - 799 ₽ (лучшее соотношение!)
📆 6 месяцев - 1,399 ₽ (отличная экономия!)
🗓️ 12 месяцев - 2,499 ₽ (максимальная выгода!)

🚀 Присоединяйтесь к тысячам успешных трейдеров!"""

        bot.send_message(message.chat.id, about_text, parse_mode="HTML")
    except Exception as e:
        logger.error(f"Error in about handler: {e}")
        bot.send_message(message.chat.id, "Произошла ошибка. Попробуйте еще раз.")

@bot.message_handler(commands=["help"])
def handle_help(message):
    """Handle /help command"""
    try:
        help_text = """❓ <b>Часто задаваемые вопросы</b>

<b>Q: Как оформить подписку?</b>
A: Нажмите "Купить подписку", выберите план и способ оплаты

<b>Q: Какие способы оплаты доступны?</b>
A: Карты РФ, международные карты, криптовалюта

<b>Q: Когда я получу доступ?</b>
A: Сразу после успешной оплаты (обычно 1-5 минут)

<b>Q: Как проверить статус подписки?</b>
A: Используйте команду "Статус подписки"

<b>Q: Проблемы с оплатой?</b>
A: Обратитесь в службу поддержки @rinadzhi_support

<b>Q: Можно ли отменить подписку?</b>
A: Да, обратитесь в поддержку @rinadzhi_support

💬 <b>Нужна помощь? Пишите @rinadzhi_support</b>"""

        bot.send_message(message.chat.id, help_text, parse_mode="HTML")
    except Exception as e:
        logger.error(f"Error in help handler: {e}")
        bot.send_message(message.chat.id, "Произошла ошибка. Попробуйте еще раз.")

# Handle text messages (keyboard buttons)
@bot.message_handler(content_types=["text"])
def handle_text(message):
    """Handle text messages"""
    try:
        text = message.text

        if text == "🛒 Купить подписку":
            handle_buy(message)
        elif text == "📊 Статус подписки":
            handle_status(message)
        elif text == "ℹ️ О канале":
            handle_about(message)
        elif text == "❓ Задать вопрос":
            handle_help(message)
        else:
            bot.send_message(
                message.chat.id,
                "Пожалуйста, используйте кнопки меню или команду /start для начала работы.",
                reply_markup=create_main_keyboard()
            )

    except Exception as e:
        logger.error(f"Error in text handler: {e}")
        bot.send_message(message.chat.id, "Произошла ошибка. Попробуйте еще раз.")

# Handle callback queries (inline buttons)
@bot.callback_query_handler(func=lambda call: True)
def handle_callback(call):
    """Handle callback queries"""
    try:
        if call.data.startswith("plan_"):
            plan_id = call.data.replace("plan_", "")
            plan = SUBSCRIPTION_PLANS.get(plan_id)

            if plan:
                payment_text = f"""💳 <b>Оплата подписки "{plan["name"]}"</b>

📋 <b>План:</b> {plan["name"]}
💰 <b>Стоимость:</b> {plan["price"]} ₽
📝 <b>Описание:</b> {plan["description"]}
⏰ <b>Длительность:</b> {plan["duration_days"]} дней

🔥 <b>Выберите способ оплаты:</b>"""

                bot.edit_message_text(
                    payment_text,
                    call.message.chat.id,
                    call.message.message_id,
                    reply_markup=create_payment_methods_keyboard(plan_id),
                    parse_mode="HTML"
                )

        elif call.data.startswith("pay_"):
            parts = call.data.split("_")
            plan_id = parts[1]
            method_id = parts[2]

            plan = SUBSCRIPTION_PLANS.get(plan_id)
            method = PAYMENT_METHODS.get(method_id)

            if plan and method:
                user_id = call.from_user.id

                # Create payment link via Lava.top
                payment_url = create_lava_payment(
                    plan["price"],
                    plan_id,
                    method_id,
                    user_id
                )

                if payment_url:
                    payment_link_text = f"""🔗 <b>Ссылка для оплаты создана!</b>

📋 <b>План:</b> {plan["name"]}
💰 <b>Стоимость:</b> {plan["price"]} ₽
💳 <b>Способ оплаты:</b> {method["name"]}

👇 <b>Нажмите кнопку ниже для оплаты:</b>

⚠️ <b>Важно:</b>
• Ссылка действительна 1 час
• Доступ откроется сразу после оплаты
• Сохраните ссылку, если нужно вернуться к ней

🔥 После оплаты вы автоматически получите доступ к каналу!"""

                    # Create payment button
                    payment_keyboard = types.InlineKeyboardMarkup()
                    pay_btn = types.InlineKeyboardButton(
                        f"💳 Оплатить {plan['price']} ₽",
                        url=payment_url
                    )
                    back_btn = types.InlineKeyboardButton(
                        "⬅️ Назад к тарифам",
                        callback_data="back_to_plans"
                    )
                    payment_keyboard.add(pay_btn)
                    payment_keyboard.add(back_btn)

                    bot.edit_message_text(
                        payment_link_text,
                        call.message.chat.id,
                        call.message.message_id,
                        reply_markup=payment_keyboard,
                        parse_mode="HTML"
                    )
                else:
                    # Fallback if Lava.top is not available
                    payment_link_text = f"""⚠️ <b>Система оплаты временно недоступна</b>

📋 <b>План:</b> {plan["name"]}
💰 <b>Стоимость:</b> {plan["price"]} ₽
💳 <b>Способ оплаты:</b> {method["name"]}

🔧 Пожалуйста, попробуйте через несколько минут или обратитесь в поддержку.

💬 <b>Поддержка:</b> @rinadzhi_support"""

                    bot.edit_message_text(
                        payment_link_text,
                        call.message.chat.id,
                        call.message.message_id,
                        parse_mode="HTML"
                    )

        elif call.data == "back_to_plans":
            handle_buy_callback(call)

        bot.answer_callback_query(call.id)

    except Exception as e:
        logger.error(f"Error in callback handler: {e}")
        bot.answer_callback_query(call.id, "Произошла ошибка")

def handle_buy_callback(call):
    """Handle buy callback"""
    buy_text = """🛒 <b>Выберите план подписки:</b>

💰 <b>Доступные тарифы:</b>
📅 1 месяц - 299 ₽
🗓️ 3 месяца - 799 ₽ (экономия 33% от месячной оплаты)
📆 6 месяцев - 1,399 ₽ (экономия 42% от месячной оплаты)
🗓️ 12 месяцев - 2,499 ₽ (экономия 58% от месячной оплаты)

👇 <b>Выберите подходящий план:</b>"""

    bot.edit_message_text(
        buy_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=create_subscription_plans_keyboard(),
        parse_mode="HTML"
    )

def main():
    """Main function to start the bot"""
    logger.info("Starting Rinadzhi Telegram Payment Bot...")

    try:
        # Set bot commands
        commands = [
            types.BotCommand("start", "Начать работу с ботом"),
            types.BotCommand("buy", "Купить подписку"),
            types.BotCommand("status", "Статус подписки"),
            types.BotCommand("about", "О канале"),
            types.BotCommand("help", "Задать вопрос"),
        ]
        bot.set_my_commands(commands)

        logger.info("Bot commands set successfully")

        # Start polling
        logger.info("Bot started successfully! Listening for messages...")
        bot.infinity_polling(none_stop=True, interval=1)

    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        time.sleep(5)
        main()  # Restart on error

if __name__ == "__main__":
    main()
