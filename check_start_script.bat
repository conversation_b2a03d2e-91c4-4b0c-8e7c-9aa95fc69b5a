@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Checking for start.sh files...
echo.
echo Checking /home/<USER>/app/start.sh:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "ls -la /home/<USER>/app/start.sh 2>/dev/null || echo 'File not found'"
echo.
echo Checking /opt/telegram-payment-bot/app/start.sh:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "ls -la /opt/telegram-payment-bot/app/start.sh 2>/dev/null || echo 'File not found'"
echo.
echo Checking all files in /opt/telegram-payment-bot/app/:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "ls -la /opt/telegram-payment-bot/app/"
echo Done.