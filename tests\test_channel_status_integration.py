"""
Интеграционные тесты для проверки статуса участников канала
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from app.services.channel_manager import ChannelManager
from app.services.bot_handler import TelegramBotHandler
from app.models.database import DatabaseService
from app.services.payment_service import PaymentService
from app.models.models import User, Subscription

class TestChannelStatusIntegration(unittest.TestCase):
    """Тесты для интеграции проверки статуса канала"""
    
    def setUp(self):
        """Настройка тестов"""
        self.mock_db_service = Mock(spec=DatabaseService)
        self.mock_payment_service = Mock(spec=PaymentService)
        self.mock_channel_manager = Mock(spec=ChannelManager)
        
        # Создаем bot handler с мокнутыми зависимостями
        with patch('app.services.bot_handler.telebot.TeleBot'):
            self.bot_handler = TelegramBotHandler(
                db_service=self.mock_db_service,
                payment_service=self.mock_payment_service,
                channel_manager=self.mock_channel_manager
            )
    
    def test_get_subscription_status_with_channel_check_active_in_channel(self):
        """Тест получения статуса активной подписки с пользователем в канале"""
        # Настройка данных
        user_id = 123456789
        subscription = Mock()
        subscription.days_until_expiry.return_value = 15
        
        # Пользователь в канале
        self.mock_channel_manager.check_user_in_channel.return_value = True
        
        # Выполнение теста
        status_emoji, status_text, channel_status, is_in_channel = self.bot_handler._get_subscription_status_with_channel_check(
            user_id, subscription
        )
        
        # Проверки
        self.assertEqual(status_emoji, "✅")
        self.assertEqual(status_text, "Активна")
        self.assertEqual(channel_status, "✅ В канале")
        self.assertTrue(is_in_channel)
        self.mock_channel_manager.check_user_in_channel.assert_called_once_with(user_id)
    
    def test_get_subscription_status_with_channel_check_active_not_in_channel(self):
        """Тест получения статуса активной подписки с пользователем не в канале"""
        # Настройка данных
        user_id = 123456789
        subscription = Mock()
        subscription.days_until_expiry.return_value = 15
        
        # Пользователь не в канале
        self.mock_channel_manager.check_user_in_channel.return_value = False
        
        # Выполнение теста
        status_emoji, status_text, channel_status, is_in_channel = self.bot_handler._get_subscription_status_with_channel_check(
            user_id, subscription
        )
        
        # Проверки
        self.assertEqual(status_emoji, "⚠️")
        self.assertEqual(status_text, "Активна (не в канале)")
        self.assertEqual(channel_status, "❌ Не в канале")
        self.assertFalse(is_in_channel)
        self.mock_channel_manager.check_user_in_channel.assert_called_once_with(user_id)
    
    def test_get_subscription_status_with_channel_check_expiring_soon(self):
        """Тест получения статуса подписки, истекающей скоро"""
        # Настройка данных
        user_id = 123456789
        subscription = Mock()
        subscription.days_until_expiry.return_value = 3  # Скоро истечет
        
        # Пользователь в канале
        self.mock_channel_manager.check_user_in_channel.return_value = True
        
        # Выполнение теста
        status_emoji, status_text, channel_status, is_in_channel = self.bot_handler._get_subscription_status_with_channel_check(
            user_id, subscription
        )
        
        # Проверки
        self.assertEqual(status_emoji, "⚠️")
        self.assertEqual(status_text, "Скоро истечет")
        self.assertEqual(channel_status, "✅ В канале")
        self.assertTrue(is_in_channel)
    
    def test_get_subscription_status_with_channel_check_expiring_today(self):
        """Тест получения статуса подписки, истекающей сегодня"""
        # Настройка данных
        user_id = 123456789
        subscription = Mock()
        subscription.days_until_expiry.return_value = 0  # Истекает сегодня
        
        # Пользователь не в канале
        self.mock_channel_manager.check_user_in_channel.return_value = False
        
        # Выполнение теста
        status_emoji, status_text, channel_status, is_in_channel = self.bot_handler._get_subscription_status_with_channel_check(
            user_id, subscription
        )
        
        # Проверки
        self.assertEqual(status_emoji, "🔴")
        self.assertEqual(status_text, "Истекает сегодня (не в канале)")
        self.assertEqual(channel_status, "❌ Не в канале")
        self.assertFalse(is_in_channel)
    
    def test_get_status_advice_not_in_channel(self):
        """Тест получения совета для пользователя не в канале"""
        advice = self.bot_handler._get_status_advice(15, is_in_channel=False)
        
        self.assertEqual(
            advice, 
            "⚠️ Вы не находитесь в канале! Обратитесь в поддержку для получения новой пригласительной ссылки."
        )
    
    def test_get_status_advice_in_channel_active(self):
        """Тест получения совета для активной подписки в канале"""
        advice = self.bot_handler._get_status_advice(45, is_in_channel=True)
        
        self.assertEqual(advice, "✅ Ваша подписка активна. Наслаждайтесь контентом!")
    
    def test_get_status_advice_in_channel_expiring_soon(self):
        """Тест получения совета для подписки, истекающей скоро"""
        advice = self.bot_handler._get_status_advice(3, is_in_channel=True)
        
        self.assertEqual(
            advice, 
            "🔴 Подписка скоро истечет! Продлите её, чтобы сохранить доступ к каналу."
        )
    
    def test_get_status_advice_in_channel_expiring_today(self):
        """Тест получения совета для подписки, истекающей сегодня"""
        advice = self.bot_handler._get_status_advice(0, is_in_channel=True)
        
        self.assertEqual(advice, "🚨 Подписка истекает сегодня! Продлите её прямо сейчас.")
    
    def test_create_status_keyboard_with_channel_status(self):
        """Тест создания клавиатуры статуса с учетом статуса канала"""
        # Тест для пользователя в канале с активной подпиской
        keyboard = self.bot_handler._create_status_keyboard(15, is_in_channel=True)
        
        # Проверяем, что клавиатура создана
        self.assertIsNotNone(keyboard)
        
        # Тест для пользователя не в канале с истекающей подпиской
        keyboard = self.bot_handler._create_status_keyboard(3, is_in_channel=False)
        
        # Проверяем, что клавиатура создана
        self.assertIsNotNone(keyboard)
    
    @patch('app.services.bot_handler.telebot.TeleBot')
    def test_channel_manager_integration_in_bot_handler(self, mock_telebot):
        """Тест интеграции channel manager в bot handler"""
        # Создаем реальный channel manager для теста
        real_channel_manager = ChannelManager(
            bot_token="test_token",
            channel_id="@test_channel"
        )
        
        # Создаем bot handler с реальным channel manager
        bot_handler = TelegramBotHandler(
            db_service=self.mock_db_service,
            payment_service=self.mock_payment_service,
            channel_manager=real_channel_manager
        )
        
        # Проверяем, что channel manager правильно инициализирован
        self.assertIsNotNone(bot_handler.channel_manager)
        self.assertEqual(bot_handler.channel_manager.channel_id, "@test_channel")
    
    def test_channel_status_check_error_handling(self):
        """Тест обработки ошибок при проверке статуса канала"""
        # Настройка данных
        user_id = 123456789
        subscription = Mock()
        subscription.days_until_expiry.return_value = 15
        
        # Имитируем ошибку при проверке канала
        self.mock_channel_manager.check_user_in_channel.side_effect = Exception("API Error")
        
        # Выполнение теста - метод должен обработать ошибку gracefully
        try:
            status_emoji, status_text, channel_status, is_in_channel = self.bot_handler._get_subscription_status_with_channel_check(
                user_id, subscription
            )
            # Если ошибка обработана, проверяем результат
            # В случае ошибки должен возвращаться статус "не в канале"
            self.assertFalse(is_in_channel)
        except Exception:
            # Если ошибка не обработана, тест провален
            self.fail("Метод должен обрабатывать ошибки проверки канала")

if __name__ == '__main__':
    unittest.main()