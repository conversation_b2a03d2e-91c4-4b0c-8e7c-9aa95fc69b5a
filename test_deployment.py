#!/usr/bin/env python3
"""
Скрипт для тестирования админ-панели после обновления
"""

import requests
import sys
import time
from urllib3 import disable_warnings
from urllib3.exceptions import InsecureRequestWarning

# Отключаем предупреждения SSL
disable_warnings(InsecureRequestWarning)

def test_admin_panel():
    """Тестирует работу админ-панели"""
    base_url = "https://195.49.212.172"
    
    print("🧪 Тестирование админ-панели после обновления...")
    
    # Создаем сессию
    session = requests.Session()
    session.verify = False
    
    tests = [
        ("Доступность страницы входа", test_login_page),
        ("Вход в систему", test_login),
        ("Доступность дашборда", test_dashboard),
        ("Страница пользователей", test_users_page),
        ("Страница платежей", test_payments_page),
        ("Страница логов", test_logs_page),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            result = test_func(session, base_url)
            if result:
                print(f"✅ {test_name} - УСПЕШНО")
                results.append((test_name, True, None))
            else:
                print(f"❌ {test_name} - НЕУДАЧНО")
                results.append((test_name, False, "Тест не прошел"))
        except Exception as e:
            print(f"💥 {test_name} - ОШИБКА: {e}")
            results.append((test_name, False, str(e)))
        
        time.sleep(1)  # Пауза между тестами
    
    # Выводим итоги
    print("\n" + "="*50)
    print("📊 ИТОГИ ТЕСТИРОВАНИЯ")
    print("="*50)
    
    passed = 0
    failed = 0
    
    for test_name, success, error in results:
        status = "✅ ПРОШЕЛ" if success else "❌ ПРОВАЛЕН"
        print(f"{status:<12} {test_name}")
        if not success and error:
            print(f"             Ошибка: {error}")
        
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 Результат: {passed} прошли, {failed} провалены")
    
    if failed == 0:
        print("🎉 Все тесты прошли успешно! Админ-панель работает корректно.")
        return True
    else:
        print("⚠️ Некоторые тесты провалены. Требуется дополнительная диагностика.")
        return False

def test_login_page(session, base_url):
    """Тест доступности страницы входа"""
    response = session.get(f"{base_url}/admin/login", timeout=10)
    return response.status_code == 200 and "Пароль" in response.text

def test_login(session, base_url):
    """Тест входа в систему"""
    # Получаем страницу входа
    login_page = session.get(f"{base_url}/admin/login")
    if login_page.status_code != 200:
        return False
    
    # Ищем CSRF токен
    csrf_token = None
    if 'csrf_token' in login_page.text:
        import re
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
    
    # Данные для входа
    login_data = {
        'password': 'admin123',
        'submit': 'Войти'
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    # Пытаемся войти
    response = session.post(f"{base_url}/admin/login", data=login_data, allow_redirects=False)
    return response.status_code == 302 and '/admin/dashboard' in response.headers.get('Location', '')

def test_dashboard(session, base_url):
    """Тест доступности дашборда"""
    response = session.get(f"{base_url}/admin/dashboard", timeout=10)
    return response.status_code == 200 and ("Панель управления" in response.text or "dashboard" in response.text)

def test_users_page(session, base_url):
    """Тест страницы пользователей"""
    response = session.get(f"{base_url}/admin/users", timeout=10)
    return response.status_code == 200

def test_payments_page(session, base_url):
    """Тест страницы платежей"""
    response = session.get(f"{base_url}/admin/payments", timeout=10)
    return response.status_code == 200

def test_logs_page(session, base_url):
    """Тест страницы логов"""
    response = session.get(f"{base_url}/admin/logs", timeout=10)
    return response.status_code == 200

def quick_test():
    """Быстрый тест доступности"""
    print("⚡ Быстрый тест доступности...")
    
    try:
        response = requests.get("https://195.49.212.172/admin/login", 
                              verify=False, timeout=5)
        if response.status_code == 200:
            print("✅ Сервер доступен")
            return True
        else:
            print(f"❌ Сервер вернул код: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Сервер недоступен: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Запуск тестирования обновленной админ-панели")
    print("="*50)
    
    # Сначала быстрый тест
    if not quick_test():
        print("\n💥 Сервер недоступен. Проверьте:")
        print("1. Запущен ли сервис: sudo systemctl status telegram-bot")
        print("2. Логи сервиса: sudo journalctl -u telegram-bot -n 20")
        print("3. Сетевое подключение к серверу")
        sys.exit(1)
    
    # Полное тестирование
    success = test_admin_panel()
    
    if success:
        print("\n🎊 ОБНОВЛЕНИЕ УСПЕШНО ЗАВЕРШЕНО!")
        print("Админ-панель работает корректно.")
        sys.exit(0)
    else:
        print("\n🔧 ТРЕБУЕТСЯ ДОПОЛНИТЕЛЬНАЯ НАСТРОЙКА")
        print("Проверьте логи сервера для диагностики проблем.")
        sys.exit(1)