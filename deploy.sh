#!/bin/bash

# Скрипт автоматического обновления админ-панели на сервере
# Использование: ./deploy.sh

set -e  # Остановить выполнение при ошибке

SERVER="**************"
USER="ubuntu"
PROJECT_DIR="/home/<USER>/telegram_bot"
SERVICE_NAME="telegram-bot"

echo "🚀 Начинаем обновление админ-панели на сервере..."

# Функция для выполнения команд на сервере
run_remote() {
    ssh -o StrictHostKeyChecking=no ${USER}@${SERVER} "$1"
}

# Функция для загрузки файлов на сервер
upload_file() {
    scp -o StrictHostKeyChecking=no "$1" ${USER}@${SERVER}:"$2"
}

echo "📦 Создание архива с обновлениями..."
if [ -f "admin_panel_fix.tar.gz" ]; then
    rm admin_panel_fix.tar.gz
fi
tar -czf admin_panel_fix.tar.gz app/admin.py templates/

echo "📤 Загрузка архива на сервер..."
upload_file "admin_panel_fix.tar.gz" "/home/<USER>/"

echo "🛑 Остановка сервиса..."
run_remote "sudo systemctl stop ${SERVICE_NAME}"

echo "💾 Создание резервной копии..."
run_remote "cd ${PROJECT_DIR} && sudo cp -r app templates backup_\$(date +%Y%m%d_%H%M%S)"

echo "📥 Распаковка обновлений..."
run_remote "cd ${PROJECT_DIR} && tar -xzf /home/<USER>/admin_panel_fix.tar.gz"

echo "🔐 Установка прав доступа..."
run_remote "cd ${PROJECT_DIR} && sudo chown -R ubuntu:ubuntu app templates && sudo chmod -R 755 app templates"

echo "✅ Проверка обновлений..."
run_remote "cd ${PROJECT_DIR} && grep -q 'safe_url_for' app/admin.py && echo 'admin.py обновлен' || echo 'ОШИБКА: admin.py не обновлен'"
run_remote "cd ${PROJECT_DIR} && grep -q 'safe_url_for' templates/base.html && echo 'base.html обновлен' || echo 'ОШИБКА: base.html не обновлен'"

echo "🚀 Запуск сервиса..."
run_remote "sudo systemctl start ${SERVICE_NAME}"

echo "⏳ Ожидание запуска сервиса..."
sleep 5

echo "📊 Проверка статуса сервиса..."
run_remote "sudo systemctl status ${SERVICE_NAME} --no-pager -l"

echo "📝 Последние логи сервиса:"
run_remote "sudo journalctl -u ${SERVICE_NAME} -n 10 --no-pager"

echo ""
echo "🎉 Обновление завершено!"
echo ""
echo "📋 Следующие шаги:"
echo "1. Откройте https://${SERVER}/admin/login"
echo "2. Введите пароль: admin123"
echo "3. Проверьте работу админ-панели"
echo ""
echo "🔍 Для просмотра логов в реальном времени:"
echo "ssh ${USER}@${SERVER} 'sudo journalctl -u ${SERVICE_NAME} -f'"
echo ""
echo "🆘 При проблемах:"
echo "ssh ${USER}@${SERVER} 'sudo systemctl restart ${SERVICE_NAME}'"