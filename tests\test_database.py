"""
Тесты для модуля базы данных
"""

import unittest
import tempfile
import os
import sqlite3
from datetime import datetime, timedelta
from decimal import Decimal
from app.models.database import DatabaseManager, DatabaseService, User, Subscription, Payment, AdminLog

class TestDatabaseManager(unittest.TestCase):
    """Тесты для DatabaseManager"""
    
    def setUp(self):
        """Настройка тестового окружения"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_manager = DatabaseManager(self.temp_db.name)
    
    def tearDown(self):
        """Очистка после тестов"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_init_database(self):
        """Тест инициализации базы данных"""
        # Инициализируем базу данных
        self.db_manager.init_database()
        
        # Проверяем, что все таблицы созданы
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('users', 'subscriptions', 'payments', 'admin_logs')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
        expected_tables = ['users', 'subscriptions', 'payments', 'admin_logs']
        for table in expected_tables:
            self.assertIn(table, tables, f"Таблица {table} не была создана")
    
    def test_database_schema_users(self):
        """Тест схемы таблицы users"""
        self.db_manager.init_database()
        
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(users)")
            columns = {row[1]: row[2] for row in cursor.fetchall()}
            
        expected_columns = {
            'id': 'INTEGER',
            'telegram_id': 'INTEGER',
            'username': 'TEXT',
            'first_name': 'TEXT',
            'last_name': 'TEXT',
            'created_at': 'TIMESTAMP',
            'updated_at': 'TIMESTAMP'
        }
        
        for col_name, col_type in expected_columns.items():
            self.assertIn(col_name, columns, f"Колонка {col_name} отсутствует в таблице users")
    
    def test_database_schema_subscriptions(self):
        """Тест схемы таблицы subscriptions"""
        self.db_manager.init_database()
        
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(subscriptions)")
            columns = {row[1]: row[2] for row in cursor.fetchall()}
            
        expected_columns = {
            'id': 'INTEGER',
            'user_id': 'INTEGER',
            'plan_type': 'TEXT',
            'status': 'TEXT',
            'start_date': 'TIMESTAMP',
            'end_date': 'TIMESTAMP',
            'created_at': 'TIMESTAMP',
            'updated_at': 'TIMESTAMP'
        }
        
        for col_name, col_type in expected_columns.items():
            self.assertIn(col_name, columns, f"Колонка {col_name} отсутствует в таблице subscriptions")
    
    def test_database_schema_payments(self):
        """Тест схемы таблицы payments"""
        self.db_manager.init_database()
        
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(payments)")
            columns = {row[1]: row[2] for row in cursor.fetchall()}
            
        expected_columns = {
            'id': 'INTEGER',
            'user_id': 'INTEGER',
            'subscription_id': 'INTEGER',
            'lava_invoice_id': 'TEXT',
            'amount': 'DECIMAL(10,2)',
            'currency': 'TEXT',
            'payment_method': 'TEXT',
            'status': 'TEXT',
            'payment_url': 'TEXT',
            'created_at': 'TIMESTAMP',
            'completed_at': 'TIMESTAMP',
            'expires_at': 'TIMESTAMP'
        }
        
        for col_name in expected_columns.keys():
            self.assertIn(col_name, columns, f"Колонка {col_name} отсутствует в таблице payments")
    
    def test_database_indexes(self):
        """Тест создания индексов"""
        self.db_manager.init_database()
        
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
            indexes = [row[0] for row in cursor.fetchall()]
            
        expected_indexes = [
            'idx_users_telegram_id',
            'idx_subscriptions_user_id',
            'idx_subscriptions_status',
            'idx_subscriptions_end_date',
            'idx_payments_user_id',
            'idx_payments_status',
            'idx_payments_lava_invoice_id',
            'idx_admin_logs_admin_id'
        ]
        
        for index in expected_indexes:
            self.assertIn(index, indexes, f"Индекс {index} не был создан")
    
    def test_migrate_database(self):
        """Тест миграции базы данных"""
        # Сначала инициализируем базу
        self.db_manager.init_database()
        
        # Затем запускаем миграцию
        self.db_manager.migrate_database()
        
        # Проверяем, что все таблицы на месте
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('users', 'subscriptions', 'payments', 'admin_logs')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
        self.assertEqual(len(tables), 4, "Не все таблицы присутствуют после миграции")
    
    def test_check_database_health(self):
        """Тест проверки состояния базы данных"""
        # До инициализации база может не работать
        # После инициализации должна работать
        self.db_manager.init_database()
        self.assertTrue(self.db_manager.check_database_health(), "База данных не прошла проверку здоровья")
    
    def test_foreign_key_constraints(self):
        """Тест внешних ключей"""
        self.db_manager.init_database()
        
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # Включаем проверку внешних ключей
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # Создаем пользователя
            cursor.execute("""
                INSERT INTO users (telegram_id, username, first_name) 
                VALUES (12345, 'testuser', 'Test')
            """)
            user_id = cursor.lastrowid
            
            # Создаем подписку
            cursor.execute("""
                INSERT INTO subscriptions (user_id, plan_type, end_date) 
                VALUES (?, 'monthly', datetime('now', '+1 month'))
            """, (user_id,))
            
            # Проверяем, что подписка создалась
            cursor.execute("SELECT COUNT(*) FROM subscriptions WHERE user_id = ?", (user_id,))
            count = cursor.fetchone()[0]
            self.assertEqual(count, 1, "Подписка не была создана")

if __name__ == '__main__':
    unittest.main()


class TestDatabaseService(unittest.TestCase):
    """Тесты для DatabaseService CRUD операций"""
    
    def setUp(self):
        """Настройка тестового окружения"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.db_manager.init_database()
        self.db_service = DatabaseService(self.db_manager)
    
    def tearDown(self):
        """Очистка после тестов"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    # ==================== ТЕСТЫ ПОЛЬЗОВАТЕЛЕЙ ====================
    
    def test_create_user(self):
        """Тест создания пользователя"""
        user = self.db_service.create_user(
            telegram_id=12345,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        self.assertIsNotNone(user)
        self.assertEqual(user.telegram_id, 12345)
        self.assertEqual(user.username, "testuser")
        self.assertEqual(user.first_name, "Test")
        self.assertEqual(user.last_name, "User")
        self.assertIsNotNone(user.id)
        self.assertIsNotNone(user.created_at)
    
    def test_create_duplicate_user(self):
        """Тест создания дублирующего пользователя"""
        # Создаем первого пользователя
        user1 = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Пытаемся создать второго с тем же telegram_id
        user2 = self.db_service.create_user(telegram_id=12345, username="testuser2")
        
        # Должен вернуться существующий пользователь
        self.assertEqual(user1.id, user2.id)
        self.assertEqual(user1.telegram_id, user2.telegram_id)
    
    def test_get_user_by_id(self):
        """Тест получения пользователя по ID"""
        # Создаем пользователя
        created_user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Получаем по ID
        user = self.db_service.get_user_by_id(created_user.id)
        
        self.assertIsNotNone(user)
        self.assertEqual(user.id, created_user.id)
        self.assertEqual(user.telegram_id, 12345)
        self.assertEqual(user.username, "testuser")
    
    def test_get_user_by_telegram_id(self):
        """Тест получения пользователя по Telegram ID"""
        # Создаем пользователя
        created_user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Получаем по Telegram ID
        user = self.db_service.get_user_by_telegram_id(12345)
        
        self.assertIsNotNone(user)
        self.assertEqual(user.id, created_user.id)
        self.assertEqual(user.telegram_id, 12345)
        self.assertEqual(user.username, "testuser")
    
    def test_get_nonexistent_user(self):
        """Тест получения несуществующего пользователя"""
        user = self.db_service.get_user_by_id(999)
        self.assertIsNone(user)
        
        user = self.db_service.get_user_by_telegram_id(999)
        self.assertIsNone(user)
    
    def test_update_user(self):
        """Тест обновления пользователя"""
        # Создаем пользователя
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Обновляем данные
        updated = self.db_service.update_user(
            user.id,
            username="newusername",
            first_name="NewName"
        )
        
        self.assertTrue(updated)
        
        # Проверяем обновление
        updated_user = self.db_service.get_user_by_id(user.id)
        self.assertEqual(updated_user.username, "newusername")
        self.assertEqual(updated_user.first_name, "NewName")
    
    def test_get_all_users(self):
        """Тест получения всех пользователей"""
        # Создаем несколько пользователей
        self.db_service.create_user(telegram_id=12345, username="user1")
        self.db_service.create_user(telegram_id=12346, username="user2")
        self.db_service.create_user(telegram_id=12347, username="user3")
        
        # Получаем всех пользователей
        users = self.db_service.get_all_users()
        self.assertEqual(len(users), 3)
        
        # Тест с лимитом
        users_limited = self.db_service.get_all_users(limit=2)
        self.assertEqual(len(users_limited), 2)
    
    # ==================== ТЕСТЫ ПОДПИСОК ====================
    
    def test_create_subscription(self):
        """Тест создания подписки"""
        # Создаем пользователя
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Создаем подписку
        subscription = self.db_service.create_subscription(
            user_id=user.id,
            plan_type="monthly",
            months=1
        )
        
        self.assertIsNotNone(subscription)
        self.assertEqual(subscription.user_id, user.id)
        self.assertEqual(subscription.plan_type, "monthly")
        self.assertEqual(subscription.status, "active")
        self.assertIsNotNone(subscription.start_date)
        self.assertIsNotNone(subscription.end_date)
    
    def test_get_subscription_by_id(self):
        """Тест получения подписки по ID"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        created_sub = self.db_service.create_subscription(user.id, "monthly", 1)
        
        subscription = self.db_service.get_subscription_by_id(created_sub.id)
        
        self.assertIsNotNone(subscription)
        self.assertEqual(subscription.id, created_sub.id)
        self.assertEqual(subscription.user_id, user.id)
    
    def test_get_user_active_subscription(self):
        """Тест получения активной подписки пользователя"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        subscription = self.db_service.create_subscription(user.id, "monthly", 1)
        
        active_sub = self.db_service.get_user_active_subscription(user.id)
        
        self.assertIsNotNone(active_sub)
        self.assertEqual(active_sub.id, subscription.id)
        self.assertEqual(active_sub.status, "active")
    
    def test_get_user_subscriptions(self):
        """Тест получения всех подписок пользователя"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Создаем несколько подписок
        sub1 = self.db_service.create_subscription(user.id, "monthly", 1)
        sub2 = self.db_service.create_subscription(user.id, "yearly", 12)
        
        subscriptions = self.db_service.get_user_subscriptions(user.id)
        
        self.assertEqual(len(subscriptions), 2)
        subscription_ids = [sub.id for sub in subscriptions]
        self.assertIn(sub1.id, subscription_ids)
        self.assertIn(sub2.id, subscription_ids)
    
    def test_update_subscription_status(self):
        """Тест обновления статуса подписки"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        subscription = self.db_service.create_subscription(user.id, "monthly", 1)
        
        # Обновляем статус
        updated = self.db_service.update_subscription_status(subscription.id, "expired")
        self.assertTrue(updated)
        
        # Проверяем обновление
        updated_sub = self.db_service.get_subscription_by_id(subscription.id)
        self.assertEqual(updated_sub.status, "expired")
    
    def test_extend_subscription(self):
        """Тест продления подписки"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        subscription = self.db_service.create_subscription(user.id, "monthly", 1)
        
        original_end_date = subscription.end_date
        
        # Продлеваем на 2 месяца
        extended = self.db_service.extend_subscription(subscription.id, 2)
        self.assertTrue(extended)
        
        # Проверяем продление
        extended_sub = self.db_service.get_subscription_by_id(subscription.id)
        self.assertGreater(extended_sub.end_date, original_end_date)
    
    def test_get_expiring_subscriptions(self):
        """Тест получения истекающих подписок"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Создаем подписку, которая истекает через 3 дня
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO subscriptions (user_id, plan_type, start_date, end_date)
                VALUES (?, 'monthly', datetime('now'), datetime('now', '+3 days'))
            ''', (user.id,))
            conn.commit()
        
        # Получаем истекающие подписки (в течение 7 дней)
        expiring = self.db_service.get_expiring_subscriptions(7)
        self.assertEqual(len(expiring), 1)
        self.assertEqual(expiring[0].user_id, user.id)
    
    # ==================== ТЕСТЫ ПЛАТЕЖЕЙ ====================
    
    def test_create_payment(self):
        """Тест создания платежа"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        payment = self.db_service.create_payment(
            user_id=user.id,
            lava_invoice_id="lava_123",
            amount=Decimal("100.50"),
            payment_method="card_ru",
            payment_url="https://example.com/pay",
            expires_at=datetime.now() + timedelta(hours=1)
        )
        
        self.assertIsNotNone(payment)
        self.assertEqual(payment.user_id, user.id)
        self.assertEqual(payment.lava_invoice_id, "lava_123")
        self.assertEqual(payment.amount, Decimal("100.50"))
        self.assertEqual(payment.payment_method, "card_ru")
        self.assertEqual(payment.status, "pending")
    
    def test_get_payment_by_id(self):
        """Тест получения платежа по ID"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        created_payment = self.db_service.create_payment(
            user.id, "lava_123", Decimal("100.00"), "card_ru"
        )
        
        payment = self.db_service.get_payment_by_id(created_payment.id)
        
        self.assertIsNotNone(payment)
        self.assertEqual(payment.id, created_payment.id)
        self.assertEqual(payment.lava_invoice_id, "lava_123")
    
    def test_get_payment_by_lava_id(self):
        """Тест получения платежа по Lava ID"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        created_payment = self.db_service.create_payment(
            user.id, "lava_123", Decimal("100.00"), "card_ru"
        )
        
        payment = self.db_service.get_payment_by_lava_id("lava_123")
        
        self.assertIsNotNone(payment)
        self.assertEqual(payment.id, created_payment.id)
        self.assertEqual(payment.lava_invoice_id, "lava_123")
    
    def test_update_payment_status(self):
        """Тест обновления статуса платежа"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        subscription = self.db_service.create_subscription(user.id, "monthly", 1)
        payment = self.db_service.create_payment(
            user.id, "lava_123", Decimal("100.00"), "card_ru"
        )
        
        # Обновляем статус на завершенный
        updated = self.db_service.update_payment_status(
            payment.id, "completed", subscription.id
        )
        self.assertTrue(updated)
        
        # Проверяем обновление
        updated_payment = self.db_service.get_payment_by_id(payment.id)
        self.assertEqual(updated_payment.status, "completed")
        self.assertEqual(updated_payment.subscription_id, subscription.id)
        self.assertIsNotNone(updated_payment.completed_at)
    
    def test_get_user_payments(self):
        """Тест получения платежей пользователя"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Создаем несколько платежей
        payment1 = self.db_service.create_payment(
            user.id, "lava_123", Decimal("100.00"), "card_ru"
        )
        payment2 = self.db_service.create_payment(
            user.id, "lava_124", Decimal("200.00"), "crypto"
        )
        
        payments = self.db_service.get_user_payments(user.id)
        
        self.assertEqual(len(payments), 2)
        payment_ids = [p.id for p in payments]
        self.assertIn(payment1.id, payment_ids)
        self.assertIn(payment2.id, payment_ids)
    
    def test_get_expired_payments(self):
        """Тест получения истекших платежей"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Создаем истекший платеж напрямую в БД
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO payments (user_id, lava_invoice_id, amount, payment_method, status, expires_at)
                VALUES (?, ?, ?, ?, 'pending', datetime('now', '-1 hour'))
            ''', (user.id, "lava_123", 100.00, "card_ru"))
            payment_id = cursor.lastrowid
            conn.commit()
        
        expired_payments = self.db_service.get_expired_payments()
        
        self.assertEqual(len(expired_payments), 1)
        self.assertEqual(expired_payments[0].id, payment_id)
    
    # ==================== ТЕСТЫ ЛОГОВ АДМИНИСТРАТОРА ====================
    
    def test_create_admin_log(self):
        """Тест создания лога администратора"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        log = self.db_service.create_admin_log(
            admin_telegram_id=99999,
            action="grant_subscription",
            target_user_id=user.id,
            details="Manually granted 1 month subscription"
        )
        
        self.assertIsNotNone(log)
        self.assertEqual(log.admin_telegram_id, 99999)
        self.assertEqual(log.action, "grant_subscription")
        self.assertEqual(log.target_user_id, user.id)
        self.assertEqual(log.details, "Manually granted 1 month subscription")
    
    def test_get_admin_log_by_id(self):
        """Тест получения лога администратора по ID"""
        created_log = self.db_service.create_admin_log(
            admin_telegram_id=99999,
            action="test_action"
        )
        
        log = self.db_service.get_admin_log_by_id(created_log.id)
        
        self.assertIsNotNone(log)
        self.assertEqual(log.id, created_log.id)
        self.assertEqual(log.action, "test_action")
    
    def test_get_admin_logs(self):
        """Тест получения логов администратора"""
        # Создаем логи от разных администраторов
        log1 = self.db_service.create_admin_log(99999, "action1")
        log2 = self.db_service.create_admin_log(99999, "action2")
        log3 = self.db_service.create_admin_log(88888, "action3")
        
        # Получаем все логи
        all_logs = self.db_service.get_admin_logs()
        self.assertEqual(len(all_logs), 3)
        
        # Получаем логи конкретного администратора
        admin_logs = self.db_service.get_admin_logs(admin_telegram_id=99999)
        self.assertEqual(len(admin_logs), 2)
        
        # Тест с лимитом
        limited_logs = self.db_service.get_admin_logs(limit=2)
        self.assertEqual(len(limited_logs), 2)
    
    # ==================== ТЕСТЫ СТАТИСТИКИ ====================
    
    def test_get_statistics(self):
        """Тест получения статистики системы"""
        # Создаем тестовые данные
        user1 = self.db_service.create_user(telegram_id=12345, username="user1")
        user2 = self.db_service.create_user(telegram_id=12346, username="user2")
        
        # Создаем подписки
        sub1 = self.db_service.create_subscription(user1.id, "monthly", 1)
        sub2 = self.db_service.create_subscription(user2.id, "yearly", 12)
        
        # Создаем платежи
        payment1 = self.db_service.create_payment(
            user1.id, "lava_123", Decimal("100.00"), "card_ru"
        )
        payment2 = self.db_service.create_payment(
            user2.id, "lava_124", Decimal("1200.00"), "crypto"
        )
        
        # Завершаем платежи
        self.db_service.update_payment_status(payment1.id, "completed", sub1.id)
        self.db_service.update_payment_status(payment2.id, "completed", sub2.id)
        
        # Получаем статистику
        stats = self.db_service.get_statistics()
        
        self.assertEqual(stats['total_users'], 2)
        self.assertEqual(stats['active_subscriptions'], 2)
        self.assertEqual(stats['total_revenue'], 1300.0)
        self.assertEqual(stats['monthly_payments'], 2)
    
    # ==================== ТЕСТЫ ОБРАБОТКИ ОШИБОК ====================
    
    def test_update_nonexistent_user(self):
        """Тест обновления несуществующего пользователя"""
        updated = self.db_service.update_user(999, username="newname")
        self.assertFalse(updated)
    
    def test_create_payment_duplicate_lava_id(self):
        """Тест создания платежа с дублирующим Lava ID"""
        user = self.db_service.create_user(telegram_id=12345, username="testuser")
        
        # Создаем первый платеж
        self.db_service.create_payment(
            user.id, "lava_123", Decimal("100.00"), "card_ru"
        )
        
        # Попытка создать второй платеж с тем же lava_invoice_id
        with self.assertRaises(Exception):
            self.db_service.create_payment(
                user.id, "lava_123", Decimal("200.00"), "crypto"
            )


if __name__ == '__main__':
    unittest.main()