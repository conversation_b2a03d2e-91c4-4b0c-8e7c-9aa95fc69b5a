#!/usr/bin/env python3
"""
Тест административных команд для проверки их работы
"""

import os
import sys
import time
from unittest.mock import patch, MagicMock

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_admin_commands():
    """Тест административных команд бота"""
    
    # Мокаем переменные окружения для тестирования
    test_env = {
        'TELEGRAM_BOT_TOKEN': 'test_bot_token',
        'LAVA_API_KEY': 'test_lava_key',
        'LAVA_SECRET_KEY': 'test_lava_secret',
        'WEBHOOK_URL': 'https://test.example.com/webhook',
        'CHANNEL_ID': '@test_channel',
        'ADMIN_USER_IDS': '123456789,987654321'  # Тестовые админы
    }
    
    with patch.dict(os.environ, test_env):
        try:
            # Импортируем после установки переменных окружения
            from config import Config
            from app.services.bot_handler import TelegramBotHandler
            from app.models.database import DatabaseService
            from app.services.payment_service import PaymentService
            from app.services.channel_manager import ChannelManager
            
            print("✅ Импорт модулей успешен")
            
            # Проверяем конфигурацию админов
            print(f"✅ Административные ID: {Config.ADMIN_USER_IDS}")
            
            # Создаем сервисы
            db_service = DatabaseService()
            payment_service = PaymentService()
            channel_manager = ChannelManager()
            
            # Создаем обработчик бота
            bot_handler = TelegramBotHandler(db_service, payment_service, channel_manager)
            print("✅ TelegramBotHandler создан")
            
            # Тестируем проверку прав администратора
            admin_id = 123456789
            regular_user_id = 111111111
            
            is_admin_check = bot_handler._is_admin(admin_id)
            is_regular_check = bot_handler._is_admin(regular_user_id)
            
            print(f"✅ Проверка админа {admin_id}: {is_admin_check}")
            print(f"✅ Проверка обычного пользователя {regular_user_id}: {is_regular_check}")
            
            if is_admin_check and not is_regular_check:
                print("✅ Проверка прав администратора работает корректно")
            else:
                print("❌ Ошибка проверки прав администратора")
                return False
            
            # Проверяем наличие административных методов
            admin_methods = [
                'handle_admin_info',
                'handle_admin_grant', 
                'handle_admin_revoke',
                'handle_admin_stats'
            ]
            
            for method_name in admin_methods:
                if hasattr(bot_handler, method_name):
                    print(f"✅ Метод {method_name} найден")
                else:
                    print(f"❌ Метод {method_name} не найден")
                    return False
            
            # Проверяем методы статистики в DatabaseService
            stats_methods = [
                'get_system_statistics',
                'get_payment_statistics',
                'get_all_active_subscriptions',
                'get_expired_subscriptions'
            ]
            
            for method_name in stats_methods:
                if hasattr(db_service, method_name):
                    print(f"✅ Метод {method_name} найден в DatabaseService")
                else:
                    print(f"❌ Метод {method_name} не найден в DatabaseService")
                    return False
            
            # Тестируем получение статистики
            try:
                system_stats = db_service.get_system_statistics()
                payment_stats = db_service.get_payment_statistics()
                print(f"✅ Системная статистика: {len(system_stats)} параметров")
                print(f"✅ Статистика платежей: {len(payment_stats)} параметров")
            except Exception as e:
                print(f"❌ Ошибка получения статистики: {e}")
                return False
            
            print("\n🎉 Все тесты административных команд прошли успешно!")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка тестирования: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🔄 Запуск теста административных команд...")
    success = test_admin_commands()
    sys.exit(0 if success else 1)