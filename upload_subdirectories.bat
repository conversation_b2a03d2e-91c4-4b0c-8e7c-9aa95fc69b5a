@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Uploading remaining files and creating start script...
echo.
echo Uploading models directory files:
pscp -pw %PASSWORD% app\models\__init__.py ubuntu@**************:/home/<USER>/models_init.py
pscp -pw %PASSWORD% app\models\database.py ubuntu@**************:/home/<USER>/models_database.py
pscp -pw %PASSWORD% app\models\models.py ubuntu@**************:/home/<USER>/models_models.py
echo.
echo Creating models directory and moving files:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S mkdir -p /home/<USER>/app/app/models && echo '%PASSWORD%' | sudo -S mv /home/<USER>/models_init.py /home/<USER>/app/app/models/__init__.py && echo '%PASSWORD%' | sudo -S mv /home/<USER>/models_database.py /home/<USER>/app/app/models/database.py && echo '%PASSWORD%' | sudo -S mv /home/<USER>/models_models.py /home/<USER>/app/app/models/models.py"
echo.
echo Uploading services directory files:
pscp -pw %PASSWORD% app\services\__init__.py ubuntu@**************:/home/<USER>/services_init.py
pscp -pw %PASSWORD% app\services\bot_handler.py ubuntu@**************:/home/<USER>/services_bot_handler.py
pscp -pw %PASSWORD% app\services\payment_service.py ubuntu@**************:/home/<USER>/services_payment_service.py
echo.
echo Creating services directory and moving files:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S mkdir -p /home/<USER>/app/app/services && echo '%PASSWORD%' | sudo -S mv /home/<USER>/services_init.py /home/<USER>/app/app/services/__init__.py && echo '%PASSWORD%' | sudo -S mv /home/<USER>/services_bot_handler.py /home/<USER>/app/app/services/bot_handler.py && echo '%PASSWORD%' | sudo -S mv /home/<USER>/services_payment_service.py /home/<USER>/app/app/services/payment_service.py"
echo Done with subdirectories.