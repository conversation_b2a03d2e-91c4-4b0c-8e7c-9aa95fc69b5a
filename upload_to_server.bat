@echo off
echo 🚀 Загрузка файлов Rinadzhi Bot на сервер...
echo.

echo 📁 Создание временной директории на сервере...
ssh ubuntu@195.49.212.172 "mkdir -p /tmp/rinadzhi_bot"

echo 📤 Загрузка файлов...
scp server_files/rinadzhi_bot.py ubuntu@195.49.212.172:/tmp/rinadzhi_bot.py
scp server_files/config.py ubuntu@195.49.212.172:/tmp/config.py  
scp server_files/init_database.py ubuntu@195.49.212.172:/tmp/init_database.py
scp server_files/install.sh ubuntu@195.49.212.172:/tmp/install.sh

echo 🔧 Установка прав на выполнение...
ssh ubuntu@195.49.212.172 "chmod +x /tmp/install.sh"

echo 🚀 Запуск установки...
ssh ubuntu@195.49.212.172 "sudo /tmp/install.sh"

echo.
echo ✅ Установка завершена!
echo 📊 Проверить статус: ssh ubuntu@195.49.212.172 "sudo systemctl status rinadzhi-bot"
echo 📋 Посмотреть логи: ssh ubuntu@195.49.212.172 "sudo tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log"
pause
