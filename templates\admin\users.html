{% extends "base.html" %}

{% block title %}Пользователи{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="pt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ safe_url_for('admin.dashboard') }}">Главная</a></li>
        <li class="breadcrumb-item active" aria-current="page">Пользователи</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-people"></i> Управление пользователями
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-download"></i> Экспорт
            </button>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<form method="GET" class="mb-3">
    <div class="row">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" name="search" class="form-control" placeholder="Поиск по имени, username или ID..." value="{{ request.args.get('search', '') }}">
            </div>
        </div>
        <div class="col-md-3">
            <select name="status" class="form-select">
                <option value="">Все пользователи</option>
                <option value="active" {{ 'selected' if request.args.get('status') == 'active' else '' }}>С активной подпиской</option>
                <option value="expired" {{ 'selected' if request.args.get('status') == 'expired' else '' }}>С истекшей подпиской</option>
                <option value="no_subscription" {{ 'selected' if request.args.get('status') == 'no_subscription' else '' }}>Без подписки</option>
            </select>
        </div>
        <div class="col-md-3">
            <select name="sort" class="form-select">
                <option value="created_desc" {{ 'selected' if request.args.get('sort') == 'created_desc' else '' }}>Новые первыми</option>
                <option value="created_asc" {{ 'selected' if request.args.get('sort') == 'created_asc' else '' }}>Старые первыми</option>
                <option value="name_asc" {{ 'selected' if request.args.get('sort') == 'name_asc' else '' }}>По имени А-Я</option>
                <option value="name_desc" {{ 'selected' if request.args.get('sort') == 'name_desc' else '' }}>По имени Я-А</option>
            </select>
        </div>
        <div class="col-md-2">
            <div class="btn-group w-100">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i> Найти
                </button>
                <a href="{{ safe_url_for('admin.users') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-x"></i>
                </a>
            </div>
        </div>
    </div>
</form>

<!-- Bulk Actions -->
<div id="bulkActions" class="alert alert-info" style="display: none;">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <i class="bi bi-check-square"></i>
            Выбрано пользователей: <span id="selectedCount">0</span>
        </div>
        <div class="btn-group">
            <button class="btn btn-sm btn-success" onclick="bulkGrantSubscription()">
                <i class="bi bi-plus-circle"></i> Выдать подписку
            </button>
            <button class="btn btn-sm btn-warning" onclick="bulkRevokeSubscription()">
                <i class="bi bi-x-circle"></i> Отозвать подписку
            </button>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card shadow">
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" onchange="selectAllUsers()">
                        </th>
                        <th>ID</th>
                        <th>Telegram ID</th>
                        <th>Имя</th>
                        <th>Username</th>
                        <th>Регистрация</th>
                        <th>Статус</th>
                        <th>Действия</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user_data in users %}
                    <tr>
                        <td>
                            <input type="checkbox" name="selected_users" value="{{ user_data.user.id }}" onchange="updateBulkActions()">
                        </td>
                        <td>{{ user_data.user.id }}</td>
                        <td><code>{{ user_data.user.telegram_id }}</code></td>
                        <td>{{ user_data.user.first_name or '-' }} {{ user_data.user.last_name or '' }}</td>
                        <td>
                            {% if user_data.user.username %}
                                @{{ user_data.user.username }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>{{ user_data.user.created_at.strftime('%d.%m.%Y %H:%M') if user_data.user.created_at else '-' }}</td>
                        <td>
                            {% if user_data.subscription and user_data.subscription.status == 'active' %}
                                <span class="badge bg-success">Активна</span>
                                <br><small class="text-muted">до {{ user_data.subscription.end_date.strftime('%d.%m.%Y') }}</small>
                            {% elif user_data.subscription %}
                                <span class="badge bg-danger">Истекла</span>
                                <br><small class="text-muted">{{ user_data.subscription.end_date.strftime('%d.%m.%Y') }}</small>
                            {% else %}
                                <span class="badge bg-secondary">Нет подписки</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ safe_url_for('admin.user_detail', user_id=user_data.user.id) }}" class="btn btn-outline-primary" title="Подробнее">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <button class="btn btn-outline-success" title="Выдать подписку" onclick="grantSubscription({{ user_data.user.id }}, '{{ user_data.user.first_name or 'Пользователь' }}')">
                                    <i class="bi bi-plus-circle"></i>
                                </button>
                                {% if user_data.subscription and user_data.subscription.status == 'active' %}
                                <button class="btn btn-outline-warning" title="Отозвать подписку" onclick="revokeSubscription({{ user_data.user.id }}, '{{ user_data.user.first_name or 'Пользователь' }}')">
                                    <i class="bi bi-x-circle"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Пагинация пользователей">
            <ul class="pagination justify-content-center">
                <li class="page-item {% if not has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ safe_url_for('admin.users', page=page-1, search=search, status=status_filter, sort=sort_by) if has_prev else '#' }}">Предыдущая</a>
                </li>
                
                {% for p in range(1, total_pages + 1) %}
                    {% if p == page %}
                        <li class="page-item active">
                            <span class="page-link">{{ p }}</span>
                        </li>
                    {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                        <li class="page-item">
                            <a class="page-link" href="{{ safe_url_for('admin.users', page=p, search=search, status=status_filter, sort=sort_by) }}">{{ p }}</a>
                        </li>
                    {% elif p == 4 and page > 6 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% elif p == total_pages - 3 and page < total_pages - 5 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                <li class="page-item {% if not has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ safe_url_for('admin.users', page=page+1, search=search, status=status_filter, sort=sort_by) if has_next else '#' }}">Следующая</a>
                </li>
            </ul>
        </nav>
        
        <!-- Информация о пагинации -->
        <div class="text-center text-muted mt-2">
            Показано {{ users|length }} из {{ total_count }} пользователей
            {% if search or status_filter %}
                (отфильтровано)
            {% endif %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">Пользователи не найдены</h4>
            <p class="text-muted">Пока нет зарегистрированных пользователей</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function grantSubscription(userId, userName) {
    const months = prompt(`Выдать подписку пользователю "${userName}".\nВведите количество месяцев (1-12):`, '1');
    
    if (months === null) return; // Отмена
    
    const monthsNum = parseInt(months);
    if (isNaN(monthsNum) || monthsNum < 1 || monthsNum > 12) {
        alert('Пожалуйста, введите число от 1 до 12');
        return;
    }
    
    if (confirm(`Выдать подписку на ${monthsNum} мес. пользователю "${userName}"?`)) {
        fetch('/admin/api/grant-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                months: monthsNum
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Подписка выдана успешно!');
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}

function revokeSubscription(userId, userName) {
    if (confirm(`Отозвать подписку у пользователя "${userName}"?`)) {
        fetch('/admin/api/revoke-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Подписка отозвана успешно!');
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}

// Массовые операции
function selectAllUsers() {
    const checkboxes = document.querySelectorAll('input[name="selected_users"]');
    const selectAll = document.getElementById('selectAll');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const selectedCount = document.querySelectorAll('input[name="selected_users"]:checked').length;
    const bulkActions = document.getElementById('bulkActions');
    
    if (selectedCount > 0) {
        bulkActions.style.display = 'block';
        document.getElementById('selectedCount').textContent = selectedCount;
    } else {
        bulkActions.style.display = 'none';
    }
}

function bulkGrantSubscription() {
    const selected = Array.from(document.querySelectorAll('input[name="selected_users"]:checked'))
                         .map(cb => cb.value);
    
    if (selected.length === 0) {
        alert('Выберите пользователей');
        return;
    }
    
    const months = prompt(`Выдать подписку ${selected.length} пользователям.\nВведите количество месяцев (1-12):`, '1');
    
    if (months === null) return;
    
    const monthsNum = parseInt(months);
    if (isNaN(monthsNum) || monthsNum < 1 || monthsNum > 12) {
        alert('Пожалуйста, введите число от 1 до 12');
        return;
    }
    
    if (confirm(`Выдать подписку на ${monthsNum} мес. ${selected.length} пользователям?`)) {
        fetch('/admin/api/bulk-grant-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_ids: selected,
                months: monthsNum
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Подписка выдана ${data.granted_count} пользователям!`);
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}

function bulkRevokeSubscription() {
    const selected = Array.from(document.querySelectorAll('input[name="selected_users"]:checked'))
                         .map(cb => cb.value);
    
    if (selected.length === 0) {
        alert('Выберите пользователей');
        return;
    }
    
    if (confirm(`Отозвать подписки у ${selected.length} пользователей?`)) {
        fetch('/admin/api/bulk-revoke-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_ids: selected
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Подписки отозваны у ${data.revoked_count} пользователей!`);
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}
</script>
{% endblock %}