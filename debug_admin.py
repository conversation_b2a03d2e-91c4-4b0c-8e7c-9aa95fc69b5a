#!/usr/bin/env python3
"""
Скрипт для диагностики проблем с админ-панелью
"""

import sys
import traceback
from flask import Flask
from config import Config

def test_admin_initialization():
    """Тестирует инициализацию админ-панели"""
    try:
        print("=== Тест инициализации админ-панели ===")
        
        # Создаем Flask приложение
        app = Flask(__name__)
        app.config.from_object(Config)
        
        print("✅ Flask приложение создано")
        
        # Импортируем и инициализируем сервисы
        from app.models.database import DatabaseService
        from app.services.scheduler_service import SchedulerService
        from app.admin import admin_bp, init_admin_services
        
        print("✅ Модули импортированы")
        
        # Инициализируем сервисы
        db_service = DatabaseService()
        print("✅ DatabaseService инициализирован")
        
        scheduler_service = SchedulerService(db_service, None, None)
        print("✅ SchedulerService инициализирован")
        
        # Инициализируем админ-панель
        init_admin_services(db_service, scheduler_service)
        app.register_blueprint(admin_bp)
        print("✅ Админ-панель инициализирована")
        
        # Тестируем методы базы данных
        print("\n=== Тест методов базы данных ===")
        
        try:
            stats = db_service.get_system_statistics()
            print(f"✅ get_system_statistics: {stats}")
        except Exception as e:
            print(f"❌ get_system_statistics: {e}")
        
        try:
            payment_stats = db_service.get_payment_statistics()
            print(f"✅ get_payment_statistics: {payment_stats}")
        except Exception as e:
            print(f"❌ get_payment_statistics: {e}")
        
        try:
            recent_payments = db_service.get_recent_payments(limit=5)
            print(f"✅ get_recent_payments: {len(recent_payments)} записей")
        except Exception as e:
            print(f"❌ get_recent_payments: {e}")
        
        # Тестируем рендеринг шаблона
        print("\n=== Тест рендеринга шаблона ===")
        
        with app.app_context():
            try:
                from flask import render_template
                
                # Подготавливаем тестовые данные
                test_stats = {
                    'total_users': 10,
                    'active_subscriptions': 5,
                    'expiring_today': 1,
                    'expiring_tomorrow': 2,
                    'monthly_subs': 3,
                    'quarterly_subs': 1,
                    'half_yearly_subs': 1,
                    'yearly_subs': 0
                }
                
                test_payment_stats = {
                    'total_revenue': 1500.0,
                    'pending_payments': 2,
                    'failed_payments': 1
                }
                
                test_recent_payments = []
                test_revenue_labels = ['01.01', '02.01', '03.01']
                test_revenue_data = [100, 200, 150]
                
                # Пробуем отрендерить шаблон
                html = render_template('admin/dashboard.html',
                                     stats=test_stats,
                                     payment_stats=test_payment_stats,
                                     recent_payments=test_recent_payments,
                                     revenue_labels=test_revenue_labels,
                                     revenue_data=test_revenue_data)
                
                print("✅ Шаблон dashboard.html успешно отрендерен")
                print(f"   Размер HTML: {len(html)} символов")
                
            except Exception as e:
                print(f"❌ Ошибка рендеринга шаблона: {e}")
                traceback.print_exc()
        
        print("\n=== Диагностика завершена ===")
        return True
        
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        traceback.print_exc()
        return False

def test_database_methods():
    """Тестирует методы базы данных отдельно"""
    try:
        print("\n=== Детальный тест методов базы данных ===")
        
        from app.models.database import DatabaseService
        db_service = DatabaseService()
        
        # Проверяем подключение к базе данных
        try:
            with db_service.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM users")
                user_count = cursor.fetchone()[0]
                print(f"✅ Подключение к БД: {user_count} пользователей")
        except Exception as e:
            print(f"❌ Ошибка подключения к БД: {e}")
            return False
        
        # Тестируем каждый метод отдельно
        methods_to_test = [
            ('get_system_statistics', []),
            ('get_payment_statistics', []),
            ('get_recent_payments', [5]),
            ('get_all_active_subscriptions', []),
        ]
        
        for method_name, args in methods_to_test:
            try:
                method = getattr(db_service, method_name)
                result = method(*args)
                print(f"✅ {method_name}: {type(result)} - {len(result) if isinstance(result, (list, dict)) else result}")
            except Exception as e:
                print(f"❌ {method_name}: {e}")
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка тестирования БД: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Запуск диагностики админ-панели...")
    
    success1 = test_database_methods()
    success2 = test_admin_initialization()
    
    if success1 and success2:
        print("\n🎉 Все тесты прошли успешно!")
        sys.exit(0)
    else:
        print("\n💥 Обнаружены ошибки!")
        sys.exit(1)