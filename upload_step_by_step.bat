@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Uploading files one by one...
echo.
echo Uploading main Python files:
pscp -pw %PASSWORD% app.py ubuntu@195.49.212.172:/home/<USER>/
pscp -pw %PASSWORD% main.py ubuntu@195.49.212.172:/home/<USER>/
pscp -pw %PASSWORD% config.py ubuntu@195.49.212.172:/home/<USER>/
pscp -pw %PASSWORD% requirements.txt ubuntu@195.49.212.172:/home/<USER>/
pscp -pw %PASSWORD% .env ubuntu@195.49.212.172:/home/<USER>/
echo.
echo Moving main files to telegrambot directory:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S mv /home/<USER>/app.py /home/<USER>/app/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/main.py /home/<USER>/app/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/config.py /home/<USER>/app/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/requirements.txt /home/<USER>/app/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/.env /home/<USER>/app/"
echo.
echo Uploading app subdirectory files:
pscp -pw %PASSWORD% app\__init__.py ubuntu@195.49.212.172:/home/<USER>/app_init.py
pscp -pw %PASSWORD% app\admin.py ubuntu@195.49.212.172:/home/<USER>/app_admin.py
echo.
echo Creating app subdirectory and moving files:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S mkdir -p /home/<USER>/app/app && echo '%PASSWORD%' | sudo -S mv /home/<USER>/app_init.py /home/<USER>/app/app/__init__.py && echo '%PASSWORD%' | sudo -S mv /home/<USER>/app_admin.py /home/<USER>/app/app/admin.py"
echo Done with main files.