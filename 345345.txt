создал несколько скриптов и файлов для обновления данных на сервере. Вот что было сделано:

## Созданные файлы для обновления сервера:
1. 1.
   `update_server_env.ps1` - PowerShell скрипт для обновления .env файла
2. 2.
   `update_server.sh` - Bash скрипт для обновления
3. 3.
   `simple_update.ps1` - Упрощенный PowerShell скрипт
4. 4.
   `update_with_hostkey.ps1` - Скрипт с обработкой SSH ключа
5. 5.
   `server_env_file` - Готовый .env файл для загрузки
## Данные из локального .env файла:
Все скрипты содержат актуальные данные из вашего локального файла `.env` :

- TELEGRAM_BOT_TOKEN : **********************************************
- LAVA_API_KEY : WnJ549MVZPHMtNPuUdtLaxS9nWhNVh3e4oazrhFVmUDB2qJaql36ECIeOX4ixD6q
- WEBHOOK_URL : http://**************/webhook
- CHANNEL_ID : -1001002786126628
- ADMIN_USER_IDS : 389794370
- ADMIN_PASSWORD : SecureAdmin2024!
- И все остальные настройки
## Рекомендации для завершения обновления:
1. 1.
   Выполните один из скриптов вручную в PowerShell с правами администратора
2. 2.
   Или используйте готовый файл server_env_file для ручной загрузки через SCP/SFTP клиент
3. 3.
   После загрузки файла подключитесь к серверу и перезапустите службы:
   ```
   ssh root@**************
   systemctl restart telegram-bot
   systemctl restart nginx
   ```
Все необходимые файлы созданы и готовы к использованию. Данные из вашего локального .env файла успешно подготовлены для загрузки на сервер **************.