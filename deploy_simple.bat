@echo off
echo 🚀 Простая установка Rinadzhi Bot
echo.

echo 📋 Шаг 1: Проверка подключения...
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "echo 'Подключение работает!'"
if errorlevel 1 (
    echo ❌ Ошибка подключения
    pause
    exit /b 1
)
echo ✅ Подключение успешно

echo.
echo 📋 Шаг 2: Создание пользователя и директорий...
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo useradd -m -s /bin/bash telegrambot 2>/dev/null || echo 'Пользователь уже существует'"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo mkdir -p /home/<USER>/app"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo mkdir -p /var/log/telegram-payment-bot"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo chown telegrambot:telegrambot /home/<USER>/app"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo chown telegrambot:telegrambot /var/log/telegram-payment-bot"
echo ✅ Структура создана

echo.
echo 📋 Шаг 3: Установка Python...
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo apt update && sudo apt install -y python3 python3-pip python3-venv"
echo ✅ Python установлен

echo.
echo 📋 Шаг 4: Создание виртуального окружения...
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "cd /home/<USER>/app && sudo -u telegrambot python3 -m venv venv"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot /home/<USER>/app/venv/bin/pip install --upgrade pip"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot /home/<USER>/app/venv/bin/pip install pyTelegramBotAPI requests"
echo ✅ Зависимости установлены

echo.
echo 📋 Шаг 5: Загрузка файлов...
pscp -pw "dkomqgTaijxro7in^bxd" server_files/rinadzhi_bot.py ubuntu@**************:/tmp/
pscp -pw "dkomqgTaijxro7in^bxd" server_files/config.py ubuntu@**************:/tmp/
pscp -pw "dkomqgTaijxro7in^bxd" server_files/init_database.py ubuntu@**************:/tmp/

plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo cp /tmp/rinadzhi_bot.py /home/<USER>/app/"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo cp /tmp/config.py /home/<USER>/app/"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo cp /tmp/init_database.py /home/<USER>/app/"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo chown -R telegrambot:telegrambot /home/<USER>/app/"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo chmod +x /home/<USER>/app/rinadzhi_bot.py"
echo ✅ Файлы загружены

echo.
echo 📋 Шаг 6: Инициализация базы данных...
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot /home/<USER>/app/venv/bin/python /home/<USER>/app/init_database.py"
echo ✅ База данных готова

echo.
echo 📋 Шаг 7: Запуск бота...
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot || true"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "cd /home/<USER>/app && sudo -u telegrambot nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &"
echo ✅ Бот запущен

echo.
echo 📋 Шаг 8: Проверка статуса...
timeout 5 >nul
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ps aux | grep rinadzhi_bot | grep -v grep"
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "tail -5 /var/log/telegram-payment-bot/rinadzhi_bot.log"

echo.
echo 🎉 УСТАНОВКА ЗАВЕРШЕНА!
echo ========================
echo.
echo 📊 Проверить статус:
echo    plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ps aux | grep rinadzhi_bot"
echo.
echo 📋 Посмотреть логи:
echo    plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log"
echo.
echo ✅ Бот готов! Тестируйте: https://t.me/rinadzhi_bot
pause
