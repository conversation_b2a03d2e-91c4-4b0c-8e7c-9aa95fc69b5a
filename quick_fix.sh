#!/bin/bash

# Quick Fix Script for Telegram Payment Bot
# This script addresses the immediate issues found in the deployment check

set -e

SERVER_IP="**************"
SERVER_USER="ubuntu"
SERVER_PASSWORD="dkomqgTaijxro7in^bxd"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

execute_remote() {
    local command="$1"
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "$command"
}

echo "=== QUICK FIX FOR TELEGRAM PAYMENT BOT ==="

# Check if the application files exist
print_status "Checking current state..."
if execute_remote "test -d /home/<USER>/app"; then
    print_status "Application directory exists"
    
    # Check if service is configured correctly
    if execute_remote "test -f /etc/systemd/system/telegram-payment-bot.service"; then
        print_status "Service file exists, checking configuration..."
        
        # Get current service status
        service_status=$(execute_remote "sudo systemctl is-active telegram-payment-bot || echo 'inactive'")
        print_status "Current service status: $service_status"
        
        if [ "$service_status" = "inactive" ]; then
            print_status "Attempting to start the service..."
            execute_remote "sudo systemctl start telegram-payment-bot"
            sleep 3
            
            new_status=$(execute_remote "sudo systemctl is-active telegram-payment-bot || echo 'failed'")
            if [ "$new_status" = "active" ]; then
                print_success "Service started successfully!"
            else
                print_error "Service failed to start. Checking logs..."
                execute_remote "sudo journalctl -u telegram-payment-bot --no-pager -n 10"
            fi
        fi
    else
        print_error "Service file not found. Full deployment needed."
    fi
else
    print_error "Application directory not found. Full deployment needed."
fi

# Test endpoints
print_status "Testing endpoints..."
health_status=$(execute_remote "curl -s -o /dev/null -w '%{http_code}' http://localhost/health || echo '000'")
main_status=$(execute_remote "curl -s -o /dev/null -w '%{http_code}' http://localhost/ || echo '000'")

echo "Health endpoint status: $health_status"
echo "Main page status: $main_status"

if [ "$health_status" = "200" ]; then
    print_success "Health endpoint is working!"
else
    print_error "Health endpoint is not working (status: $health_status)"
fi

if [ "$main_status" = "200" ] || [ "$main_status" = "302" ]; then
    print_success "Main page is accessible!"
else
    print_error "Main page is not accessible (status: $main_status)"
fi

print_status "Current service status:"
execute_remote "sudo systemctl status telegram-payment-bot --no-pager -l || true"