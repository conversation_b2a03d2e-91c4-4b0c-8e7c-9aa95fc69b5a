# Flask приложение будет инициализировано здесь

def create_app():
    """Создает и настраивает Flask приложение"""
    import sys
    import os
    
    # Добавляем родительскую директорию в путь
    parent_dir = os.path.dirname(os.path.dirname(__file__))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    
    # Импортируем функцию из основного модуля
    try:
        import app as main_app
        return main_app.create_app()
    except Exception as e:
        # Если не удается импортировать, создаем базовое приложение
        from flask import Flask
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'dev-key'
        return app