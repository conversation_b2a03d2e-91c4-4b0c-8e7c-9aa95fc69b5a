#!/usr/bin/env python3
"""
Скрипт для обновления админ-панели на сервере
"""

import subprocess
import sys
import time

def run_command(command, description):
    """Выполняет команду и выводит результат"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ {description} - успешно")
            if result.stdout.strip():
                print(f"   Вывод: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} - ошибка")
            if result.stderr.strip():
                print(f"   Ошибка: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - превышено время ожидания")
        return False
    except Exception as e:
        print(f"💥 {description} - исключение: {e}")
        return False

def main():
    """Основная функция обновления"""
    print("=== Обновление админ-панели на сервере ===")
    
    server_commands = [
        # Команды для выполнения на сервере через SSH
        'ssh -o StrictHostKeyChecking=no ubuntu@************** "cd /home/<USER>/telegram_bot && sudo systemctl stop telegram-bot"',
        'ssh -o StrictHostKeyChecking=no ubuntu@************** "cd /home/<USER>/telegram_bot && git pull origin main"',
        'ssh -o StrictHostKeyChecking=no ubuntu@************** "cd /home/<USER>/telegram_bot && sudo systemctl start telegram-bot"',
        'ssh -o StrictHostKeyChecking=no ubuntu@************** "cd /home/<USER>/telegram_bot && sudo systemctl status telegram-bot"'
    ]
    
    descriptions = [
        "Остановка сервиса",
        "Обновление кода",
        "Запуск сервиса",
        "Проверка статуса"
    ]
    
    # Выполняем команды
    for command, description in zip(server_commands, descriptions):
        success = run_command(command, description)
        if not success and "status" not in description:
            print(f"\n💥 Ошибка при выполнении: {description}")
            return False
        time.sleep(2)
    
    print("\n🎉 Обновление завершено!")
    print("\n📝 Рекомендации:")
    print("1. Подождите 10-15 секунд для полного запуска сервиса")
    print("2. Попробуйте войти в админ-панель: https://**************/admin/login")
    print("3. Используйте пароль: admin123")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)