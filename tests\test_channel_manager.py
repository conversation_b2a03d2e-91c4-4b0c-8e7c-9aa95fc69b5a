"""
Тесты для Channel Manager
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import telebot

from app.services.channel_manager import ChannelManager
from config import Config

class TestChannelManager(unittest.TestCase):
    """Тесты для ChannelManager"""
    
    def setUp(self):
        """Настройка тестов"""
        self.mock_bot_token = "test_bot_token"
        self.mock_channel_id = "@test_channel"
        
        # Создаем менеджер с явными параметрами
        self.channel_manager = ChannelManager(
            bot_token=self.mock_bot_token, 
            channel_id=self.mock_channel_id
        )
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_init_with_default_config(self, mock_telebot):
        """Тест инициализации с конфигурацией по умолчанию"""
        with patch.object(Config, 'TELEGRAM_BOT_TOKEN', self.mock_bot_token):
            # Добавляем атрибут CHANNEL_ID к Config для теста
            with patch.object(Config, 'CHANNEL_ID', self.mock_channel_id, create=True):
                manager = ChannelManager()
                
                mock_telebot.assert_called_with(self.mock_bot_token)
                self.assertEqual(manager.channel_id, self.mock_channel_id)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_init_with_custom_params(self, mock_telebot):
        """Тест инициализации с пользовательскими параметрами"""
        custom_token = "custom_token"
        custom_channel = "@custom_channel"
        
        manager = ChannelManager(bot_token=custom_token, channel_id=custom_channel)
        
        mock_telebot.assert_called_with(custom_token)
        self.assertEqual(manager.channel_id, custom_channel)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_create_invite_link_success(self, mock_telebot):
        """Тест успешного создания пригласительной ссылки"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_invite_link = Mock()
        mock_invite_link.invite_link = "https://t.me/+test_invite_link"
        mock_bot.create_chat_invite_link.return_value = mock_invite_link
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        user_id = 123456789
        result = manager.create_invite_link(user_id)
        
        # Проверки
        self.assertEqual(result, "https://t.me/+test_invite_link")
        mock_bot.create_chat_invite_link.assert_called_once()
        
        # Проверяем параметры вызова
        call_args = mock_bot.create_chat_invite_link.call_args
        self.assertEqual(call_args[1]['chat_id'], self.mock_channel_id)
        self.assertEqual(call_args[1]['name'], f"Invite for user {user_id}")
        self.assertEqual(call_args[1]['member_limit'], 1)
        self.assertFalse(call_args[1]['creates_join_request'])
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_create_invite_link_custom_params(self, mock_telebot):
        """Тест создания пригласительной ссылки с пользовательскими параметрами"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_invite_link = Mock()
        mock_invite_link.invite_link = "https://t.me/+test_invite_link"
        mock_bot.create_chat_invite_link.return_value = mock_invite_link
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        user_id = 123456789
        result = manager.create_invite_link(user_id, expire_hours=48, member_limit=5)
        
        # Проверки
        self.assertEqual(result, "https://t.me/+test_invite_link")
        
        # Проверяем параметры вызова
        call_args = mock_bot.create_chat_invite_link.call_args
        self.assertEqual(call_args[1]['member_limit'], 5)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_create_invite_link_no_channel_id(self, mock_telebot):
        """Тест создания пригласительной ссылки без настроенного channel_id"""
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=None)
        
        result = manager.create_invite_link(123456789)
        
        self.assertIsNone(result)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_create_invite_link_api_error(self, mock_telebot):
        """Тест обработки ошибки API при создании пригласительной ссылки"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        mock_bot.create_chat_invite_link.side_effect = telebot.apihelper.ApiTelegramException(
            "create_chat_invite_link", "Bad Request", {"error_code": 400, "description": "Bad Request"}
        )
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.create_invite_link(123456789)
        
        # Проверки
        self.assertIsNone(result)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_remove_user_from_channel_success(self, mock_telebot):
        """Тест успешного удаления пользователя из канала"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        user_id = 123456789
        result = manager.remove_user_from_channel(user_id)
        
        # Проверки
        self.assertTrue(result)
        mock_bot.ban_chat_member.assert_called_once_with(
            chat_id=self.mock_channel_id,
            user_id=user_id,
            until_date=unittest.mock.ANY
        )
        mock_bot.unban_chat_member.assert_called_once_with(
            chat_id=self.mock_channel_id,
            user_id=user_id,
            only_if_banned=True
        )
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_remove_user_from_channel_user_not_found(self, mock_telebot):
        """Тест удаления пользователя, которого нет в канале"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        mock_bot.ban_chat_member.side_effect = telebot.apihelper.ApiTelegramException(
            "ban_chat_member", "Bad Request: user not found", {"error_code": 400, "description": "Bad Request: user not found"}
        )
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.remove_user_from_channel(123456789)
        
        # Проверки - должно вернуть True, так как пользователя уже нет в канале
        self.assertTrue(result)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_remove_user_from_channel_no_channel_id(self, mock_telebot):
        """Тест удаления пользователя без настроенного channel_id"""
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=None)
        
        result = manager.remove_user_from_channel(123456789)
        
        self.assertFalse(result)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_check_user_in_channel_member(self, mock_telebot):
        """Тест проверки пользователя в канале - пользователь является участником"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_member = Mock()
        mock_member.status = 'member'
        mock_bot.get_chat_member.return_value = mock_member
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.check_user_in_channel(123456789)
        
        # Проверки
        self.assertTrue(result)
        mock_bot.get_chat_member.assert_called_once_with(
            chat_id=self.mock_channel_id,
            user_id=123456789
        )
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_check_user_in_channel_admin(self, mock_telebot):
        """Тест проверки пользователя в канале - пользователь является администратором"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_member = Mock()
        mock_member.status = 'administrator'
        mock_bot.get_chat_member.return_value = mock_member
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.check_user_in_channel(123456789)
        
        # Проверки
        self.assertTrue(result)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_check_user_in_channel_left(self, mock_telebot):
        """Тест проверки пользователя в канале - пользователь покинул канал"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_member = Mock()
        mock_member.status = 'left'
        mock_bot.get_chat_member.return_value = mock_member
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.check_user_in_channel(123456789)
        
        # Проверки
        self.assertFalse(result)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_check_user_in_channel_not_found(self, mock_telebot):
        """Тест проверки пользователя в канале - пользователь не найден"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        mock_bot.get_chat_member.side_effect = telebot.apihelper.ApiTelegramException(
            "get_chat_member", "Bad Request: user not found", {"error_code": 400, "description": "Bad Request: user not found"}
        )
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.check_user_in_channel(123456789)
        
        # Проверки
        self.assertFalse(result)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_get_channel_info_success(self, mock_telebot):
        """Тест успешного получения информации о канале"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_chat = Mock()
        mock_chat.id = -1001234567890
        mock_chat.title = "Test Channel"
        mock_chat.username = "test_channel"
        mock_chat.description = "Test channel description"
        mock_chat.type = "channel"
        mock_bot.get_chat.return_value = mock_chat
        mock_bot.get_chat_member_count.return_value = 100
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.get_channel_info()
        
        # Проверки
        self.assertIsNotNone(result)
        self.assertEqual(result['id'], -1001234567890)
        self.assertEqual(result['title'], "Test Channel")
        self.assertEqual(result['username'], "test_channel")
        self.assertEqual(result['description'], "Test channel description")
        self.assertEqual(result['type'], "channel")
        self.assertEqual(result['member_count'], 100)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_get_channel_info_no_member_count(self, mock_telebot):
        """Тест получения информации о канале без количества участников"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_chat = Mock()
        mock_chat.id = -1001234567890
        mock_chat.title = "Test Channel"
        mock_chat.username = "test_channel"
        mock_chat.description = "Test channel description"
        mock_chat.type = "channel"
        mock_bot.get_chat.return_value = mock_chat
        mock_bot.get_chat_member_count.side_effect = Exception("Cannot get member count")
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.get_channel_info()
        
        # Проверки
        self.assertIsNotNone(result)
        self.assertEqual(result['title'], "Test Channel")
        self.assertIsNone(result['member_count'])
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_revoke_invite_link_success(self, mock_telebot):
        """Тест успешного отзыва пригласительной ссылки"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        invite_link = "https://t.me/+test_invite_link"
        result = manager.revoke_invite_link(invite_link)
        
        # Проверки
        self.assertTrue(result)
        mock_bot.revoke_chat_invite_link.assert_called_once_with(
            chat_id=self.mock_channel_id,
            invite_link=invite_link
        )
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_get_invite_links_success(self, mock_telebot):
        """Тест успешного получения списка пригласительных ссылок"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_link1 = Mock()
        mock_link1.invite_link = "https://t.me/+link1"
        mock_link1.name = "Link 1"
        mock_link1.creator = Mock()
        mock_link1.creator.id = 123456789
        mock_link1.creates_join_request = False
        mock_link1.is_primary = True
        mock_link1.is_revoked = False
        mock_link1.expire_date = None
        mock_link1.member_limit = None
        mock_link1.pending_join_request_count = 0
        
        mock_link2 = Mock()
        mock_link2.invite_link = "https://t.me/+link2"
        mock_link2.name = "Link 2"
        mock_link2.creator = None
        mock_link2.creates_join_request = False
        mock_link2.is_primary = False
        mock_link2.is_revoked = False
        mock_link2.expire_date = 1234567890
        mock_link2.member_limit = 1
        mock_link2.pending_join_request_count = 0
        
        mock_bot.get_chat_invite_links.return_value = [mock_link1, mock_link2]
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.get_invite_links()
        
        # Проверки
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 2)
        
        # Проверяем первую ссылку
        self.assertEqual(result[0]['invite_link'], "https://t.me/+link1")
        self.assertEqual(result[0]['name'], "Link 1")
        self.assertEqual(result[0]['creator'], 123456789)
        self.assertTrue(result[0]['is_primary'])
        
        # Проверяем вторую ссылку
        self.assertEqual(result[1]['invite_link'], "https://t.me/+link2")
        self.assertEqual(result[1]['name'], "Link 2")
        self.assertIsNone(result[1]['creator'])
        self.assertFalse(result[1]['is_primary'])
        self.assertEqual(result[1]['member_limit'], 1)
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_validate_bot_permissions_admin(self, mock_telebot):
        """Тест проверки прав бота - бот является администратором"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_bot_info = Mock()
        mock_bot_info.id = 987654321
        mock_bot.get_me.return_value = mock_bot_info
        
        mock_member = Mock()
        mock_member.status = 'administrator'
        mock_member.can_invite_users = True
        mock_member.can_restrict_members = True
        mock_member.can_delete_messages = False
        mock_member.can_manage_chat = True
        mock_bot.get_chat_member.return_value = mock_member
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.validate_bot_permissions()
        
        # Проверки
        self.assertTrue(result['is_admin'])
        self.assertTrue(result['can_invite_users'])
        self.assertTrue(result['can_restrict_members'])
        self.assertFalse(result['can_delete_messages'])
        self.assertTrue(result['can_manage_chat'])
        self.assertEqual(result['status'], 'administrator')
    
    @patch('app.services.channel_manager.telebot.TeleBot')
    def test_validate_bot_permissions_member(self, mock_telebot):
        """Тест проверки прав бота - бот является обычным участником"""
        # Настройка мока
        mock_bot = Mock()
        mock_telebot.return_value = mock_bot
        
        mock_bot_info = Mock()
        mock_bot_info.id = 987654321
        mock_bot.get_me.return_value = mock_bot_info
        
        mock_member = Mock()
        mock_member.status = 'member'
        # У обычного участника нет дополнительных прав
        mock_bot.get_chat_member.return_value = mock_member
        
        manager = ChannelManager(bot_token=self.mock_bot_token, channel_id=self.mock_channel_id)
        
        # Выполнение теста
        result = manager.validate_bot_permissions()
        
        # Проверки
        self.assertFalse(result['is_admin'])
        self.assertEqual(result['status'], 'member')

if __name__ == '__main__':
    unittest.main()