@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Checking current state and fixing file structure...
echo.
echo Checking what files were uploaded:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "ls -la /home/<USER>/app/"
echo.
echo Removing existing templates directory if it exists:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S rm -rf /home/<USER>/app/templates"
echo.
echo Creating fresh directory structure:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S mkdir -p /home/<USER>/app/templates/admin"
echo.
echo Setting proper ownership:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S chown -R telegrambot:telegrambot /home/<USER>/"
echo Done.