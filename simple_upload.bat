@echo off
echo ========================================
echo   ОБНОВЛЕНИЕ АДМИН-ПАНЕЛИ НА СЕРВЕРЕ
echo ========================================
echo.

echo 📦 Загрузка архива на сервер...
scp -o StrictHostKeyChecking=no admin_panel_fix.zip ubuntu@**************:/home/<USER>/
if %errorlevel% neq 0 (
    echo ❌ Ошибка загрузки архива
    pause
    exit /b 1
)
echo ✅ Архив загружен

echo.
echo 🔄 Обновление файлов на сервере...
ssh -o StrictHostKeyChecking=no ubuntu@************** "cd /home/<USER>/telegram_bot && sudo systemctl stop telegram-bot && sudo cp -r app templates backup_$(date +%%Y%%m%%d_%%H%%M%%S) && unzip -o /home/<USER>/admin_panel_fix.zip && sudo chown -R ubuntu:ubuntu app templates && sudo chmod -R 755 app templates && sudo systemctl start telegram-bot"
if %errorlevel% neq 0 (
    echo ❌ Ошибка обновления на сервере
    pause
    exit /b 1
)
echo ✅ Файлы обновлены

echo.
echo 📊 Проверка статуса сервиса...
ssh -o StrictHostKeyChecking=no ubuntu@************** "sudo systemctl status telegram-bot --no-pager -l"

echo.
echo 🎉 ОБНОВЛЕНИЕ ЗАВЕРШЕНО!
echo.
echo 📋 Следующие шаги:
echo 1. Откройте https://**************/admin/login
echo 2. Введите пароль: admin123
echo 3. Проверьте работу админ-панели
echo.
echo 🔍 Для просмотра логов:
echo ssh ubuntu@************** "sudo journalctl -u telegram-bot -f"
echo.
pause