@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Uploading templates and creating start script...
echo.
echo Uploading templates:
pscp -pw %PASSWORD% templates\admin\dashboard.html ubuntu@**************:/home/<USER>/dashboard.html
pscp -pw %PASSWORD% templates\admin\login.html ubuntu@**************:/home/<USER>/login.html
pscp -pw %PASSWORD% templates\admin\users.html ubuntu@**************:/home/<USER>/users.html
echo.
echo Creating templates directory and moving files:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S mkdir -p /home/<USER>/app/templates/admin && echo '%PASSWORD%' | sudo -S mv /home/<USER>/dashboard.html /home/<USER>/app/templates/admin/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/login.html /home/<USER>/app/templates/admin/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/users.html /home/<USER>/app/templates/admin/"
echo.
echo Creating start.sh script:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S tee /home/<USER>/app/start.sh > /dev/null << 'EOF'
#!/bin/bash
cd /home/<USER>/app
source venv/bin/activate
python main.py
EOF"
echo.
echo Making start.sh executable:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S chmod +x /home/<USER>/app/start.sh"
echo.
echo Setting proper ownership for all files:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S chown -R telegrambot:telegrambot /home/<USER>/app/"
echo Done with templates and start script.