{% extends "base.html" %}

{% block title %}Финансовые отчеты{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-graph-up"></i> Финансовые отчеты
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ safe_url_for('admin.reports', period='7') }}" 
               class="btn btn-outline-primary {{ 'active' if period == 7 }}">7 дней</a>
            <a href="{{ safe_url_for('admin.reports', period='30') }}" 
               class="btn btn-outline-primary {{ 'active' if period == 30 }}">30 дней</a>
            <a href="{{ safe_url_for('admin.reports', period='90') }}" 
               class="btn btn-outline-primary {{ 'active' if period == 90 }}">90 дней</a>
            <a href="{{ safe_url_for('admin.reports', period='365') }}" 
               class="btn btn-outline-primary {{ 'active' if period == 365 }}">1 год</a>
        </div>
        <a href="{{ safe_url_for('admin.payments') }}" class="btn btn-outline-secondary">
            <i class="bi bi-credit-card"></i> К платежам
        </a>
    </div>
</div>

<!-- Общая статистика -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Общий доход</h6>
                        <h3 class="mb-0">{{ "%.0f"|format(payment_stats.total_revenue or 0) }} ₽</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-currency-exchange" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Доход за месяц</h6>
                        <h3 class="mb-0">{{ "%.0f"|format(payment_stats.monthly_revenue or 0) }} ₽</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-month" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Доход за неделю</h6>
                        <h3 class="mb-0">{{ "%.0f"|format(payment_stats.weekly_revenue or 0) }} ₽</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-week" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Завершенные платежи</h6>
                        <h3 class="mb-0">{{ payment_stats.completed_payments or 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- График доходов -->
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> График доходов за {{ period }} дней
                </h5>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Доходы по способам оплаты -->
        {% if revenue_report.revenue_by_method %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart"></i> Доходы по способам оплаты
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for method, amount in revenue_report.revenue_by_method.items() %}
                    <div class="col-md-4 mb-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                {% if method == 'card_ru' %}
                                    <i class="bi bi-credit-card text-primary" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">Карта РФ</h6>
                                {% elif method == 'card_foreign' %}
                                    <i class="bi bi-credit-card-2-front text-info" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">Карта иностранная</h6>
                                {% elif method == 'crypto' %}
                                    <i class="bi bi-currency-bitcoin text-warning" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">Криптовалюта</h6>
                                {% else %}
                                    <i class="bi bi-cash text-secondary" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">{{ method }}</h6>
                                {% endif %}
                                <h4 class="text-success">{{ "%.0f"|format(amount) }} ₽</h4>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Круговая диаграмма -->
                <div class="text-center mt-4">
                    <canvas id="paymentMethodChart" width="300" height="300"></canvas>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Детальная статистика способов оплаты -->
        {% if method_stats %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart"></i> Детальная статистика способов оплаты
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Способ оплаты</th>
                                <th>Всего платежей</th>
                                <th>Успешных</th>
                                <th>Неудачных</th>
                                <th>Успешность</th>
                                <th>Общий доход</th>
                                <th>Средний чек</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for method, stats in method_stats.items() %}
                            <tr>
                                <td>
                                    {% if method == 'card_ru' %}
                                        <i class="bi bi-credit-card text-primary"></i> Карта РФ
                                    {% elif method == 'card_foreign' %}
                                        <i class="bi bi-credit-card-2-front text-info"></i> Карта иностранная
                                    {% elif method == 'crypto' %}
                                        <i class="bi bi-currency-bitcoin text-warning"></i> Криптовалюта
                                    {% else %}
                                        <i class="bi bi-cash text-secondary"></i> {{ method }}
                                    {% endif %}
                                </td>
                                <td>{{ stats.total_payments }}</td>
                                <td><span class="badge bg-success">{{ stats.completed_payments }}</span></td>
                                <td><span class="badge bg-danger">{{ stats.failed_payments }}</span></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ stats.success_rate }}%">
                                            {{ "%.1f"|format(stats.success_rate) }}%
                                        </div>
                                    </div>
                                </td>
                                <td><strong>{{ "%.0f"|format(stats.total_revenue) }} ₽</strong></td>
                                <td>{{ "%.0f"|format(stats.avg_amount) }} ₽</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Месячное сравнение доходов -->
        {% if monthly_comparison.monthly_data %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-month"></i> Доходы по месяцам
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyRevenueChart" width="400" height="200"></canvas>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Боковая панель с дополнительной статистикой -->
    <div class="col-md-4">
        <!-- Статистика за период -->
        {% if revenue_report %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-range"></i> За {{ period }} дней
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-muted">Общий доход</h6>
                    <h4 class="text-success">{{ "%.2f"|format(revenue_report.total_revenue or 0) }} ₽</h4>
                </div>
                
                {% if revenue_report.average_check %}
                <div class="mb-3">
                    <h6 class="text-muted">Средний чек</h6>
                    <h5 class="text-primary">{{ "%.2f"|format(revenue_report.average_check) }} ₽</h5>
                </div>
                {% endif %}
                
                {% if revenue_report.total_transactions %}
                <div class="mb-3">
                    <h6 class="text-muted">Количество транзакций</h6>
                    <h5 class="text-info">{{ revenue_report.total_transactions }}</h5>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Топ дни по доходам -->
        {% if revenue_report.daily_revenue %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-trophy"></i> Лучшие дни
                </h5>
            </div>
            <div class="card-body">
                {% set sorted_days = revenue_report.daily_revenue|sort(attribute='revenue', reverse=true) %}
                {% for day in sorted_days[:5] %}
                    {% if day.revenue > 0 %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ day.date }}</span>
                        <span class="badge bg-success">{{ "%.0f"|format(day.revenue) }} ₽</span>
                    </div>
                    {% endif %}
                {% endfor %}
                
                {% if not sorted_days or sorted_days[0].revenue == 0 %}
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    <p class="mt-2">Нет данных за период</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Экспорт данных -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-download"></i> Экспорт
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="exportData('csv')">
                        <i class="bi bi-file-earmark-spreadsheet"></i> Экспорт в CSV
                    </button>
                    <button class="btn btn-outline-success" onclick="exportData('excel')">
                        <i class="bi bi-file-earmark-excel"></i> Экспорт в Excel
                    </button>
                    <button class="btn btn-outline-danger" onclick="printReport()">
                        <i class="bi bi-printer"></i> Печать отчета
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Подключаем Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// График доходов по дням
const revenueCtx = document.getElementById('revenueChart').getContext('2d');

// Получаем данные для графика
fetch(`/admin/api/revenue-chart?period={{ period }}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const chartData = data.data;
            
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: chartData.map(item => item.date),
                    datasets: [{
                        label: 'Доход (₽)',
                        data: chartData.map(item => item.revenue),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Доходы по дням'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + ' ₽';
                                }
                            }
                        }
                    }
                }
            });
        }
    })
    .catch(error => {
        console.error('Error loading chart data:', error);
    });

// Круговая диаграмма способов оплаты
{% if revenue_report.revenue_by_method %}
const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');

const methodLabels = {
    'card_ru': 'Карта РФ',
    'card_foreign': 'Карта иностранная', 
    'crypto': 'Криптовалюта'
};

const methodData = {{ revenue_report.revenue_by_method|tojson }};
const labels = Object.keys(methodData).map(key => methodLabels[key] || key);
const values = Object.values(methodData);

new Chart(paymentMethodCtx, {
    type: 'doughnut',
    data: {
        labels: labels,
        datasets: [{
            data: values,
            backgroundColor: [
                '#0d6efd',  // Синий для карты РФ
                '#0dcaf0',  // Голубой для иностранной карты
                '#ffc107'   // Желтый для криптовалюты
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ' + context.parsed + ' ₽';
                    }
                }
            }
        }
    }
});
{% endif %}

// График месячных доходов
{% if monthly_comparison.monthly_data %}
const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');

const monthlyData = {{ monthly_comparison.monthly_data|tojson }};

new Chart(monthlyRevenueCtx, {
    type: 'bar',
    data: {
        labels: monthlyData.map(item => item.month),
        datasets: [{
            label: 'Доход (₽)',
            data: monthlyData.map(item => item.revenue),
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }, {
            label: 'Количество транзакций',
            data: monthlyData.map(item => item.transactions),
            type: 'line',
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            yAxisID: 'y1',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'Доходы и транзакции по месяцам'
            },
            legend: {
                display: true
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value + ' ₽';
                    }
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                beginAtZero: true,
                grid: {
                    drawOnChartArea: false,
                },
                ticks: {
                    callback: function(value) {
                        return value + ' шт';
                    }
                }
            }
        }
    }
});
{% endif %}

// Функции экспорта
function exportData(format) {
    const period = {{ period }};
    const url = `/admin/api/export-report?format=${format}&period=${period}`;
    
    // Создаем временную ссылку для скачивания
    const link = document.createElement('a');
    link.href = url;
    link.download = `financial_report_${period}days.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function printReport() {
    window.print();
}

// Обновление данных в реальном времени
function refreshStats() {
    fetch('/admin/api/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Обновляем статистику на странице
                console.log('Stats updated:', data);
            }
        })
        .catch(error => {
            console.error('Error refreshing stats:', error);
        });
}

// Автообновление каждые 5 минут
setInterval(refreshStats, 300000);
</script>

<style>
@media print {
    .btn-toolbar, .card-header .btn, .btn-group {
        display: none !important;
    }
}
</style>
{% endblock %}