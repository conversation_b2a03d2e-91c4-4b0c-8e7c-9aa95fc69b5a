@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Alternative status check...
echo.
echo Checking service status with systemctl:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S systemctl status telegram-payment-bot --no-pager -l"
echo.
echo Checking processes with ss command:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "ss -tlnp | grep :5000"
echo.
echo Checking if bot is responding:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "curl -s http://localhost:5000/admin/login | head -n 5"
echo Done.