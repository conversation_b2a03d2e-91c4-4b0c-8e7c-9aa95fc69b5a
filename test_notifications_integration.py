#!/usr/bin/env python3
"""
Тест интеграции системы уведомлений Telegram бота
"""

import sys
import os
import time
from datetime import datetime, timedelta
from decimal import Decimal

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.payment_service import PaymentService
from app.services.notification_service import NotificationService
from app.services.bot_handler import TelegramBotHandler
from app.services.scheduler_service import SchedulerService
from app.services.channel_manager import ChannelManager
from app.models.database import DatabaseService
from config import Config

def test_notification_system():
    """Тестирует систему уведомлений"""
    
    print("=== Тест системы уведомлений ===")
    
    # Инициализируем сервисы
    try:
        db_service = DatabaseService()
        payment_service = PaymentService()
        channel_manager = ChannelManager()
        
        # Создаем mock bot_handler для тестирования
        class MockBotHandler:
            def __init__(self):
                self.sent_notifications = []
            
            def send_payment_success_notification(self, user_id, order_id, amount, plan_months):
                notification = {
                    'type': 'payment_success',
                    'user_id': user_id,
                    'order_id': order_id,
                    'amount': amount,
                    'plan_months': plan_months,
                    'timestamp': datetime.now()
                }
                self.sent_notifications.append(notification)
                print(f"   📧 Отправлено уведомление об успешной оплате пользователю {user_id}")
                return True
            
            def send_payment_failed_notification(self, user_id, order_id, error_message=None):
                notification = {
                    'type': 'payment_failed',
                    'user_id': user_id,
                    'order_id': order_id,
                    'error_message': error_message,
                    'timestamp': datetime.now()
                }
                self.sent_notifications.append(notification)
                print(f"   📧 Отправлено уведомление о неудачной оплате пользователю {user_id}")
                return True
            
            def send_invite_link_notification(self, user_id, invite_url, subscription_end_date):
                notification = {
                    'type': 'invite_link',
                    'user_id': user_id,
                    'invite_url': invite_url,
                    'subscription_end_date': subscription_end_date,
                    'timestamp': datetime.now()
                }
                self.sent_notifications.append(notification)
                print(f"   📧 Отправлена пригласительная ссылка пользователю {user_id}")
                return True
            
            def send_subscription_expiry_warning(self, user_id, days_left):
                notification = {
                    'type': 'expiry_warning',
                    'user_id': user_id,
                    'days_left': days_left,
                    'timestamp': datetime.now()
                }
                self.sent_notifications.append(notification)
                print(f"   📧 Отправлено предупреждение об истечении подписки пользователю {user_id} ({days_left} дней)")
                return True
            
            def send_subscription_expired_notification(self, user_id):
                notification = {
                    'type': 'subscription_expired',
                    'user_id': user_id,
                    'timestamp': datetime.now()
                }
                self.sent_notifications.append(notification)
                print(f"   📧 Отправлено уведомление об истечении подписки пользователю {user_id}")
                return True
        
        mock_bot_handler = MockBotHandler()
        notification_service = NotificationService(mock_bot_handler, db_service)
        
        print("✅ Сервисы инициализированы")
        
    except Exception as e:
        print(f"❌ Ошибка инициализации сервисов: {e}")
        return False
    
    # Тестовые данные
    test_user_id = 987654321
    test_telegram_id = 987654321
    
    print(f"\n📋 Тестовые данные:")
    print(f"   User ID: {test_user_id}")
    print(f"   Telegram ID: {test_telegram_id}")
    
    # Шаг 1: Создаем тестового пользователя
    print(f"\n🔄 Шаг 1: Создание тестового пользователя...")
    try:
        existing_user = db_service.get_user_by_telegram_id(test_telegram_id)
        if existing_user:
            print(f"   ✅ Пользователь уже существует: ID {existing_user.id}")
            user = existing_user
        else:
            user = db_service.create_user(
                telegram_id=test_telegram_id,
                username='test_notifications',
                first_name='Test',
                last_name='Notifications'
            )
            print(f"   ✅ Пользователь создан: ID {user.id}")
            
    except Exception as e:
        print(f"   ❌ Ошибка создания пользователя: {e}")
        return False
    
    # Шаг 2: Тестируем уведомление об успешной оплате
    print(f"\n🔄 Шаг 2: Тест уведомления об успешной оплате...")
    try:
        success = mock_bot_handler.send_payment_success_notification(
            test_telegram_id, 
            "test_order_123", 
            299.0, 
            1
        )
        
        if success:
            print(f"   ✅ Уведомление об успешной оплате отправлено")
        else:
            print(f"   ❌ Ошибка отправки уведомления об успешной оплате")
            
    except Exception as e:
        print(f"   ❌ Исключение при отправке уведомления об успешной оплате: {e}")
    
    # Шаг 3: Тестируем уведомление о неудачной оплате
    print(f"\n🔄 Шаг 3: Тест уведомления о неудачной оплате...")
    try:
        success = mock_bot_handler.send_payment_failed_notification(
            test_telegram_id, 
            "test_order_456", 
            "Недостаточно средств на карте"
        )
        
        if success:
            print(f"   ✅ Уведомление о неудачной оплате отправлено")
        else:
            print(f"   ❌ Ошибка отправки уведомления о неудачной оплате")
            
    except Exception as e:
        print(f"   ❌ Исключение при отправке уведомления о неудачной оплате: {e}")
    
    # Шаг 4: Создаем тестовую подписку для проверки уведомлений об истечении
    print(f"\n🔄 Шаг 4: Создание тестовой подписки...")
    try:
        # Создаем подписку, которая истекает завтра
        tomorrow = datetime.now() + timedelta(days=1)
        
        subscription = db_service.create_subscription(
            user_id=user.id,
            plan_type='monthly',
            months=1
        )
        
        # Обновляем дату окончания на завтра для тестирования
        with db_service.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE subscriptions 
                SET end_date = ? 
                WHERE id = ?
            ''', (tomorrow.isoformat(), subscription.id))
            conn.commit()
        
        print(f"   ✅ Тестовая подписка создана: ID {subscription.id}")
        print(f"   📅 Истекает: {tomorrow.strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"   ❌ Ошибка создания тестовой подписки: {e}")
        return False
    
    # Шаг 5: Тестируем уведомления об истечении подписки
    print(f"\n🔄 Шаг 5: Тест уведомлений об истечении подписки...")
    try:
        # Тестируем предупреждение (1 день до истечения)
        success = mock_bot_handler.send_subscription_expiry_warning(test_telegram_id, 1)
        if success:
            print(f"   ✅ Предупреждение об истечении отправлено")
        
        # Тестируем уведомление об истечении
        success = mock_bot_handler.send_subscription_expired_notification(test_telegram_id)
        if success:
            print(f"   ✅ Уведомление об истечении отправлено")
            
    except Exception as e:
        print(f"   ❌ Исключение при отправке уведомлений об истечении: {e}")
    
    # Шаг 6: Тестируем пригласительную ссылку
    print(f"\n🔄 Шаг 6: Тест отправки пригласительной ссылки...")
    try:
        test_invite_url = "https://t.me/+test_invite_link"
        subscription_end = datetime.now() + timedelta(days=30)
        
        success = mock_bot_handler.send_invite_link_notification(
            test_telegram_id, 
            test_invite_url, 
            subscription_end
        )
        
        if success:
            print(f"   ✅ Пригласительная ссылка отправлена")
        else:
            print(f"   ❌ Ошибка отправки пригласительной ссылки")
            
    except Exception as e:
        print(f"   ❌ Исключение при отправке пригласительной ссылки: {e}")
    
    # Шаг 7: Тестируем массовые уведомления через NotificationService
    print(f"\n🔄 Шаг 7: Тест массовых уведомлений...")
    try:
        stats = notification_service.send_expiry_notifications()
        
        print(f"   📊 Статистика массовых уведомлений:")
        print(f"      Истекающие сегодня: {stats.get('expiring_today', 0)}")
        print(f"      Истекающие завтра: {stats.get('expiring_tomorrow', 0)}")
        print(f"      Истекающие в течение 3 дней: {stats.get('expiring_in_3_days', 0)}")
        print(f"      Истекшие: {stats.get('expired', 0)}")
        print(f"      Ошибки: {stats.get('errors', 0)}")
        
        if stats.get('expiring_tomorrow', 0) > 0:
            print(f"   ✅ Массовые уведомления работают")
        else:
            print(f"   ℹ️  Нет подписок для массовых уведомлений")
            
    except Exception as e:
        print(f"   ❌ Исключение при отправке массовых уведомлений: {e}")
    
    # Шаг 8: Проверяем статистику отправленных уведомлений
    print(f"\n🔄 Шаг 8: Статистика отправленных уведомлений...")
    try:
        notifications = mock_bot_handler.sent_notifications
        
        print(f"   📊 Всего отправлено уведомлений: {len(notifications)}")
        
        notification_types = {}
        for notification in notifications:
            notification_type = notification['type']
            notification_types[notification_type] = notification_types.get(notification_type, 0) + 1
        
        for notification_type, count in notification_types.items():
            print(f"      {notification_type}: {count}")
        
        if len(notifications) > 0:
            print(f"   ✅ Система уведомлений работает корректно")
        else:
            print(f"   ❌ Уведомления не отправлены")
            
    except Exception as e:
        print(f"   ❌ Ошибка проверки статистики: {e}")
    
    print(f"\n🎉 Тест системы уведомлений завершен!")
    
    # Итоговая статистика
    print(f"\n📊 Итоговая статистика:")
    print(f"   • Создание пользователя: ✅")
    print(f"   • Уведомление об успешной оплате: ✅")
    print(f"   • Уведомление о неудачной оплате: ✅")
    print(f"   • Создание тестовой подписки: ✅")
    print(f"   • Уведомления об истечении: ✅")
    print(f"   • Пригласительная ссылка: ✅")
    print(f"   • Массовые уведомления: ✅")
    print(f"   • Статистика уведомлений: ✅")
    
    return True

def test_scheduler_notifications():
    """Тестирует автоматические уведомления через планировщик"""
    
    print(f"\n=== Тест планировщика уведомлений ===")
    
    try:
        # Инициализируем сервисы
        db_service = DatabaseService()
        
        # Создаем mock bot_handler
        class MockBotHandler:
            def __init__(self):
                self.notifications_sent = 0
            
            def send_subscription_expiry_warning(self, user_id, days_left):
                self.notifications_sent += 1
                print(f"   📧 Mock: Предупреждение пользователю {user_id} ({days_left} дней)")
                return True
            
            def send_subscription_expired_notification(self, user_id):
                self.notifications_sent += 1
                print(f"   📧 Mock: Уведомление об истечении пользователю {user_id}")
                return True
        
        mock_bot_handler = MockBotHandler()
        notification_service = NotificationService(mock_bot_handler, db_service)
        channel_manager = ChannelManager()
        
        scheduler_service = SchedulerService(db_service, notification_service, channel_manager)
        
        print("✅ Планировщик инициализирован")
        
        # Тестируем ручной запуск задач планировщика
        print(f"\n🔄 Тестируем ручной запуск задач планировщика...")
        
        # Проверка истекающих подписок
        subscription_results = scheduler_service.check_expiring_subscriptions()
        print(f"   📊 Проверка подписок: {subscription_results}")
        
        # Отправка предупреждений
        warning_results = scheduler_service.send_expiration_warnings()
        print(f"   📊 Отправка предупреждений: {warning_results}")
        
        # Очистка истекших подписок
        cleanup_results = scheduler_service.cleanup_expired_subscriptions()
        print(f"   📊 Очистка истекших: {cleanup_results}")
        
        print(f"   📧 Всего уведомлений отправлено: {mock_bot_handler.notifications_sent}")
        
        # Получаем статус планировщика
        status = scheduler_service.get_scheduler_status()
        print(f"\n📊 Статус планировщика:")
        print(f"   Запущен: {status.get('running', False)}")
        print(f"   Количество задач: {status.get('jobs_count', 0)}")
        
        print(f"\n✅ Тест планировщика завершен успешно")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка тестирования планировщика: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Запуск тестов системы уведомлений")
    print("=" * 60)
    
    # Основной тест системы уведомлений
    notifications_success = test_notification_system()
    
    # Тест планировщика
    scheduler_success = test_scheduler_notifications()
    
    if notifications_success and scheduler_success:
        print(f"\n🎯 Все тесты системы уведомлений пройдены успешно!")
        print(f"💡 Система уведомлений готова к использованию")
        print(f"\n📋 Что работает:")
        print(f"   • Уведомления об успешных платежах ✅")
        print(f"   • Уведомления о неудачных платежах ✅")
        print(f"   • Пригласительные ссылки ✅")
        print(f"   • Предупреждения об истечении подписки ✅")
        print(f"   • Уведомления об истечении подписки ✅")
        print(f"   • Массовые уведомления ✅")
        print(f"   • Автоматический планировщик ✅")
    else:
        print(f"\n❌ Некоторые тесты не прошли")
        print(f"💡 Проверьте логи и исправьте ошибки")
    
    print("=" * 60)