# Restart services on server
param(
    [string]$ServerIP = "**************",
    [string]$Username = "ubuntu"
)

Write-Host "Connecting to server to restart services..." -ForegroundColor Green

# Get password securely
$SecurePassword = Read-Host "Enter server password" -AsSecureString
$Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))

Write-Host "Restarting telegram-payment-bot service..." -ForegroundColor Yellow

# Restart telegram-payment-bot service
$RestartBot = @"
echo '$Password' | sudo -S systemctl restart telegram-payment-bot
"@

echo $RestartBot | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host "Restarting nginx service..." -ForegroundColor Yellow

# Restart nginx service  
$RestartNginx = @"
echo '$Password' | sudo -S systemctl restart nginx
"@

echo $RestartNginx | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host "Checking service status..." -ForegroundColor Yellow

# Check services status
$CheckStatus = @"
echo '$Password' | sudo -S systemctl status telegram-payment-bot --no-pager -l
echo "---"
echo '$Password' | sudo -S systemctl status nginx --no-pager -l
"@

echo $CheckStatus | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host "Services restart completed!" -ForegroundColor Green