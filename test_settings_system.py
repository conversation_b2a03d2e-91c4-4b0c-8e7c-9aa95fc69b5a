#!/usr/bin/env python3
"""
Тест системы настроек для задачи 8.5
"""

import os
import sys
import tempfile
from decimal import Decimal

# Добавляем путь к проекту
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.database import DatabaseManager, DatabaseService

def test_settings_system():
    """Тестирование системы настроек"""
    print("🧪 Тестирование системы настроек...")
    
    # Создаем временную базу данных для тестов
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        test_db_path = tmp_file.name
    
    try:
        # Инициализируем базу данных
        db_manager = DatabaseManager(test_db_path)
        db_manager.init_database()
        
        db_service = DatabaseService(db_manager)
        
        print("✅ База данных инициализирована")
        
        # Тестируем системные настройки
        print("\n📋 Тестирование системных настроек...")
        
        # Устанавливаем настройку
        result = db_service.set_system_setting('test', 'sample_setting', 'test_value', 'Тестовая настройка', 'string')
        assert result, "Ошибка установки настройки"
        print("✅ Настройка установлена")
        
        # Получаем настройку
        setting = db_service.get_system_setting('test', 'sample_setting')
        assert setting is not None, "Настройка не найдена"
        assert setting.value == 'test_value', "Неверное значение настройки"
        print("✅ Настройка получена корректно")
        
        # Получаем настройки по категории
        settings = db_service.get_settings_by_category('test')
        assert len(settings) == 1, "Неверное количество настроек в категории"
        print("✅ Настройки по категории получены")
        
        # Тестируем тарифные планы
        print("\n💳 Тестирование тарифных планов...")
        
        # Создаем тарифный план
        plan = db_service.create_subscription_plan(
            name='Тестовый план',
            duration_months=1,
            price=Decimal('299.00'),
            currency='RUB',
            description='Тестовый тарифный план',
            is_active=True,
            sort_order=1
        )
        assert plan is not None, "Ошибка создания тарифного плана"
        print("✅ Тарифный план создан")
        
        # Получаем тарифный план
        retrieved_plan = db_service.get_subscription_plan_by_id(plan.id)
        assert retrieved_plan is not None, "Тарифный план не найден"
        assert retrieved_plan.name == 'Тестовый план', "Неверное название плана"
        assert retrieved_plan.price == Decimal('299.00'), "Неверная цена плана"
        print("✅ Тарифный план получен корректно")
        
        # Обновляем тарифный план
        update_result = db_service.update_subscription_plan(
            plan.id,
            name='Обновленный план',
            price=Decimal('399.00')
        )
        assert update_result, "Ошибка обновления тарифного плана"
        print("✅ Тарифный план обновлен")
        
        # Получаем все тарифные планы
        all_plans = db_service.get_all_subscription_plans()
        assert len(all_plans) >= 4, "Неверное количество тарифных планов (должны быть планы по умолчанию)"
        print("✅ Все тарифные планы получены")
        
        # Тестируем тексты бота
        print("\n🤖 Тестирование текстов бота...")
        
        # Устанавливаем текст бота
        text_result = db_service.set_bot_text('test_key', 'Тестовый текст бота', 'Описание тестового текста')
        assert text_result, "Ошибка установки текста бота"
        print("✅ Текст бота установлен")
        
        # Получаем текст бота
        bot_text = db_service.get_bot_text('test_key')
        assert bot_text is not None, "Текст бота не найден"
        assert bot_text.text == 'Тестовый текст бота', "Неверный текст бота"
        print("✅ Текст бота получен корректно")
        
        # Получаем все тексты бота
        all_texts = db_service.get_all_bot_texts()
        assert len(all_texts) >= 8, "Неверное количество текстов бота (должны быть тексты по умолчанию)"
        print("✅ Все тексты бота получены")
        
        # Тестируем резервное копирование
        print("\n💾 Тестирование резервного копирования...")
        
        # Создаем резервную копию
        backup_path = test_db_path + '.backup'
        backup_result = db_service.create_backup(backup_path)
        assert backup_result, "Ошибка создания резервной копии"
        assert os.path.exists(backup_path), "Файл резервной копии не создан"
        print("✅ Резервная копия создана")
        
        # Проверяем инициализацию настроек по умолчанию
        print("\n⚙️ Проверка настроек по умолчанию...")
        all_settings = db_service.get_all_settings()
        assert len(all_settings) > 0, "Настройки по умолчанию не инициализированы"
        
        # Проверяем наличие основных категорий
        expected_categories = ['api', 'bot', 'subscription', 'system']
        for category in expected_categories:
            assert category in all_settings, f"Категория {category} не найдена"
        
        print("✅ Настройки по умолчанию инициализированы корректно")
        
        print("\n🎉 Все тесты пройдены успешно!")
        print("\n📊 Статистика:")
        print(f"   • Системных настроек: {sum(len(settings) for settings in all_settings.values())}")
        print(f"   • Тарифных планов: {len(all_plans)}")
        print(f"   • Текстов бота: {len(all_texts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка тестирования: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Удаляем временные файлы
        try:
            if os.path.exists(test_db_path):
                os.unlink(test_db_path)
            if os.path.exists(backup_path):
                os.unlink(backup_path)
        except:
            pass

if __name__ == '__main__':
    success = test_settings_system()
    sys.exit(0 if success else 1)