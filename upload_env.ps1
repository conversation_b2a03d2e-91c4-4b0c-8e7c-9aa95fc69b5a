# Upload .env file to the server
$serverIP = "**************"
$localEnvPath = ".\.env"
$remotePath = "/root/.env"

# Check if .env file exists
if (-not (Test-Path $localEnvPath)) {
    Write-Host "Error: .env file not found at $localEnvPath" -ForegroundColor Red
    exit 1
}

# Upload the file using SCP
Write-Host "Uploading .env file to $serverIP..." -ForegroundColor Cyan
try {
    $scpCommand = "scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null \"$localEnvPath\" root@${serverIP}:\"$remotePath\""
    Invoke-Expression $scpCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully uploaded .env file to the server." -ForegroundColor Green
        
        # Restart services
        Write-Host "Restarting services on the server..." -ForegroundColor Cyan
        $restartCommand = "ssh -o StrictHostKeyChecking=no root@${serverIP} 'systemctl restart telegram-bot && systemctl restart nginx'"
        Invoke-Expression $restartCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Services restarted successfully." -ForegroundColor Green
        } else {
            Write-Host "Warning: Could not restart services. Please restart them manually." -ForegroundColor Yellow
        }
    } else {
        Write-Host "Error: Failed to upload .env file." -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "An error occurred: $_" -ForegroundColor Red
    exit 1
}
