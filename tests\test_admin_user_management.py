"""
Тесты для функций управления пользователями и подписками в админ-панели
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from flask import Flask

from app.admin import admin_bp, init_admin_services
from app.models.database import DatabaseService, User, Subscription


@pytest.fixture
def app():
    """Создание тестового Flask приложения"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test_secret_key'
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    
    app.register_blueprint(admin_bp)
    
    return app


@pytest.fixture
def client(app):
    """Создание тестового клиента"""
    return app.test_client()


@pytest.fixture
def mock_db_service():
    """Мок сервиса базы данных"""
    return Mock(spec=DatabaseService)


@pytest.fixture
def authenticated_session(client):
    """Аутентифицированная сессия администратора"""
    with client.session_transaction() as sess:
        sess['admin_logged_in'] = True
        sess['admin_login_time'] = datetime.now().isoformat()


class TestUserManagement:
    """Тесты управления пользователями"""
    
    def test_users_page_with_filters(self, client, mock_db_service, authenticated_session):
        """Тест страницы пользователей с фильтрами"""
        # Подготавливаем тестовые данные
        test_user = User(
            id=1,
            telegram_id=123456789,
            username='testuser',
            first_name='Test',
            last_name='User',
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        test_subscription = Subscription(
            id=1,
            user_id=1,
            plan_type='monthly',
            status='active',
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        users_data = [{
            'user': test_user,
            'subscription': test_subscription
        }]
        
        mock_db_service.get_users_with_filters.return_value = (users_data, 1)
        
        # Инициализируем сервисы
        init_admin_services(mock_db_service, None)
        
        # Тестируем запрос без фильтров
        response = client.get('/admin/users')
        assert response.status_code == 200
        assert b'Test User' in response.data
        
        # Проверяем вызов метода с правильными параметрами
        mock_db_service.get_users_with_filters.assert_called_with(
            search='',
            status_filter='',
            sort_by='created_desc',
            limit=20,
            offset=0
        )
    
    def test_users_page_with_search(self, client, mock_db_service, authenticated_session):
        """Тест поиска пользователей"""
        mock_db_service.get_users_with_filters.return_value = ([], 0)
        init_admin_services(mock_db_service, None)
        
        response = client.get('/admin/users?search=test&status=active&sort=name_asc')
        assert response.status_code == 200
        
        mock_db_service.get_users_with_filters.assert_called_with(
            search='test',
            status_filter='active',
            sort_by='name_asc',
            limit=20,
            offset=0
        )
    
    def test_user_detail_page(self, client, mock_db_service, authenticated_session):
        """Тест страницы детальной информации о пользователе"""
        test_user = User(
            id=1,
            telegram_id=123456789,
            username='testuser',
            first_name='Test',
            last_name='User',
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        mock_db_service.get_user_by_id.return_value = test_user
        mock_db_service.get_user_subscriptions.return_value = []
        mock_db_service.get_user_payments.return_value = []
        mock_db_service.get_user_active_subscription.return_value = None
        
        init_admin_services(mock_db_service, None)
        
        response = client.get('/admin/users/1')
        assert response.status_code == 200
        assert b'Test User' in response.data
        
        # Проверяем вызовы методов
        mock_db_service.get_user_by_id.assert_called_with(1)
        mock_db_service.get_user_subscriptions.assert_called_with(1)
        mock_db_service.get_user_payments.assert_called_with(1)
        mock_db_service.get_user_active_subscription.assert_called_with(1)
    
    def test_user_detail_not_found(self, client, mock_db_service, authenticated_session):
        """Тест страницы пользователя, который не найден"""
        mock_db_service.get_user_by_id.return_value = None
        init_admin_services(mock_db_service, None)
        
        response = client.get('/admin/users/999')
        assert response.status_code == 302  # Редирект на страницу пользователей


class TestSubscriptionManagement:
    """Тесты управления подписками"""
    
    def test_grant_subscription_api(self, client, mock_db_service, authenticated_session):
        """Тест API выдачи подписки"""
        test_user = User(
            id=1,
            telegram_id=123456789,
            username='testuser',
            first_name='Test',
            last_name='User',
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        mock_db_service.get_user_by_id.return_value = test_user
        mock_db_service.admin_grant_subscription.return_value = True
        mock_db_service.log_admin_action.return_value = True
        
        init_admin_services(mock_db_service, None)
        
        response = client.post('/admin/api/grant-subscription',
                             json={'user_id': 1, 'months': 3},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'Подписка выдана успешно' in data['message']
        
        # Проверяем вызовы методов
        mock_db_service.get_user_by_id.assert_called_with(1)
        mock_db_service.admin_grant_subscription.assert_called_with(1, 3)
        mock_db_service.log_admin_action.assert_called_once()
    
    def test_grant_subscription_invalid_params(self, client, mock_db_service, authenticated_session):
        """Тест API выдачи подписки с неверными параметрами"""
        init_admin_services(mock_db_service, None)
        
        # Тест без user_id
        response = client.post('/admin/api/grant-subscription',
                             json={'months': 3},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Неверные параметры' in data['error']
        
        # Тест с неверным количеством месяцев
        response = client.post('/admin/api/grant-subscription',
                             json={'user_id': 1, 'months': 15},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Неверные параметры' in data['error']
    
    def test_revoke_subscription_api(self, client, mock_db_service, authenticated_session):
        """Тест API отзыва подписки"""
        test_user = User(
            id=1,
            telegram_id=123456789,
            username='testuser',
            first_name='Test',
            last_name='User',
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        mock_db_service.get_user_by_id.return_value = test_user
        mock_db_service.admin_revoke_subscription.return_value = True
        mock_db_service.log_admin_action.return_value = True
        
        init_admin_services(mock_db_service, None)
        
        response = client.post('/admin/api/revoke-subscription',
                             json={'user_id': 1},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'Подписка отозвана успешно' in data['message']
        
        # Проверяем вызовы методов
        mock_db_service.get_user_by_id.assert_called_with(1)
        mock_db_service.admin_revoke_subscription.assert_called_with(1)
        mock_db_service.log_admin_action.assert_called_once()
    
    def test_bulk_grant_subscription_api(self, client, mock_db_service, authenticated_session):
        """Тест API массовой выдачи подписок"""
        test_user1 = User(id=1, telegram_id=123, username='user1', first_name='User1', last_name='', created_at=datetime.now(), updated_at=datetime.now())
        test_user2 = User(id=2, telegram_id=456, username='user2', first_name='User2', last_name='', created_at=datetime.now(), updated_at=datetime.now())
        
        def mock_get_user_by_id(user_id):
            if user_id == 1:
                return test_user1
            elif user_id == 2:
                return test_user2
            return None
        
        mock_db_service.get_user_by_id.side_effect = mock_get_user_by_id
        mock_db_service.admin_grant_subscription.return_value = True
        mock_db_service.log_admin_action.return_value = True
        
        init_admin_services(mock_db_service, None)
        
        response = client.post('/admin/api/bulk-grant-subscription',
                             json={'user_ids': ['1', '2'], 'months': 2},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['granted_count'] == 2
        assert len(data['errors']) == 0
        
        # Проверяем количество вызовов
        assert mock_db_service.admin_grant_subscription.call_count == 2
        assert mock_db_service.log_admin_action.call_count == 2
    
    def test_bulk_revoke_subscription_api(self, client, mock_db_service, authenticated_session):
        """Тест API массового отзыва подписок"""
        test_user1 = User(id=1, telegram_id=123, username='user1', first_name='User1', last_name='', created_at=datetime.now(), updated_at=datetime.now())
        test_user2 = User(id=2, telegram_id=456, username='user2', first_name='User2', last_name='', created_at=datetime.now(), updated_at=datetime.now())
        
        def mock_get_user_by_id(user_id):
            if user_id == 1:
                return test_user1
            elif user_id == 2:
                return test_user2
            return None
        
        mock_db_service.get_user_by_id.side_effect = mock_get_user_by_id
        mock_db_service.admin_revoke_subscription.return_value = True
        mock_db_service.log_admin_action.return_value = True
        
        init_admin_services(mock_db_service, None)
        
        response = client.post('/admin/api/bulk-revoke-subscription',
                             json={'user_ids': ['1', '2']},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['revoked_count'] == 2
        assert len(data['errors']) == 0
        
        # Проверяем количество вызовов
        assert mock_db_service.admin_revoke_subscription.call_count == 2
        assert mock_db_service.log_admin_action.call_count == 2
    
    def test_cancel_subscription_api(self, client, mock_db_service, authenticated_session):
        """Тест API отмены конкретной подписки"""
        test_subscription = Subscription(
            id=1,
            user_id=1,
            plan_type='monthly',
            status='active',
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        mock_db_service.get_subscription_by_id.return_value = test_subscription
        mock_db_service.update_subscription_status.return_value = True
        mock_db_service.log_admin_action.return_value = True
        
        init_admin_services(mock_db_service, None)
        
        response = client.post('/admin/api/cancel-subscription',
                             json={'subscription_id': 1},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'Подписка отменена успешно' in data['message']
        
        # Проверяем вызовы методов
        mock_db_service.get_subscription_by_id.assert_called_with(1)
        mock_db_service.update_subscription_status.assert_called_with(1, 'cancelled')
        mock_db_service.log_admin_action.assert_called_once()


class TestDatabaseMethods:
    """Тесты новых методов базы данных"""
    
    def test_get_users_with_filters_search(self):
        """Тест поиска пользователей"""
        # Этот тест требует реальной базы данных или более сложного мокинга
        # Здесь мы проверяем только структуру вызова
        db_service = Mock(spec=DatabaseService)
        
        # Тестируем вызов метода с различными параметрами
        db_service.get_users_with_filters(
            search='test',
            status_filter='active',
            sort_by='name_asc',
            limit=10,
            offset=0
        )
        
        db_service.get_users_with_filters.assert_called_with(
            search='test',
            status_filter='active',
            sort_by='name_asc',
            limit=10,
            offset=0
        )
    
    def test_admin_grant_subscription(self):
        """Тест административной выдачи подписки"""
        db_service = Mock(spec=DatabaseService)
        db_service.admin_grant_subscription.return_value = True
        
        result = db_service.admin_grant_subscription(1, 3)
        assert result is True
        
        db_service.admin_grant_subscription.assert_called_with(1, 3)
    
    def test_admin_revoke_subscription(self):
        """Тест административного отзыва подписки"""
        db_service = Mock(spec=DatabaseService)
        db_service.admin_revoke_subscription.return_value = True
        
        result = db_service.admin_revoke_subscription(1)
        assert result is True
        
        db_service.admin_revoke_subscription.assert_called_with(1)
    
    def test_log_admin_action(self):
        """Тест логирования действий администратора"""
        db_service = Mock(spec=DatabaseService)
        db_service.log_admin_action.return_value = True
        
        result = db_service.log_admin_action(
            admin_telegram_id=0,
            action='grant_subscription',
            target_user_id=1,
            details='Выдана подписка на 3 месяца'
        )
        assert result is True
        
        db_service.log_admin_action.assert_called_with(
            admin_telegram_id=0,
            action='grant_subscription',
            target_user_id=1,
            details='Выдана подписка на 3 месяца'
        )


if __name__ == '__main__':
    pytest.main([__file__])