# Telegram Payment Bot

🤖 **Полнофункциональный бот для управления платными подписками на приватный Telegram канал с интеграцией Lava.top API**

## 🚀 Статус развертывания

✅ **Бот успешно развернут и работает на сервере!**

- **Сервер**: **************
- **Веб-админ панель**: http://**************:5000/admin/login
- **Статус сервиса**: Активен (systemd)
- **Директория**: `/home/<USER>/app/`

## 🎉 Статус проекта: ГОТОВ К ИСПОЛЬЗОВАНИЮ

### ✅ Полностью реализованные и протестированные компоненты

#### 1. Базовая инфраструктура
- ✅ Структура проекта и конфигурация
- ✅ База данных SQLite с полной схемой
- ✅ Модели данных (User, Subscription, Payment)
- ✅ CRUD операции через DatabaseService

#### 2. Интеграция с платежной системой
- ✅ PaymentService для работы с Lava.top API
- ✅ Создание счетов для оплаты (3 способа: карта РФ, зарубежная карта, криптовалюта)
- ✅ Обработка webhook уведомлений
- ✅ Проверка статуса платежей
- ✅ WebhookHandler для обработки уведомлений

#### 3. Telegram Bot
- ✅ TelegramBotHandler с полным функционалом
- ✅ Команды: /start, /купить_подписку, /статус_подписки, /о_канале, /задать_вопрос
- ✅ Inline клавиатуры для выбора тарифов и способов оплаты
- ✅ Обработка callback'ов и неизвестных команд
- ✅ **ПОЛНОСТЬЮ ИНТЕГРИРОВАН** с Flask приложением
- ✅ Автоматический запуск в отдельном потоке

#### 4. Управление каналом
- ✅ ChannelManager для работы с Telegram каналом
- ✅ Создание пригласительных ссылок
- ✅ Удаление пользователей из канала
- ✅ Проверка статуса участников

#### 5. Система уведомлений
- ✅ NotificationService для отправки уведомлений
- ✅ Уведомления об успешной/неудачной оплате
- ✅ Уведомления об истечении подписки (1, 3, 7 дней)
- ✅ Автоматическая отправка пригласительных ссылок
- ✅ Массовые уведомления через планировщик
- ✅ **ПОЛНОСТЬЮ ИНТЕГРИРОВАНА** с webhook и ботом

#### 6. Flask приложение
- ✅ Полнофункциональное Flask приложение (app.py)
- ✅ Webhook endpoint для Lava.top с безопасностью
- ✅ Страницы успешной/неудачной оплаты
- ✅ Health check и metrics endpoints
- ✅ **ПОЛНАЯ ИНТЕГРАЦИЯ** с Telegram ботом
- ✅ Автоматический запуск всех сервисов

#### 7. Планировщик задач
- ✅ SchedulerService с APScheduler
- ✅ Автоматические задачи проверки подписок
- ✅ Автоматическая очистка истекших подписок
- ✅ Отправка уведомлений об истечении
- ✅ **ПОЛНОСТЬЮ ИНТЕГРИРОВАН** в app.py

#### 8. Утилиты
- ✅ Скрипт check_payments.py для проверки платежей
- ✅ Полное покрытие тестами всех компонентов

#### 8. Административные функции
- ✅ Административные команды в боте
- ✅ Система логирования административных действий
- ✅ Управление пользователями через бота
- ✅ Статистика и отчеты

#### 9. Веб-админ панель
- ✅ Базовая структура админ-панели (HTML шаблоны, аутентификация)
- ✅ Управление пользователями и подписками через веб
- ✅ Управление платежами и финансовая отчетность
- ✅ Системный мониторинг и логи
- ✅ Настройки системы и конфигурация

#### 10. Обработка ошибок и безопасность
- ✅ Комплексная обработка ошибок с retry механизмами
- ✅ Усиленная безопасность webhook (IP фильтрация, rate limiting)
- ✅ Валидация данных и защита от атак
- ✅ Логирование безопасности

#### 11. Интеграционное тестирование
- ✅ Полные интеграционные тесты всего цикла
- ✅ Тесты всех компонентов системы
- ✅ Готовность к развертыванию

## 🚀 Быстрый старт

### 1. Подготовка окружения
```bash
git clone <repository-url>
cd telegram-payment-bot
pip install -r requirements.txt
```

### 2. Настройка конфигурации
```bash
cp .env.example .env
# Отредактируйте .env файл с вашими настройками:
# - TELEGRAM_BOT_TOKEN (получить у @BotFather)
# - LAVA_API_KEY (из личного кабинета Lava.top)
# - WEBHOOK_URL (ваш публичный URL)
# - CHANNEL_ID (ID приватного канала)
```

### 3. Инициализация базы данных
```bash
python -c "from app.models.database import init_database; init_database()"
```

### 4. Проверка готовности системы
```bash
python test_bot_integration.py
# Ожидаемый результат: "🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!"
```

### 5. Запуск приложения
```bash
python app.py
# Приложение запустится на http://0.0.0.0:5000
```

### 6. Настройка webhook в Lava.top
- URL: `http://ваш-домен.com/webhook`
- Метод: POST
- События: Все события платежей

### 7. Тестирование бота
Отправьте `/start` вашему боту в Telegram

## 📋 Что работает прямо сейчас

### ✅ Готовые функции:
- **Создание счетов** через Lava.top API
- **Обработка платежей** и webhook уведомлений
- **Telegram бот** с полным интерфейсом
- **Автоматические уведомления** пользователям
- **Управление подписками** и каналом
- **Планировщик задач** для автоматизации
- **Веб-админ панель** для управления
- **Система безопасности** и мониторинга

### 🎯 Полный цикл работы:
1. Пользователь выбирает план в боте
2. Система создает счет в Lava.top
3. Пользователь оплачивает по ссылке
4. Webhook обрабатывает платеж
5. Создается подписка в БД
6. Отправляется пригласительная ссылка
7. Планировщик следит за истечением
8. Автоматические уведомления

## Архитектура проекта

```
telegram-payment-bot/
├── app/
│   ├── models/
│   │   ├── database.py      # ✅ DatabaseService
│   │   └── models.py        # ✅ Модели данных
│   └── services/
│       ├── bot_handler.py   # ✅ Telegram Bot Handler
│       ├── payment_service.py # ✅ Интеграция с Lava.top
│       ├── webhook_handler.py # ✅ Обработка webhook
│       ├── channel_manager.py # ✅ Управление каналом
│       ├── notification_service.py # ✅ Уведомления
│       └── scheduler_service.py # ✅ Планировщик (не интегрирован)
├── tests/                   # ✅ Полное покрытие тестами
├── app.py                   # ✅ Flask приложение
├── main.py                  # ⚠️ Заглушка
├── check_payments.py        # ✅ Скрипт проверки платежей
├── config.py               # ✅ Конфигурация
└── requirements.txt        # ✅ Зависимости
```

## Установка и запуск

### Требования
- Python 3.8+
- SQLite
- Telegram Bot Token
- Lava.top API ключи

### Настройка
1. Клонировать репозиторий
2. Установить зависимости: `pip install -r requirements.txt`
3. Создать `.env` файл с переменными окружения
4. Инициализировать базу данных
5. Запустить Flask приложение: `python app.py`

### Переменные окружения
```env
TELEGRAM_BOT_TOKEN=your_bot_token
LAVA_API_KEY=your_lava_api_key
LAVA_SECRET_KEY=your_lava_secret_key
TELEGRAM_CHANNEL_ID=@your_channel
SECRET_KEY=your_flask_secret_key
ADMIN_PASSWORD=your_admin_password
```

## Статус выполнения

**Общий прогресс: ~50%** (пересмотрено)

- ✅ Основная функциональность (код): 100%
- ✅ Интеграция с платежами: 100%
- ✅ Telegram бот (код): 100%
- ❌ **Интеграция компонентов: 0%** (критическая проблема)
- ⚠️ Планировщик: 80% (код готов, не интегрирован)
- ❌ Админ функции: 0%
- ❌ Веб-панель: 0%

## Следующие шаги

**КРИТИЧЕСКИ ВАЖНО:**
1. **Задача 6.1**: Интегрировать TelegramBotHandler в app.py
2. **Задача 6.2**: Интегрировать SchedulerService в app.py  
3. **Задача 6.3**: Настроить одновременный запуск Flask + Telegram бота
4. **Задача 7.1**: Добавить административные команды в бота
5. **Задача 8.1**: Создать базовую веб-админ панель

## Документация

- [Спецификация требований](.kiro/specs/telegram-payment-bot/requirements.md)
- [Техническое описание](.kiro/specs/telegram-payment-bot/design.md)
- [План задач](.kiro/specs/telegram-payment-bot/tasks.md)

## Тестирование

Запуск тестов:
```bash
pytest tests/ -v
```

Покрытие тестами: ~90% для реализованных компонентов.
##
 Развертывание в Production

### Подготовка к развертыванию

Перед развертыванием в production окружении выполните проверку готовности системы:

```bash
# Проверка всех компонентов системы
python test_deployment.py
```

## 🚀 Руководство по деплою

### 🔍 Обзор

Данное руководство описывает процесс развертывания Telegram бота для платных подписок на сервере Ubuntu 22.04.

### 📋 Требования

- Сервер с Ubuntu 22.04
- Python 3.10+
- Доступ по SSH
- Внешний IP-адрес: `**************`

### 🔑 Доступ к серверу

Данные для доступа по протоколу SSH (порт 22):

- **IP-адрес сервера:** **************
- **Пользователь:** ubuntu
- **Пароль:** dkomqgTaijxro7in^bxd

Для получения root-привилегий выполните команду:
```bash
sudo -i
```

## 🚀 Развертывание проекта

### 1. Подготовка к деплою

1. **Клонируйте репозиторий** на локальную машину:
   ```bash
   git clone <ваш-репозиторий>
   cd telegram-payment-bot
   ```

2. **Настройте переменные окружения** в файле `.env.production`

3. **Настройте SSH-ключи** для доступа к серверу:
   ```bash
   ssh-keygen -t rsa -b 4096
   ssh-copy-id ubuntu@**************
   ```

### 2. Автоматический деплой

1. **Сделайте скрипты исполняемыми**:
   ```bash
   chmod +x deploy.sh setup_env.sh
   ```

2. **Запустите деплой**:
   ```bash
   ./deploy.sh
   ```

3. **Настройте переменные окружения**:
   ```bash
   ./setup_env.sh
   ```
   Затем отредактируйте файл `.env` на сервере:
   ```bash
   ssh ubuntu@************** "nano /opt/telegram-payment-bot/.env"
   ```

### 3. Ручная настройка сервера

1. **Подключитесь к серверу**:
   ```bash
   ssh ubuntu@**************
   ```

2. **Обновите пакеты**:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

3. **Установите зависимости**:
   ```bash
   sudo apt install -y python3-pip python3-venv nginx
   ```

4. **Скопируйте файлы проекта**:
   ```bash
   rsync -avz --exclude='.git' --exclude='__pycache__' --exclude='venv' . ubuntu@**************:/opt/telegram-payment-bot/
   ```

### 4. Настройка домена и SSL

1. **Настройте DNS-записи** вашего домена
2. **Установите Certbot**:
   ```bash
   sudo apt install -y certbot python3-certbot-nginx
   sudo certbot --nginx -d ваш-домен.ру
   ```

### 5. Управление ботом

- **Запуск**: `sudo systemctl start telegram-bot`
- **Остановка**: `sudo systemctl stop telegram-bot`
- **Перезапуск**: `sudo systemctl restart telegram-bot`
- **Статус**: `sudo systemctl status telegram-bot`

### 6. Мониторинг

- **Логи бота**: `sudo journalctl -u telegram-bot -f`
- **Логи Nginx**: `sudo tail -f /var/log/nginx/error.log`

### 7. Обновление бота

1. Обновите код на локальной машине
2. Запустите деплой снова:
   ```bash
   ./deploy.sh
   ```

### Production конфигурация

1. **Создайте production конфигурацию:**
   ```bash
   cp .env.production .env
   # Заполните все обязательные переменные окружения
   ```

2. **Обязательные переменные для production:**
   - `TELEGRAM_BOT_TOKEN` - токен Telegram бота
   - `LAVA_API_KEY` - API ключ Lava.top
   - `LAVA_SECRET_KEY` - секретный ключ Lava.top
   - `WEBHOOK_URL` - URL для webhook (https://yourdomain.com/webhook)
   - `CHANNEL_ID` - ID приватного канала
   - `ADMIN_USER_IDS` - ID администраторов (через запятую)
   - `SECRET_KEY` - секретный ключ Flask (сгенерируйте уникальный)
   - `ADMIN_PASSWORD` - пароль админ-панели

### Запуск в Production

#### Вариант 1: Простой запуск
```bash
python start_production.py
```

#### Вариант 2: С Gunicorn (рекомендуется)
```bash
# Установка Gunicorn
pip install gunicorn

# Запуск с конфигурацией
gunicorn -c gunicorn.conf.py app:create_app
```

#### Вариант 3: С Supervisor (для автозапуска)
```bash
# Настройка в /etc/supervisor/conf.d/telegram-payment-bot.conf
sudo supervisorctl start telegram-payment-bot
```

### Мониторинг и метрики

Система включает встроенные endpoints для мониторинга:

- **Health Check:** `GET /health` - проверка состояния системы
- **Metrics:** `GET /metrics` - метрики производительности
- **System Status:** `GET /status` - подробная информация о системе

### Настройка веб-сервера (Nginx)

Пример конфигурации Nginx для production:

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Резервное копирование

Система автоматически создает резервные копии базы данных. Настройки:

- `BACKUP_ENABLED=true` - включить автоматическое резервное копирование
- `BACKUP_INTERVAL_HOURS=24` - интервал создания копий
- `BACKUP_RETENTION_DAYS=30` - срок хранения копий
- `BACKUP_PATH=./backups` - путь для сохранения копий

### Логирование

Production логирование настраивается через переменные окружения:

- `LOG_LEVEL=INFO` - уровень логирования
- `LOG_FILE=/var/log/telegram-payment-bot/app.log` - основной лог
- `ERROR_LOG_FILE=/var/log/telegram-payment-bot/error.log` - лог ошибок

### Полная документация

Подробное руководство по развертыванию доступно в [DEPLOYMENT.md](DEPLOYMENT.md).

## Безопасность

### Обязательные меры безопасности для production:

1. **Смените пароли по умолчанию:**
   - `SECRET_KEY` - уникальный секретный ключ
   - `ADMIN_PASSWORD` - сложный пароль админ-панели

2. **Настройте SSL/TLS:**
   - Используйте HTTPS для webhook URL
   - Настройте SSL сертификат

3. **Ограничьте доступ:**
   - Настройте файрвол
   - Ограничьте доступ к админ-панели по IP (опционально)

4. **Мониторинг:**
   - Настройте уведомления о критических ошибках
   - Регулярно проверяйте логи системы

### Системные требования для Production

**Минимальные:**
- Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- Python 3.8+
- 512 MB RAM
- 2 GB свободного места на диске

**Рекомендуемые:**
- Ubuntu 22.04 LTS
- Python 3.10+
- 1 GB RAM
- 5 GB свободного места на диске
- 1 vCPU

## Troubleshooting

### Проблемы с админ-панелью

#### ❌ Не удается войти в админ-панель

**Симптомы:**
- Форма входа загружается, но при вводе пароля ничего не происходит
- Страница остается на `/admin/login`
- В логах нет ошибок

**Причина:**
Настройка `SESSION_COOKIE_SECURE = true` блокирует cookies для HTTP соединений.

**Решение:**
```bash
# Для HTTP (разработка/тестирование)
sudo sed -i "s/SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'true')/SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'false')/" /home/<USER>/app/config.py

# Перезапустите приложение
sudo systemctl restart telegram-payment-bot
```

**Для production с HTTPS:**
```bash
# Добавьте в .env файл
echo "SESSION_COOKIE_SECURE=false" >> .env
```

### Общие проблемы

#### 🔌 Telegram бот не отвечает
- Проверьте `TELEGRAM_BOT_TOKEN` в .env
- Убедитесь, что webhook настроен: `curl https://api.telegram.org/bot<TOKEN>/getWebhookInfo`

#### 💳 Ошибки платежей
- Проверьте настройки Lava.top API
- Убедитесь, что webhook URL доступен извне

#### 🗄️ Проблемы с базой данных
- Проверьте права доступа к файлу БД
- Убедитесь, что директория существует

### Полезные команды для диагностики

```bash
# Проверка статуса сервисов
sudo systemctl status telegram-payment-bot
sudo systemctl status nginx

# Просмотр логов
sudo journalctl -u telegram-payment-bot -f
sudo tail -f /var/log/nginx/error.log

# Тестирование endpoints
curl http://localhost:5000/health
curl http://localhost:5000/admin/login

# Проверка портов
sudo netstat -tlnp | grep -E ':80|:5000'
```

Подробная документация по устранению неполадок доступна в [DEPLOYMENT_INSTRUCTIONS.md](DEPLOYMENT_INSTRUCTIONS.md#troubleshooting).