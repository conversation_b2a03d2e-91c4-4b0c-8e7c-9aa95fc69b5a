@echo off
echo Uploading .env file to server with password authentication...

:: Create expect-like script for Windows using PowerShell
powershell -Command "& {
    $securePassword = ConvertTo-SecureString 'your_server_password' -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential ('root', $securePassword)
    
    # Try using pscp with password
    echo 'Uploading file...'
    pscp -pw your_server_password -o StrictHostKeyChecking=no server_env_file root@**************:/root/.env
    
    if ($LASTEXITCODE -eq 0) {
        echo 'File uploaded successfully!'
        echo 'Restarting services...'
        plink -pw your_server_password root@************** 'systemctl restart telegram-bot && systemctl restart nginx'
    } else {
        echo 'Upload failed, trying alternative method...'
        # Alternative: use WinSCP or other tools
    }
}"

pause