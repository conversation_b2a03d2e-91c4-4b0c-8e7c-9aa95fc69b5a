"""
Telegram Bot Handler для управления подписками на канал
"""

import logging
import functools
import telebot
from telebot import types
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta

from config import Config
from app.models.models import User, create_user_from_telegram
from app.models.database import DatabaseService
from app.services.payment_service import PaymentService
from app.services.channel_manager import ChannelManager

logger = logging.getLogger('bot')

class TelegramBotHandler:
    """Обработчик команд Telegram бота"""
    
    def __init__(self, db_service: DatabaseService, payment_service: PaymentService, channel_manager: Optional[ChannelManager] = None):
        """
        Инициализация обработчика бота
        
        Args:
            db_service: Сервис для работы с базой данных
            payment_service: Сервис для работы с платежами
            channel_manager: Менеджер для работы с каналом (опционально)
        """
        self.bot = telebot.TeleBot(Config.TELEGRAM_BOT_TOKEN)
        self.db_service = db_service
        self.payment_service = payment_service
        self.channel_manager = channel_manager or ChannelManager()
        
        # Регистрируем обработчики команд
        self._register_handlers()
        
        logger.info("TelegramBotHandler инициализирован")
    
    def _is_admin(self, user_id: int) -> bool:
        """
        Проверяет, является ли пользователь администратором
        
        Args:
            user_id: ID пользователя Telegram
            
        Returns:
            True если пользователь является администратором
        """
        return user_id in Config.ADMIN_USER_IDS
    
    def _admin_required(self, func):
        """
        Декоратор для проверки прав администратора
        
        Args:
            func: Функция-обработчик команды
            
        Returns:
            Обернутая функция с проверкой прав
        """
        def wrapper(message):
            user_id = message.from_user.id
            
            if not self._is_admin(user_id):
                logger.warning(f"Неавторизованная попытка выполнения административной команды от пользователя {user_id}")
                self.bot.send_message(
                    user_id,
                    "❌ <b>Доступ запрещен</b>\n\nУ вас нет прав для выполнения административных команд.",
                    parse_mode='HTML'
                )
                return
            
            # Логируем административное действие
            try:
                command = message.text.split()[0] if message.text else "unknown"
                self.db_service.create_admin_log(
                    admin_telegram_id=user_id,
                    action=f"admin_command_{command}",
                    details=f"Command: {message.text}"
                )
            except Exception as e:
                logger.error(f"Ошибка логирования административного действия: {e}")
            
            return func(message)
        
        return wrapper
    
    def _admin_required_callback(self, func):
        """
        Декоратор для проверки прав администратора для callback'ов
        
        Args:
            func: Функция-обработчик callback'а
            
        Returns:
            Обернутая функция с проверкой прав
        """
        def wrapper(call):
            user_id = call.from_user.id
            
            if not self._is_admin(user_id):
                logger.warning(f"Неавторизованная попытка выполнения административного callback от пользователя {user_id}")
                self.bot.answer_callback_query(
                    call.id,
                    "❌ Доступ запрещен. У вас нет прав администратора.",
                    show_alert=True
                )
                return
            
            # Логируем административное действие
            try:
                self.db_service.create_admin_log(
                    admin_telegram_id=user_id,
                    action=f"admin_callback_{call.data}",
                    details=f"Callback: {call.data}"
                )
            except Exception as e:
                logger.error(f"Ошибка логирования административного действия: {e}")
            
            return func(call)
        
        return wrapper
    
    def _register_handlers(self):
        """Регистрирует обработчики команд и callback'ов"""
        
        # Команды
        self.bot.message_handler(commands=['start'])(self.handle_start_command)
        self.bot.message_handler(commands=['купить_подписку', 'buy'])(self.handle_buy_subscription)
        self.bot.message_handler(commands=['статус_подписки', 'status'])(self.handle_subscription_status)
        self.bot.message_handler(commands=['о_канале', 'about'])(self.handle_channel_info)
        self.bot.message_handler(commands=['задать_вопрос', 'help'])(self.handle_ask_question)
        
        # Административные команды
        self.bot.message_handler(commands=['admin_info'])(self._admin_required(self.handle_admin_info))
        self.bot.message_handler(commands=['admin_grant'])(self._admin_required(self.handle_admin_grant))
        self.bot.message_handler(commands=['admin_revoke'])(self._admin_required(self.handle_admin_revoke))
        self.bot.message_handler(commands=['admin_stats'])(self._admin_required(self.handle_admin_stats))
        
        # Callback обработчики для inline клавиатур
        self.bot.callback_query_handler(func=lambda call: call.data.startswith('plan_'))(self.handle_plan_selection)
        self.bot.callback_query_handler(func=lambda call: call.data.startswith('payment_'))(self.handle_payment_method_selection)
        self.bot.callback_query_handler(func=lambda call: call.data == 'buy_subscription')(self.handle_buy_subscription_callback)
        self.bot.callback_query_handler(func=lambda call: call.data == 'subscription_status')(self.handle_subscription_status_callback)
        self.bot.callback_query_handler(func=lambda call: call.data == 'channel_info')(self.handle_channel_info_callback)
        self.bot.callback_query_handler(func=lambda call: call.data == 'ask_question')(self.handle_ask_question_callback)
        self.bot.callback_query_handler(func=lambda call: call.data == 'main_menu')(self.handle_main_menu_callback)
        self.bot.callback_query_handler(func=lambda call: call.data == 'back_to_plans')(self.handle_back_to_plans_callback)
        
        # Admin callback handlers
        self.bot.callback_query_handler(func=lambda call: call.data == 'admin_revenue_report')(self._admin_required_callback(self.handle_admin_revenue_report))
        self.bot.callback_query_handler(func=lambda call: call.data == 'admin_subscription_report')(self._admin_required_callback(self.handle_admin_subscription_report))
        self.bot.callback_query_handler(func=lambda call: call.data == 'admin_activity_report')(self._admin_required_callback(self.handle_admin_activity_report))
        self.bot.callback_query_handler(func=lambda call: call.data == 'admin_refresh_stats')(self._admin_required_callback(self.handle_admin_refresh_stats))
        
        # Обработчик неизвестных команд
        self.bot.message_handler(func=lambda message: True)(self.handle_unknown_command)
    
    def send_payment_failed_message(self, user_id: int, order_id: str):
        """
        Отправляет пользователю уведомление о неудачной оплате.

        Args:
            user_id: ID пользователя Telegram
            order_id: ID заказа
        """
        try:
            message = (
                f"❌ <b>Платеж не удался</b>\n\n"
                f"К сожалению, ваш платеж для заказа <code>{order_id}</code> не был успешно обработан.\n\n"
                f"Пожалуйста, попробуйте еще раз или свяжитесь с поддержкой, если проблема не устранена."
            )
            keyboard = types.InlineKeyboardMarkup()
            buy_button = types.InlineKeyboardButton(text="Попробовать снова", callback_data='buy_subscription')
            support_button = types.InlineKeyboardButton(text="Поддержка", callback_data='ask_question')
            keyboard.add(buy_button, support_button)

            self.bot.send_message(
                user_id,
                message,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            logger.info(f"Отправлено уведомление о неудачной оплате пользователю {user_id} для заказа {order_id}")
        except Exception as e:
            logger.error(f"Ошибка при отправке уведомления о неудачной оплате пользователю {user_id}: {e}")

    def handle_start_command(self, message):
        """
        Обработчик команды /start
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            logger.info(f"Команда /start от пользователя {user_id}")
            
            # Создаем или обновляем пользователя в БД
            self._ensure_user_exists(message.from_user)
            
            welcome_text = """
🎉 <b>Добро пожаловать в бот подписок на приватный канал!</b>

Здесь вы можете:
• 💳 Купить подписку на канал
• 📊 Проверить статус подписки
• ℹ️ Узнать о канале
• ❓ Задать вопрос

<b>Доступные команды:</b>
/купить_подписку - Купить доступ к каналу
/статус_подписки - Проверить статус подписки
/о_канале - Информация о канале
/задать_вопрос - Связаться с поддержкой

Выберите нужное действие из меню ниже:
            """
            
            keyboard = self._create_main_menu_keyboard()
            
            self.bot.send_message(
                user_id,
                welcome_text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_start_command: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при обработке команды")
    
    def handle_buy_subscription(self, message):
        """
        Обработчик команды /купить_подписку
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            logger.info(f"Команда /купить_подписку от пользователя {user_id}")
            
            # Создаем или обновляем пользователя в БД
            self._ensure_user_exists(message.from_user)
            
            # Проверяем, есть ли у пользователя активная подписка
            user = self.db_service.get_user_by_telegram_id(user_id)
            subscription = self.db_service.get_user_active_subscription(user.id) if user else None
            
            if subscription and subscription.is_active():
                days_left = subscription.days_until_expiry()
                text = f"""
📋 <b>У вас уже есть активная подписка!</b>

⏰ Действует до: {subscription.end_date.strftime('%d.%m.%Y %H:%M')}
📅 Осталось дней: {days_left}

Вы можете продлить подписку, выбрав тариф ниже:
                """
            else:
                text = """
💳 <b>Выберите тарифный план подписки:</b>

Все планы включают полный доступ к приватному каналу с эксклюзивным контентом.
                """
            
            keyboard = self._create_subscription_plans_keyboard()
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_buy_subscription: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при загрузке тарифов")
    
    def handle_subscription_status(self, message):
        """
        Обработчик команды /статус_подписки
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            logger.info(f"Команда /статус_подписки от пользователя {user_id}")
            
            # Создаем или обновляем пользователя в БД
            self._ensure_user_exists(message.from_user)
            
            # Получаем активную подписку
            user = self.db_service.get_user_by_telegram_id(user_id)
            subscription = self.db_service.get_user_active_subscription(user.id) if user else None
            
            if subscription and subscription.is_active():
                days_left = subscription.days_until_expiry()
                
                # Определяем статус по количеству оставшихся дней
                if days_left > 7:
                    status_emoji = "✅"
                    status_text = "Активна"
                elif days_left > 1:
                    status_emoji = "⚠️"
                    status_text = "Скоро истечет"
                else:
                    status_emoji = "🔴"
                    status_text = "Истекает сегодня"
                
                text = f"""
📊 <b>Статус вашей подписки</b>

{status_emoji} <b>Статус:</b> {status_text}
📅 <b>Действует до:</b> {subscription.end_date.strftime('%d.%m.%Y %H:%M')}
⏰ <b>Осталось дней:</b> {days_left}
📋 <b>Тип подписки:</b> {self._get_plan_display_name(subscription.plan_type)}

{self._get_status_advice(days_left)}
                """
                
                keyboard = self._create_status_keyboard(days_left)
                
            else:
                text = """
❌ <b>У вас нет активной подписки</b>

Чтобы получить доступ к приватному каналу, необходимо приобрести подписку.

Выберите подходящий тарифный план:
                """
                
                keyboard = self._create_subscription_plans_keyboard()
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_subscription_status: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при получении статуса подписки")
    
    def handle_plan_selection(self, call):
        """
        Обработчик выбора тарифного плана
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            plan_months = int(call.data.split('_')[1])  # plan_1, plan_3, etc.
            
            logger.info(f"Пользователь {user_id} выбрал план на {plan_months} месяцев")
            
            # Получаем информацию о плане
            plans = self.payment_service.get_subscription_plans()
            plan = plans.get(plan_months)
            
            if not plan:
                self.bot.answer_callback_query(call.id, "Ошибка: неверный тарифный план")
                return
            
            text = f"""
💳 <b>Выбран план: {plan['name']}</b>
💰 <b>Стоимость:</b> {plan['price']} ₽

Выберите способ оплаты:
            """
            
            keyboard = self._create_payment_methods_keyboard(plan_months)
            
            self.bot.edit_message_text(
                text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_plan_selection: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при выборе плана")
    
    def handle_payment_method_selection(self, call):
        """
        Обработчик выбора способа оплаты
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            callback_data = call.data.split('_')  # payment_card_ru_1 -> ['payment', 'card', 'ru', '1']
            
            # Для callback вида payment_card_ru_1 или payment_card_foreign_1 или payment_crypto_1
            if len(callback_data) >= 3:
                if callback_data[1] == 'card' and callback_data[2] == 'ru':
                    payment_method = 'card_ru'
                    plan_months = int(callback_data[3])
                elif callback_data[1] == 'card' and callback_data[2] == 'foreign':
                    payment_method = 'card_foreign'
                    plan_months = int(callback_data[3])
                elif callback_data[1] == 'crypto':
                    payment_method = 'crypto'
                    plan_months = int(callback_data[2])
                else:
                    raise ValueError(f"Неизвестный способ оплаты: {call.data}")
            else:
                raise ValueError(f"Неверный формат callback данных: {call.data}")
            
            logger.info(f"Пользователь {user_id} выбрал способ оплаты {payment_method} для плана {plan_months} месяцев")
            
            # Создаем счет для оплаты
            invoice_result = self.payment_service.create_invoice(user_id, plan_months, payment_method)
            
            if invoice_result['success']:
                # Сохраняем платеж в БД
                user = self.db_service.get_user_by_telegram_id(user_id)
                if user:
                    from decimal import Decimal
                    self.db_service.create_payment(
                        user_id=user.id,
                        lava_invoice_id=invoice_result['order_id'],
                        amount=Decimal(str(invoice_result['amount'])),
                        payment_method=payment_method,
                        payment_url=invoice_result['payment_url'],
                        expires_at=invoice_result['expires_at']
                    )
                
                # Отправляем ссылку для оплаты
                self.send_payment_link(user_id, invoice_result, plan_months)
                
            else:
                error_text = f"""
❌ <b>Ошибка создания счета для оплаты</b>

{invoice_result.get('error', 'Неизвестная ошибка')}

Попробуйте еще раз или выберите другой способ оплаты.
                """
                
                keyboard = self._create_payment_methods_keyboard(plan_months)
                
                self.bot.edit_message_text(
                    error_text,
                    user_id,
                    call.message.message_id,
                    parse_mode='HTML',
                    reply_markup=keyboard
                )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_payment_method_selection: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при создании счета")
    
    def send_payment_link(self, user_id: int, invoice_data: Dict[str, Any], plan_months: int) -> bool:
        """
        Отправляет ссылку для оплаты пользователю
        
        Args:
            user_id: ID пользователя Telegram
            invoice_data: Данные созданного счета
            plan_months: Количество месяцев подписки
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            plans = self.payment_service.get_subscription_plans()
            plan = plans.get(plan_months, {})
            
            payment_methods = self.payment_service.get_available_payment_methods()
            method_name = payment_methods.get(invoice_data['payment_method'], {}).get('name', 'Неизвестный способ')
            
            expires_at = invoice_data['expires_at']
            expires_str = expires_at.strftime('%d.%m.%Y в %H:%M')
            
            text = f"""
💳 <b>Счет для оплаты создан!</b>

📋 <b>План:</b> {plan.get('name', f'{plan_months} месяцев')}
💰 <b>Сумма:</b> {invoice_data['amount']} {invoice_data['currency']}
💳 <b>Способ оплаты:</b> {method_name}
⏰ <b>Действителен до:</b> {expires_str}

<b>Для оплаты нажмите кнопку ниже:</b>
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    f"💳 Оплатить {invoice_data['amount']} ₽",
                    url=invoice_data['payment_url']
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔙 Назад к выбору тарифов",
                    callback_data="back_to_plans"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Ссылка для оплаты отправлена пользователю {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки ссылки для оплаты пользователю {user_id}: {str(e)}")
            self._send_error_message(user_id, "Произошла ошибка при отправке ссылки для оплаты")
            return False
    
    def handle_channel_info(self, message):
        """
        Обработчик команды /о_канале
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            logger.info(f"Команда /о_канале от пользователя {user_id}")
            
            text = """
📺 <b>О нашем приватном канале</b>

🎯 <b>Что вы получите:</b>
• Эксклюзивный контент, недоступный в открытых источниках
• Ежедневные обновления и новости
• Аналитические материалы от экспертов
• Прямое общение с авторами канала
• Архив всех материалов

💎 <b>Преимущества подписки:</b>
• Доступ ко всем материалам 24/7
• Уведомления о новых публикациях
• Возможность задавать вопросы авторам
• Участие в закрытых обсуждениях

📊 <b>Статистика канала:</b>
• Более 1000 подписчиков
• Ежедневно 5-10 новых материалов
• Средняя оценка контента: 4.8/5

<b>Присоединяйтесь к нашему сообществу!</b>
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "💳 Купить подписку",
                    callback_data="buy_subscription"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "❓ Задать вопрос",
                    callback_data="ask_question"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_channel_info: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при загрузке информации о канале")
    
    def handle_ask_question(self, message):
        """
        Обработчик команды /задать_вопрос
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            logger.info(f"Команда /задать_вопрос от пользователя {user_id}")
            
            text = """
❓ <b>Поддержка и контакты</b>

Если у вас есть вопросы или проблемы, вы можете обратиться к нам:

📧 <b>Email:</b> <EMAIL>
💬 <b>Telegram:</b> @support_bot
📞 <b>Телефон:</b> +7 (999) 123-45-67

🕐 <b>Время работы поддержки:</b>
Понедельник - Пятница: 9:00 - 18:00 (МСК)
Суббота - Воскресенье: 10:00 - 16:00 (МСК)

<b>Часто задаваемые вопросы:</b>

<b>Q:</b> Как получить доступ к каналу после оплаты?
<b>A:</b> После успешной оплаты вы автоматически получите пригласительную ссылку в этом боте.

<b>Q:</b> Можно ли вернуть деньги?
<b>A:</b> Возврат возможен в течение 24 часов после покупки при технических проблемах.

<b>Q:</b> Что делать, если оплата прошла, но доступа нет?
<b>A:</b> Обратитесь в поддержку с номером заказа, мы решим проблему в течение часа.
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "📧 Написать в поддержку",
                    url="https://t.me/support_bot"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔙 Главное меню",
                    callback_data="main_menu"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_ask_question: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при загрузке контактной информации")
    
    def handle_unknown_command(self, message):
        """
        Обработчик неизвестных команд и сообщений
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            
            # Игнорируем callback данные и системные сообщения
            if hasattr(message, 'content_type') and message.content_type != 'text':
                return
            
            logger.info(f"Неизвестная команда от пользователя {user_id}: {message.text}")
            
            text = """
❓ <b>Команда не распознана</b>

Я не понимаю эту команду. Вот список доступных команд:

<b>Основные команды:</b>
/start - Главное меню
/купить_подписку - Купить доступ к каналу
/статус_подписки - Проверить статус подписки
/о_канале - Информация о канале
/задать_вопрос - Связаться с поддержкой

Выберите нужное действие из меню ниже:
            """
            
            keyboard = self._create_main_menu_keyboard()
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_unknown_command: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при обработке сообщения")
    
    def handle_buy_subscription_callback(self, call):
        """
        Обработчик callback для кнопки "Купить подписку"
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback buy_subscription от пользователя {user_id}")
            
            # Создаем или обновляем пользователя в БД
            self._ensure_user_exists(call.from_user)
            
            # Проверяем, есть ли у пользователя активная подписка
            user = self.db_service.get_user_by_telegram_id(user_id)
            subscription = self.db_service.get_user_active_subscription(user.id) if user else None
            
            if subscription and subscription.is_active():
                days_left = subscription.days_until_expiry()
                text = f"""
📋 <b>У вас уже есть активная подписка!</b>

⏰ Действует до: {subscription.end_date.strftime('%d.%m.%Y %H:%M')}
📅 Осталось дней: {days_left}

Вы можете продлить подписку, выбрав тариф ниже:
                """
            else:
                text = """
💳 <b>Выберите тарифный план подписки:</b>

Все планы включают полный доступ к приватному каналу с эксклюзивным контентом.
                """
            
            keyboard = self._create_subscription_plans_keyboard()
            
            self.bot.edit_message_text(
                text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_buy_subscription_callback: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при загрузке тарифов")
    
    def handle_subscription_status_callback(self, call):
        """
        Обработчик callback для кнопки "Статус подписки"
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback subscription_status от пользователя {user_id}")
            
            # Создаем или обновляем пользователя в БД
            self._ensure_user_exists(call.from_user)
            
            # Получаем активную подписку
            user = self.db_service.get_user_by_telegram_id(user_id)
            subscription = self.db_service.get_user_active_subscription(user.id) if user else None
            
            if subscription and subscription.is_active():
                days_left = subscription.days_until_expiry()
                
                # Определяем статус по количеству оставшихся дней
                if days_left > 7:
                    status_emoji = "✅"
                    status_text = "Активна"
                elif days_left > 1:
                    status_emoji = "⚠️"
                    status_text = "Скоро истечет"
                else:
                    status_emoji = "🔴"
                    status_text = "Истекает сегодня"
                
                text = f"""
📊 <b>Статус вашей подписки</b>

{status_emoji} <b>Статус:</b> {status_text}
📅 <b>Действует до:</b> {subscription.end_date.strftime('%d.%m.%Y %H:%M')}
⏰ <b>Осталось дней:</b> {days_left}
📋 <b>Тип подписки:</b> {self._get_plan_display_name(subscription.plan_type)}

{self._get_status_advice(days_left)}
                """
                
                keyboard = self._create_status_keyboard(days_left)
                
            else:
                text = """
❌ <b>У вас нет активной подписки</b>

Чтобы получить доступ к приватному каналу, необходимо приобрести подписку.

Выберите подходящий тарифный план:
                """
                
                keyboard = self._create_subscription_plans_keyboard()
            
            self.bot.edit_message_text(
                text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_subscription_status_callback: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при получении статуса подписки")
    
    def handle_channel_info_callback(self, call):
        """
        Обработчик callback для кнопки "О канале"
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback channel_info от пользователя {user_id}")
            
            text = """
📺 <b>О нашем приватном канале</b>

🎯 <b>Что вы получите:</b>
• Эксклюзивный контент, недоступный в открытых источниках
• Ежедневные обновления и новости
• Аналитические материалы от экспертов
• Прямое общение с авторами канала
• Архив всех материалов

💎 <b>Преимущества подписки:</b>
• Доступ ко всем материалам 24/7
• Уведомления о новых публикациях
• Возможность задавать вопросы авторам
• Участие в закрытых обсуждениях

📊 <b>Статистика канала:</b>
• Более 1000 подписчиков
• Ежедневно 5-10 новых материалов
• Средняя оценка контента: 4.8/5

<b>Присоединяйтесь к нашему сообществу!</b>
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "💳 Купить подписку",
                    callback_data="buy_subscription"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "❓ Задать вопрос",
                    callback_data="ask_question"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔙 Главное меню",
                    callback_data="main_menu"
                )
            )
            
            self.bot.edit_message_text(
                text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_channel_info_callback: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при загрузке информации о канале")
    
    def handle_ask_question_callback(self, call):
        """
        Обработчик callback для кнопки "Задать вопрос"
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback ask_question от пользователя {user_id}")
            
            text = """
❓ <b>Поддержка и контакты</b>

Если у вас есть вопросы или проблемы, вы можете обратиться к нам:

📧 <b>Email:</b> <EMAIL>
💬 <b>Telegram:</b> @support_bot
📞 <b>Телефон:</b> +7 (999) 123-45-67

🕐 <b>Время работы поддержки:</b>
Понедельник - Пятница: 9:00 - 18:00 (МСК)
Суббота - Воскресенье: 10:00 - 16:00 (МСК)

<b>Часто задаваемые вопросы:</b>

<b>Q:</b> Как получить доступ к каналу после оплаты?
<b>A:</b> После успешной оплаты вы автоматически получите пригласительную ссылку в этом боте.

<b>Q:</b> Можно ли вернуть деньги?
<b>A:</b> Возврат возможен в течение 24 часов после покупки при технических проблемах.

<b>Q:</b> Что делать, если оплата прошла, но доступа нет?
<b>A:</b> Обратитесь в поддержку с номером заказа, мы решим проблему в течение часа.
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "📧 Написать в поддержку",
                    url="https://t.me/support_bot"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔙 Главное меню",
                    callback_data="main_menu"
                )
            )
            
            self.bot.edit_message_text(
                text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_ask_question_callback: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при загрузке контактной информации")
    
    def handle_main_menu_callback(self, call):
        """
        Обработчик callback для кнопки "Главное меню"
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback main_menu от пользователя {user_id}")
            
            welcome_text = """
🎉 <b>Главное меню</b>

Здесь вы можете:
• 💳 Купить подписку на канал
• 📊 Проверить статус подписки
• ℹ️ Узнать о канале
• ❓ Задать вопрос

Выберите нужное действие из меню ниже:
            """
            
            keyboard = self._create_main_menu_keyboard()
            
            self.bot.edit_message_text(
                welcome_text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_main_menu_callback: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при загрузке главного меню")
    
    def handle_back_to_plans_callback(self, call):
        """
        Обработчик callback для кнопки "Назад к тарифам"
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback back_to_plans от пользователя {user_id}")
            
            text = """
💳 <b>Выберите тарифный план подписки:</b>

Все планы включают полный доступ к приватному каналу с эксклюзивным контентом.
            """
            
            keyboard = self._create_subscription_plans_keyboard()
            
            self.bot.edit_message_text(
                text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_back_to_plans_callback: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при загрузке тарифов")
    
    def _ensure_user_exists(self, telegram_user):
        """
        Создает пользователя в БД если его нет
        
        Args:
            telegram_user: Объект пользователя от Telegram
        """
        try:
            user = self.db_service.get_user_by_telegram_id(telegram_user.id)
            if not user:
                user_model = create_user_from_telegram(telegram_user)
                self.db_service.create_user(user_model)
                logger.info(f"Создан новый пользователь: {telegram_user.id}")
            
        except Exception as e:
            logger.error(f"Ошибка создания пользователя {telegram_user.id}: {str(e)}")
            raise  # Re-raise the exception so it can be caught by the calling method
    
    def _create_main_menu_keyboard(self) -> types.InlineKeyboardMarkup:
        """Создает клавиатуру главного меню"""
        keyboard = types.InlineKeyboardMarkup()
        
        keyboard.add(
            types.InlineKeyboardButton(
                "💳 Купить подписку",
                callback_data="buy_subscription"
            )
        )
        keyboard.add(
            types.InlineKeyboardButton(
                "📊 Статус подписки",
                callback_data="subscription_status"
            )
        )
        keyboard.add(
            types.InlineKeyboardButton(
                "ℹ️ О канале",
                callback_data="channel_info"
            ),
            types.InlineKeyboardButton(
                "❓ Задать вопрос",
                callback_data="ask_question"
            )
        )
        
        return keyboard
    
    def _create_subscription_plans_keyboard(self) -> types.InlineKeyboardMarkup:
        """Создает клавиатуру выбора тарифных планов"""
        keyboard = types.InlineKeyboardMarkup()
        
        plans = self.payment_service.get_subscription_plans()
        
        for months, plan in plans.items():
            # Вычисляем скидку для планов больше 1 месяца
            monthly_price = plans[1]['price']
            total_monthly = monthly_price * months
            discount = 0
            
            if months > 1:
                discount = int(((total_monthly - plan['price']) / total_monthly) * 100)
            
            button_text = f"{plan['name']} - {plan['price']} ₽"
            if discount > 0:
                button_text += f" (скидка {discount}%)"
            
            keyboard.add(
                types.InlineKeyboardButton(
                    button_text,
                    callback_data=f"plan_{months}"
                )
            )
        
        keyboard.add(
            types.InlineKeyboardButton(
                "🔙 Главное меню",
                callback_data="main_menu"
            )
        )
        
        return keyboard
    
    def _create_payment_methods_keyboard(self, plan_months: int) -> types.InlineKeyboardMarkup:
        """
        Создает клавиатуру выбора способов оплаты
        
        Args:
            plan_months: Количество месяцев выбранного плана
        """
        keyboard = types.InlineKeyboardMarkup()
        
        payment_methods = self.payment_service.get_available_payment_methods()
        
        for method_key, method_info in payment_methods.items():
            keyboard.add(
                types.InlineKeyboardButton(
                    f"💳 {method_info['name']}",
                    callback_data=f"payment_{method_key}_{plan_months}"
                )
            )
        
        keyboard.add(
            types.InlineKeyboardButton(
                "🔙 Назад к тарифам",
                callback_data="back_to_plans"
            )
        )
        
        return keyboard
    
    def _create_status_keyboard(self, days_left: int, is_in_channel: bool = True) -> types.InlineKeyboardMarkup:
        """
        Создает клавиатуру для страницы статуса подписки
        
        Args:
            days_left: Количество дней до истечения подписки
            is_in_channel: Находится ли пользователь в канале
        """
        keyboard = types.InlineKeyboardMarkup()
        
        if days_left <= 7:
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔄 Продлить подписку",
                    callback_data="buy_subscription"
                )
            )
        
        keyboard.add(
            types.InlineKeyboardButton(
                "🔙 Главное меню",
                callback_data="main_menu"
            )
        )
        
        return keyboard
    
    def _get_plan_display_name(self, plan_type: str) -> str:
        """
        Получает отображаемое название плана
        
        Args:
            plan_type: Тип плана из БД
            
        Returns:
            Отображаемое название плана
        """
        plan_names = {
            'monthly': '1 месяц',
            'quarterly': '3 месяца',
            'semi_annual': '6 месяцев',
            'yearly': '12 месяцев'
        }
        return plan_names.get(plan_type, plan_type)
    
    def _get_status_advice(self, days_left: int, is_in_channel: bool = True) -> str:
        """
        Получает совет пользователю в зависимости от оставшихся дней и статуса в канале
        
        Args:
            days_left: Количество дней до истечения
            is_in_channel: Находится ли пользователь в канале
            
        Returns:
            Текст совета
        """
        if not is_in_channel:
            return "⚠️ Вы не находитесь в канале! Обратитесь в поддержку для получения новой пригласительной ссылки."
        
        if days_left > 30:
            return "✅ Ваша подписка активна. Наслаждайтесь контентом!"
        elif days_left > 7:
            return "⚠️ Рекомендуем продлить подписку заранее, чтобы не потерять доступ."
        elif days_left > 1:
            return "🔴 Подписка скоро истечет! Продлите её, чтобы сохранить доступ к каналу."
        else:
            return "🚨 Подписка истекает сегодня! Продлите её прямо сейчас."
    
    def _get_subscription_status_with_channel_check(self, user_id: int, subscription):
        """
        Получает статус подписки с проверкой присутствия в канале
        
        Args:
            user_id: ID пользователя Telegram
            subscription: Объект подписки
            
        Returns:
            Кортеж (status_emoji, status_text, channel_status, is_in_channel)
        """
        days_left = subscription.days_until_expiry()
        
        # Проверяем статус пользователя в канале с обработкой ошибок
        try:
            is_in_channel = self.channel_manager.check_user_in_channel(user_id)
        except Exception as e:
            logger.warning(f"Ошибка при проверке статуса пользователя {user_id} в канале: {e}")
            is_in_channel = False  # В случае ошибки считаем, что пользователя нет в канале
        
        # Определяем статус по количеству оставшихся дней и присутствию в канале
        if days_left > 7:
            status_emoji = "✅" if is_in_channel else "⚠️"
            status_text = "Активна" if is_in_channel else "Активна (не в канале)"
        elif days_left > 1:
            status_emoji = "⚠️"
            status_text = "Скоро истечет" if is_in_channel else "Скоро истечет (не в канале)"
        else:
            status_emoji = "🔴"
            status_text = "Истекает сегодня" if is_in_channel else "Истекает сегодня (не в канале)"
        
        # Добавляем информацию о статусе в канале
        channel_status = "✅ В канале" if is_in_channel else "❌ Не в канале"
        
        return status_emoji, status_text, channel_status, is_in_channel

    def _send_error_message(self, user_id: int, error_text: str):
        """
        Отправляет сообщение об ошибке пользователю
        
        Args:
            user_id: ID пользователя
            error_text: Текст ошибки
        """
        try:
            full_text = f"""
❌ <b>Произошла ошибка</b>

{error_text}

Попробуйте еще раз или обратитесь в поддержку.
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔄 Попробовать снова",
                    callback_data="main_menu"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "❓ Обратиться в поддержку",
                    callback_data="ask_question"
                )
            )
            
            self.bot.send_message(
                user_id,
                full_text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"Ошибка отправки сообщения об ошибке пользователю {user_id}: {str(e)}")
    
    def start_polling(self):
        """Запускает бота в режиме polling"""
        try:
            logger.info("Запуск Telegram бота в режиме polling...")
            self.bot.polling(none_stop=True, interval=1, timeout=60)
        except Exception as e:
            logger.error(f"Ошибка при запуске бота: {str(e)}")
            raise
    
    def stop_polling(self):
        """Останавливает polling бота"""
        try:
            logger.info("Остановка Telegram бота...")
            self.bot.stop_polling()
        except Exception as e:
            logger.error(f"Ошибка при остановке бота: {str(e)}")
    
    def send_invite_link(self, user_id: int, invite_url: str, subscription_end_date: datetime) -> bool:
        """
        Отправляет пригласительную ссылку пользователю после успешной оплаты
        
        Args:
            user_id: ID пользователя Telegram
            invite_url: Пригласительная ссылка на канал
            subscription_end_date: Дата окончания подписки
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            end_date_str = subscription_end_date.strftime('%d.%m.%Y в %H:%M')
            
            text = f"""
🎉 <b>Оплата прошла успешно!</b>

✅ Ваша подписка активирована
📅 <b>Действует до:</b> {end_date_str}

🔗 <b>Ссылка для входа в приватный канал:</b>
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "🚀 Войти в канал",
                    url=invite_url
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "📊 Статус подписки",
                    callback_data="subscription_status"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Пригласительная ссылка отправлена пользователю {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки пригласительной ссылки пользователю {user_id}: {str(e)}")
            return False
    
    def send_expiration_warning(self, user_id: int, days_left: int, subscription_end_date: datetime) -> bool:
        """
        Отправляет предупреждение об истечении подписки
        
        Args:
            user_id: ID пользователя Telegram
            days_left: Количество дней до истечения
            subscription_end_date: Дата окончания подписки
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            end_date_str = subscription_end_date.strftime('%d.%m.%Y в %H:%M')
            
            if days_left == 7:
                emoji = "⚠️"
                title = "Подписка истекает через неделю"
                urgency_text = "У вас есть время продлить подписку заранее."
            elif days_left == 1:
                emoji = "🔴"
                title = "Подписка истекает завтра!"
                urgency_text = "Продлите подписку сегодня, чтобы не потерять доступ к каналу."
            else:
                emoji = "⏰"
                title = f"Подписка истекает через {days_left} дней"
                urgency_text = "Рекомендуем продлить подписку заранее."
            
            text = f"""
{emoji} <b>{title}</b>

📅 <b>Подписка действует до:</b> {end_date_str}
⏰ <b>Осталось дней:</b> {days_left}

{urgency_text}

<b>Продлите подписку прямо сейчас:</b>
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔄 Продлить подписку",
                    callback_data="buy_subscription"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "📊 Статус подписки",
                    callback_data="subscription_status"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Предупреждение об истечении подписки отправлено пользователю {user_id} (осталось {days_left} дней)")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки предупреждения об истечении пользователю {user_id}: {str(e)}")
            return False
    
    def send_subscription_expired_notification(self, user_id: int) -> bool:
        """
        Отправляет уведомление об истечении подписки
        
        Args:
            user_id: ID пользователя Telegram
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            text = """
❌ <b>Ваша подписка истекла</b>

Доступ к приватному каналу приостановлен.

Чтобы восстановить доступ, приобретите новую подписку:
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "💳 Купить подписку",
                    callback_data="buy_subscription"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "ℹ️ О канале",
                    callback_data="channel_info"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Уведомление об истечении подписки отправлено пользователю {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки уведомления об истечении пользователю {user_id}: {str(e)}")
            return False
    
    def send_payment_confirmation(self, user_id: int, payment_amount: float, payment_method: str) -> bool:
        """
        Отправляет подтверждение об успешной оплате
        
        Args:
            user_id: ID пользователя Telegram
            payment_amount: Сумма платежа
            payment_method: Способ оплаты
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            payment_methods = self.payment_service.get_available_payment_methods()
            method_name = payment_methods.get(payment_method, {}).get('name', 'Неизвестный способ')
            
            text = f"""
✅ <b>Платеж успешно обработан!</b>

💰 <b>Сумма:</b> {payment_amount} ₽
💳 <b>Способ оплаты:</b> {method_name}
📅 <b>Дата:</b> {datetime.now().strftime('%d.%m.%Y в %H:%M')}

Ваша подписка будет активирована в течение нескольких минут.
Пригласительная ссылка будет отправлена отдельным сообщением.
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "📊 Статус подписки",
                    callback_data="subscription_status"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Подтверждение оплаты отправлено пользователю {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки подтверждения оплаты пользователю {user_id}: {str(e)}")
            return False
    
    def send_bulk_notification(self, user_ids: List[int], text: str, 
                              reply_markup: Optional[types.InlineKeyboardMarkup] = None) -> Dict[int, bool]:
        """
        Отправляет массовое уведомление списку пользователей
        
        Args:
            user_ids: Список ID пользователей
            text: Текст сообщения
            reply_markup: Клавиатура (опционально)
            
        Returns:
            Словарь с результатами отправки для каждого пользователя
        """
        results = {}
        
        for user_id in user_ids:
            try:
                self.bot.send_message(
                    user_id,
                    text,
                    parse_mode='HTML',
                    reply_markup=reply_markup
                )
                results[user_id] = True
                logger.info(f"Массовое уведомление отправлено пользователю {user_id}")
                
                # Небольшая задержка между отправками для избежания rate limiting
                import time
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Ошибка отправки массового уведомления пользователю {user_id}: {str(e)}")
                results[user_id] = False
        
        successful_count = sum(1 for success in results.values() if success)
        logger.info(f"Массовое уведомление: отправлено {successful_count} из {len(user_ids)} сообщений")
        
        return results

    def send_message(self, user_id: int, text: str, parse_mode: str = 'HTML', 
                    reply_markup: Optional[types.InlineKeyboardMarkup] = None) -> bool:
        """
        Отправляет сообщение пользователю
        
        Args:
            user_id: ID пользователя
            text: Текст сообщения
            parse_mode: Режим парсинга (HTML/Markdown)
            reply_markup: Клавиатура
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            self.bot.send_message(
                user_id,
                text,
                parse_mode=parse_mode,
                reply_markup=reply_markup
            )
            return True
        except Exception as e:
            logger.error(f"Ошибка отправки сообщения пользователю {user_id}: {str(e)}")
            return False
    
    # ==================== АДМИНИСТРАТИВНЫЕ КОМАНДЫ ====================
    
    def handle_admin_info(self, message):
        """
        Обработчик команды /admin_info <user_id>
        Показывает информацию о пользователе
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            args = message.text.split()[1:] if len(message.text.split()) > 1 else []
            
            if not args:
                self.bot.send_message(
                    user_id,
                    "❌ <b>Неверный формат команды</b>\n\n"
                    "Использование: <code>/admin_info &lt;telegram_id&gt;</code>\n"
                    "Пример: <code>/admin_info 123456789</code>",
                    parse_mode='HTML'
                )
                return
            
            try:
                target_telegram_id = int(args[0])
            except ValueError:
                self.bot.send_message(
                    user_id,
                    "❌ <b>Неверный ID пользователя</b>\n\n"
                    "ID должен быть числом.",
                    parse_mode='HTML'
                )
                return
            
            # Получаем информацию о пользователе
            target_user = self.db_service.get_user_by_telegram_id(target_telegram_id)
            
            if not target_user:
                self.bot.send_message(
                    user_id,
                    f"❌ <b>Пользователь не найден</b>\n\n"
                    f"Пользователь с ID {target_telegram_id} не найден в базе данных.",
                    parse_mode='HTML'
                )
                return
            
            # Получаем подписки пользователя
            subscriptions = self.db_service.get_user_subscriptions(target_user.id)
            active_subscription = self.db_service.get_user_active_subscription(target_user.id)
            
            # Получаем платежи пользователя
            payments = self.db_service.get_user_payments(target_user.id)
            
            # Формируем информацию о пользователе
            user_info = f"""
👤 <b>Информация о пользователе</b>

<b>Основные данные:</b>
• ID: <code>{target_user.telegram_id}</code>
• Имя: {target_user.first_name or 'Не указано'}
• Фамилия: {target_user.last_name or 'Не указано'}
• Username: @{target_user.username or 'Не указано'}
• Регистрация: {target_user.created_at.strftime('%d.%m.%Y %H:%M') if target_user.created_at else 'Неизвестно'}

<b>Активная подписка:</b>
"""
            
            if active_subscription and active_subscription.is_active():
                days_left = active_subscription.days_until_expiry()
                user_info += f"""• ✅ Активна до {active_subscription.end_date.strftime('%d.%m.%Y %H:%M')}
• Осталось дней: {days_left}
• Тип: {self._get_plan_display_name(active_subscription.plan_type)}
"""
            else:
                user_info += "• ❌ Нет активной подписки\n"
            
            user_info += f"""
<b>Статистика:</b>
• Всего подписок: {len(subscriptions)}
• Всего платежей: {len(payments)}
• Успешных платежей: {len([p for p in payments if p.status == 'completed'])}
"""
            
            # Проверяем, есть ли пользователь в канале
            try:
                is_in_channel = self.channel_manager.check_user_in_channel(target_telegram_id)
                user_info += f"• В канале: {'✅ Да' if is_in_channel else '❌ Нет'}\n"
            except Exception as e:
                user_info += f"• В канале: ❓ Ошибка проверки\n"
                logger.error(f"Ошибка проверки пользователя в канале: {e}")
            
            # Создаем клавиатуру с действиями
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "📋 История подписок",
                    callback_data=f"admin_subs_{target_user.id}"
                ),
                types.InlineKeyboardButton(
                    "💳 История платежей", 
                    callback_data=f"admin_payments_{target_user.id}"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "➕ Выдать подписку",
                    callback_data=f"admin_grant_{target_user.id}"
                ),
                types.InlineKeyboardButton(
                    "➖ Отозвать подписку",
                    callback_data=f"admin_revoke_{target_user.id}"
                )
            )
            
            self.bot.send_message(
                user_id,
                user_info,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            # Логируем действие
            self.db_service.create_admin_log(
                admin_telegram_id=user_id,
                action="view_user_info",
                target_user_id=target_user.id,
                details=f"Viewed info for user {target_telegram_id}"
            )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_admin_info: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при получении информации о пользователе")
    
    def handle_admin_grant(self, message):
        """
        Обработчик команды /admin_grant <user_id> <months>
        Выдает подписку пользователю
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            args = message.text.split()[1:] if len(message.text.split()) > 1 else []
            
            if len(args) < 2:
                self.bot.send_message(
                    user_id,
                    "❌ <b>Неверный формат команды</b>\n\n"
                    "Использование: <code>/admin_grant &lt;telegram_id&gt; &lt;months&gt;</code>\n"
                    "Пример: <code>/admin_grant 123456789 1</code>",
                    parse_mode='HTML'
                )
                return
            
            try:
                target_telegram_id = int(args[0])
                months = int(args[1])
            except ValueError:
                self.bot.send_message(
                    user_id,
                    "❌ <b>Неверные параметры</b>\n\n"
                    "ID пользователя и количество месяцев должны быть числами.",
                    parse_mode='HTML'
                )
                return
            
            if months < 1 or months > 12:
                self.bot.send_message(
                    user_id,
                    "❌ <b>Неверное количество месяцев</b>\n\n"
                    "Количество месяцев должно быть от 1 до 12.",
                    parse_mode='HTML'
                )
                return
            
            # Получаем пользователя
            target_user = self.db_service.get_user_by_telegram_id(target_telegram_id)
            
            if not target_user:
                self.bot.send_message(
                    user_id,
                    f"❌ <b>Пользователь не найден</b>\n\n"
                    f"Пользователь с ID {target_telegram_id} не найден в базе данных.",
                    parse_mode='HTML'
                )
                return
            
            # Определяем тип плана
            plan_types = {1: 'monthly', 3: 'quarterly', 6: 'half_yearly', 12: 'yearly'}
            plan_type = plan_types.get(months, 'monthly')
            
            # Проверяем, есть ли активная подписка
            active_subscription = self.db_service.get_user_active_subscription(target_user.id)
            
            if active_subscription and active_subscription.is_active():
                # Продлеваем существующую подписку
                success = self.db_service.extend_subscription(active_subscription.id, months)
                action_text = "продлена"
            else:
                # Создаем новую подписку
                subscription = self.db_service.create_subscription(target_user.id, plan_type, months)
                success = subscription is not None
                action_text = "создана"
            
            if success:
                # Добавляем пользователя в канал
                try:
                    invite_link = self.channel_manager.create_invite_link()
                    if invite_link:
                        # Отправляем пригласительную ссылку пользователю
                        self.bot.send_message(
                            target_telegram_id,
                            f"🎉 <b>Вам выдана подписка на {months} мес.!</b>\n\n"
                            f"Пригласительная ссылка: {invite_link}\n\n"
                            f"Подписка выдана администратором.",
                            parse_mode='HTML'
                        )
                        
                        self.bot.send_message(
                            user_id,
                            f"✅ <b>Подписка успешно {action_text}</b>\n\n"
                            f"Пользователь: {target_telegram_id}\n"
                            f"Период: {months} мес.\n"
                            f"Пригласительная ссылка отправлена пользователю.",
                            parse_mode='HTML'
                        )
                    else:
                        self.bot.send_message(
                            user_id,
                            f"⚠️ <b>Подписка {action_text}, но ошибка с каналом</b>\n\n"
                            f"Не удалось создать пригласительную ссылку. "
                            f"Проверьте настройки канала.",
                            parse_mode='HTML'
                        )
                except Exception as e:
                    logger.error(f"Ошибка отправки пригласительной ссылки: {e}")
                    self.bot.send_message(
                        user_id,
                        f"⚠️ <b>Подписка {action_text}, но ошибка уведомления</b>\n\n"
                        f"Не удалось отправить уведомление пользователю.",
                        parse_mode='HTML'
                    )
                
                # Логируем действие
                self.db_service.create_admin_log(
                    admin_telegram_id=user_id,
                    action="grant_subscription",
                    target_user_id=target_user.id,
                    details=f"Granted {months} months subscription to user {target_telegram_id}"
                )
            else:
                self.bot.send_message(
                    user_id,
                    f"❌ <b>Ошибка создания подписки</b>\n\n"
                    f"Не удалось создать подписку для пользователя {target_telegram_id}.",
                    parse_mode='HTML'
                )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_admin_grant: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при выдаче подписки")
    
    def handle_admin_revoke(self, message):
        """
        Обработчик команды /admin_revoke <user_id>
        Отзывает подписку у пользователя
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            args = message.text.split()[1:] if len(message.text.split()) > 1 else []
            
            if not args:
                self.bot.send_message(
                    user_id,
                    "❌ <b>Неверный формат команды</b>\n\n"
                    "Использование: <code>/admin_revoke &lt;telegram_id&gt;</code>\n"
                    "Пример: <code>/admin_revoke 123456789</code>",
                    parse_mode='HTML'
                )
                return
            
            try:
                target_telegram_id = int(args[0])
            except ValueError:
                self.bot.send_message(
                    user_id,
                    "❌ <b>Неверный ID пользователя</b>\n\n"
                    "ID должен быть числом.",
                    parse_mode='HTML'
                )
                return
            
            # Получаем пользователя
            target_user = self.db_service.get_user_by_telegram_id(target_telegram_id)
            
            if not target_user:
                self.bot.send_message(
                    user_id,
                    f"❌ <b>Пользователь не найден</b>\n\n"
                    f"Пользователь с ID {target_telegram_id} не найден в базе данных.",
                    parse_mode='HTML'
                )
                return
            
            # Получаем активную подписку
            active_subscription = self.db_service.get_user_active_subscription(target_user.id)
            
            if not active_subscription or not active_subscription.is_active():
                self.bot.send_message(
                    user_id,
                    f"❌ <b>Нет активной подписки</b>\n\n"
                    f"У пользователя {target_telegram_id} нет активной подписки.",
                    parse_mode='HTML'
                )
                return
            
            # Отзываем подписку
            success = self.db_service.update_subscription_status(active_subscription.id, 'cancelled')
            
            if success:
                # Удаляем пользователя из канала
                try:
                    removed = self.channel_manager.remove_user_from_channel(target_telegram_id)
                    
                    # Уведомляем пользователя
                    self.bot.send_message(
                        target_telegram_id,
                        "❌ <b>Ваша подписка отозвана</b>\n\n"
                        "Ваша подписка была отозвана администратором. "
                        "Доступ к каналу прекращен.\n\n"
                        "Для получения новой подписки обратитесь в поддержку.",
                        parse_mode='HTML'
                    )
                    
                    self.bot.send_message(
                        user_id,
                        f"✅ <b>Подписка успешно отозвана</b>\n\n"
                        f"Пользователь: {target_telegram_id}\n"
                        f"Удален из канала: {'✅ Да' if removed else '❌ Ошибка'}\n"
                        f"Пользователь уведомлен об отзыве подписки.",
                        parse_mode='HTML'
                    )
                except Exception as e:
                    logger.error(f"Ошибка удаления из канала: {e}")
                    self.bot.send_message(
                        user_id,
                        f"⚠️ <b>Подписка отозвана, но ошибка с каналом</b>\n\n"
                        f"Не удалось удалить пользователя из канала. "
                        f"Проверьте настройки канала.",
                        parse_mode='HTML'
                    )
                
                # Логируем действие
                self.db_service.create_admin_log(
                    admin_telegram_id=user_id,
                    action="revoke_subscription",
                    target_user_id=target_user.id,
                    details=f"Revoked subscription for user {target_telegram_id}"
                )
            else:
                self.bot.send_message(
                    user_id,
                    f"❌ <b>Ошибка отзыва подписки</b>\n\n"
                    f"Не удалось отозвать подписку у пользователя {target_telegram_id}.",
                    parse_mode='HTML'
                )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_admin_revoke: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при отзыве подписки")
    
    def handle_admin_stats(self, message):
        """
        Обработчик команды /admin_stats
        Показывает статистику системы
        
        Args:
            message: Объект сообщения от Telegram
        """
        try:
            user_id = message.from_user.id
            logger.info(f"Команда /admin_stats от администратора {user_id}")
            
            # Получаем статистику из базы данных
            stats = self.db_service.get_system_statistics()
            
            # Получаем статистику платежей
            payment_stats = self.db_service.get_payment_statistics()
            
            # Получаем отчеты
            revenue_report = self.db_service.get_revenue_report(30)
            subscription_report = self.db_service.get_subscription_report()
            admin_report = self.db_service.get_admin_activity_report(7)
            
            # Формируем сообщение со статистикой
            stats_text = f"""
📊 <b>Статистика системы</b>

<b>👥 Пользователи:</b>
• Всего зарегистрировано: {stats.get('total_users', 0)}
• Активных подписок: {stats.get('active_subscriptions', 0)}
• Истекших подписок: {stats.get('expired_subscriptions', 0)}

<b>💰 Платежи:</b>
• Всего платежей: {payment_stats.get('total_payments', 0)}
• Успешных: {payment_stats.get('completed_payments', 0)}
• Ожидающих: {payment_stats.get('pending_payments', 0)}
• Неудачных: {payment_stats.get('failed_payments', 0)}

<b>💵 Доходы (30 дней):</b>
• Общий доход: {payment_stats.get('total_revenue', 0)} ₽
• Доход за месяц: {revenue_report.get('total_revenue', 0)} ₽
• Средний чек: {revenue_report.get('average_payment', 0):.2f} ₽

<b>📈 Подписки по типам:</b>
• Месячные: {stats.get('monthly_subs', 0)}
• Квартальные: {stats.get('quarterly_subs', 0)}
• Полугодовые: {stats.get('half_yearly_subs', 0)}
• Годовые: {stats.get('yearly_subs', 0)}

<b>⚠️ Требуют внимания:</b>
• Истекают сегодня: {stats.get('expiring_today', 0)}
• Истекают завтра: {stats.get('expiring_tomorrow', 0)}
• Истекают в течение недели: {stats.get('expiring_week', 0)}

<b>🔧 Администрирование:</b>
• Действий за 24ч: {stats.get('admin_actions_24h', 0)}
• Всего действий: {stats.get('total_admin_actions', 0)}
• Последняя проверка: {stats.get('last_check', 'Неизвестно')}
"""
            
            # Создаем клавиатуру с дополнительными действиями
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "💰 Отчет по доходам",
                    callback_data="admin_revenue_report"
                ),
                types.InlineKeyboardButton(
                    "📊 Отчет по подпискам",
                    callback_data="admin_subscription_report"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "👨‍💼 Активность админов",
                    callback_data="admin_activity_report"
                ),
                types.InlineKeyboardButton(
                    "🔄 Обновить",
                    callback_data="admin_refresh_stats"
                )
            )
            
            self.bot.send_message(
                user_id,
                stats_text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            # Логируем действие
            self.db_service.create_admin_log(
                admin_telegram_id=user_id,
                action="view_system_stats",
                details="Viewed system statistics"
            )
            
        except Exception as e:
            logger.error(f"Ошибка в handle_admin_stats: {str(e)}")
            self._send_error_message(message.from_user.id, "Произошла ошибка при получении статистики")
    
    def handle_admin_revenue_report(self, call):
        """
        Обработчик callback для отчета по доходам
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback admin_revenue_report от администратора {user_id}")
            
            # Получаем отчет по доходам
            revenue_report = self.db_service.get_revenue_report(30)
            
            # Формируем текст отчета
            report_text = f"""
💰 <b>Отчет по доходам (30 дней)</b>

<b>💵 Общие показатели:</b>
• Общий доход: {revenue_report.get('total_revenue', 0):.2f} ₽
• Средний чек: {revenue_report.get('average_payment', 0):.2f} ₽

<b>💳 Доходы по способам оплаты:</b>
"""
            
            # Добавляем доходы по способам оплаты
            revenue_by_method = revenue_report.get('revenue_by_method', {})
            method_names = {
                'ru_card': 'Карта РФ',
                'foreign_card': 'Карта иностранного банка', 
                'crypto': 'Криптовалюта'
            }
            
            for method, revenue in revenue_by_method.items():
                method_name = method_names.get(method, method)
                report_text += f"• {method_name}: {revenue:.2f} ₽\n"
            
            if not revenue_by_method:
                report_text += "• Нет данных за период\n"
            
            report_text += "\n<b>📊 Доходы по дням (последние 7 дней):</b>\n"
            
            # Добавляем ежедневные доходы
            daily_revenue = revenue_report.get('daily_revenue', [])
            for day_data in daily_revenue:
                report_text += f"• {day_data['date']}: {day_data['revenue']:.2f} ₽\n"
            
            if not daily_revenue:
                report_text += "• Нет данных за период\n"
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔙 Назад к статистике",
                    callback_data="admin_refresh_stats"
                )
            )
            
            self.bot.edit_message_text(
                report_text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_admin_revenue_report: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при получении отчета по доходам")
    
    def handle_admin_subscription_report(self, call):
        """
        Обработчик callback для отчета по подпискам
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback admin_subscription_report от администратора {user_id}")
            
            # Получаем отчет по подпискам
            subscription_report = self.db_service.get_subscription_report()
            
            # Формируем текст отчета
            report_text = f"""
📊 <b>Отчет по подпискам</b>

<b>📈 Активные подписки по типам:</b>
"""
            
            # Добавляем активные подписки по типам
            active_by_type = subscription_report.get('active_by_type', {})
            type_names = {
                'monthly': 'Месячные',
                'quarterly': 'Квартальные',
                'semi_annual': 'Полугодовые',
                'yearly': 'Годовые'
            }
            
            total_active = 0
            for sub_type, count in active_by_type.items():
                type_name = type_names.get(sub_type, sub_type)
                report_text += f"• {type_name}: {count}\n"
                total_active += count
            
            if not active_by_type:
                report_text += "• Нет активных подписок\n"
            
            report_text += f"\n<b>Всего активных:</b> {total_active}\n"
            
            # Добавляем информацию об истекающих подписках
            expiring = subscription_report.get('expiring_subscriptions', {})
            report_text += f"""
<b>⚠️ Истекающие подписки:</b>
• Завтра: {expiring.get('1_days', 0)}
• Через 3 дня: {expiring.get('3_days', 0)}
• Через неделю: {expiring.get('7_days', 0)}
• Через месяц: {expiring.get('30_days', 0)}

<b>📊 Статистика за 30 дней:</b>
• Новых подписок: {subscription_report.get('new_subscriptions_30d', 0)}
• Отмененных подписок: {subscription_report.get('cancelled_subscriptions_30d', 0)}
"""
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔙 Назад к статистике",
                    callback_data="admin_refresh_stats"
                )
            )
            
            self.bot.edit_message_text(
                report_text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_admin_subscription_report: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при получении отчета по подпискам")
    
    def handle_admin_activity_report(self, call):
        """
        Обработчик callback для отчета по активности администраторов
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback admin_activity_report от администратора {user_id}")
            
            # Получаем отчет по активности администраторов
            admin_report = self.db_service.get_admin_activity_report(7)
            
            # Формируем текст отчета
            report_text = f"""
👨‍💼 <b>Отчет по активности администраторов (7 дней)</b>

<b>📊 Действия по администраторам:</b>
"""
            
            # Добавляем действия по администраторам
            actions_by_admin = admin_report.get('actions_by_admin', {})
            if actions_by_admin:
                for admin_id, count in actions_by_admin.items():
                    report_text += f"• Admin {admin_id}: {count} действий\n"
            else:
                report_text += "• Нет данных за период\n"
            
            report_text += "\n<b>🔧 Действия по типам:</b>\n"
            
            # Добавляем действия по типам
            actions_by_type = admin_report.get('actions_by_type', {})
            action_names = {
                'view_user_info': 'Просмотр информации о пользователе',
                'grant_subscription': 'Выдача подписки',
                'revoke_subscription': 'Отзыв подписки',
                'view_system_stats': 'Просмотр статистики',
                'admin_command_/admin_info': 'Команда /admin_info',
                'admin_command_/admin_grant': 'Команда /admin_grant',
                'admin_command_/admin_revoke': 'Команда /admin_revoke',
                'admin_command_/admin_stats': 'Команда /admin_stats'
            }
            
            if actions_by_type:
                for action_type, count in actions_by_type.items():
                    action_name = action_names.get(action_type, action_type)
                    report_text += f"• {action_name}: {count}\n"
            else:
                report_text += "• Нет данных за период\n"
            
            report_text += "\n<b>📈 Активность по дням:</b>\n"
            
            # Добавляем ежедневную активность
            daily_activity = admin_report.get('daily_activity', [])
            for day_data in daily_activity:
                report_text += f"• {day_data['date']}: {day_data['actions']} действий\n"
            
            if not daily_activity:
                report_text += "• Нет данных за период\n"
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔙 Назад к статистике",
                    callback_data="admin_refresh_stats"
                )
            )
            
            self.bot.edit_message_text(
                report_text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id)
            
        except Exception as e:
            logger.error(f"Ошибка в handle_admin_activity_report: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при получении отчета по активности")
    
    def handle_admin_refresh_stats(self, call):
        """
        Обработчик callback для обновления статистики
        
        Args:
            call: Callback query от inline клавиатуры
        """
        try:
            user_id = call.from_user.id
            logger.info(f"Callback admin_refresh_stats от администратора {user_id}")
            
            # Получаем обновленную статистику
            stats = self.db_service.get_system_statistics()
            payment_stats = self.db_service.get_payment_statistics()
            revenue_report = self.db_service.get_revenue_report(30)
            subscription_report = self.db_service.get_subscription_report()
            admin_report = self.db_service.get_admin_activity_report(7)
            
            # Формируем обновленное сообщение со статистикой
            stats_text = f"""
📊 <b>Статистика системы</b> 🔄

<b>👥 Пользователи:</b>
• Всего зарегистрировано: {stats.get('total_users', 0)}
• Активных подписок: {stats.get('active_subscriptions', 0)}
• Истекших подписок: {stats.get('expired_subscriptions', 0)}

<b>💰 Платежи:</b>
• Всего платежей: {payment_stats.get('total_payments', 0)}
• Успешных: {payment_stats.get('completed_payments', 0)}
• Ожидающих: {payment_stats.get('pending_payments', 0)}
• Неудачных: {payment_stats.get('failed_payments', 0)}

<b>💵 Доходы (30 дней):</b>
• Общий доход: {payment_stats.get('total_revenue', 0)} ₽
• Доход за месяц: {revenue_report.get('total_revenue', 0)} ₽
• Средний чек: {revenue_report.get('average_payment', 0):.2f} ₽

<b>📈 Подписки по типам:</b>
• Месячные: {stats.get('monthly_subs', 0)}
• Квартальные: {stats.get('quarterly_subs', 0)}
• Полугодовые: {stats.get('half_yearly_subs', 0)}
• Годовые: {stats.get('yearly_subs', 0)}

<b>⚠️ Требуют внимания:</b>
• Истекают сегодня: {stats.get('expiring_today', 0)}
• Истекают завтра: {stats.get('expiring_tomorrow', 0)}
• Истекают в течение недели: {stats.get('expiring_week', 0)}

<b>🔧 Администрирование:</b>
• Действий за 24ч: {stats.get('admin_actions_24h', 0)}
• Всего действий: {stats.get('total_admin_actions', 0)}
• Последняя проверка: {stats.get('last_check', 'Неизвестно')}
"""
            
            # Создаем клавиатуру с дополнительными действиями
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "💰 Отчет по доходам",
                    callback_data="admin_revenue_report"
                ),
                types.InlineKeyboardButton(
                    "📊 Отчет по подпискам",
                    callback_data="admin_subscription_report"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "👨‍💼 Активность админов",
                    callback_data="admin_activity_report"
                ),
                types.InlineKeyboardButton(
                    "🔄 Обновить",
                    callback_data="admin_refresh_stats"
                )
            )
            
            self.bot.edit_message_text(
                stats_text,
                user_id,
                call.message.message_id,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id, "✅ Статистика обновлена")
            
        except Exception as e:
            logger.error(f"Ошибка в handle_admin_refresh_stats: {str(e)}")
            self.bot.answer_callback_query(call.id, "Произошла ошибка при обновлении статистики")

    def _get_plan_display_name(self, plan_type: str) -> str:
        """
        Получает отображаемое название тарифного плана
        
        Args:
            plan_type: Тип плана
            
        Returns:
            Отображаемое название плана
        """
        plan_names = {
            'monthly': '1 месяц',
            'quarterly': '3 месяца', 
            'half_yearly': '6 месяцев',
            'yearly': '12 месяцев'
        }
        return plan_names.get(plan_type, plan_type)    
   
 # ==================== МЕТОДЫ УВЕДОМЛЕНИЙ ====================
    
    def send_payment_success_notification(self, user_id: int, order_id: str, amount: float, plan_months: int) -> bool:
        """
        Отправляет уведомление об успешной оплате
        
        Args:
            user_id: ID пользователя Telegram
            order_id: ID заказа
            amount: Сумма платежа
            plan_months: Количество месяцев подписки
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            plans = self.payment_service.get_subscription_plans()
            plan = plans.get(plan_months, {})
            
            text = f"""
✅ <b>Платеж успешно обработан!</b>

💳 <b>Детали платежа:</b>
• Номер заказа: <code>{order_id}</code>
• Сумма: {amount} ₽
• План: {plan.get('name', f'{plan_months} месяцев')}

🎉 <b>Ваша подписка активирована!</b>

Сейчас мы создадим для вас пригласительную ссылку на канал. Это может занять несколько секунд...
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "📊 Проверить статус подписки",
                    callback_data="subscription_status"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Уведомление об успешной оплате отправлено пользователю {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки уведомления об успешной оплате пользователю {user_id}: {str(e)}")
            return False
    
    def send_payment_failed_notification(self, user_id: int, order_id: str, error_message: str = None) -> bool:
        """
        Отправляет уведомление о неудачной оплате
        
        Args:
            user_id: ID пользователя Telegram
            order_id: ID заказа
            error_message: Сообщение об ошибке
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            text = f"""
❌ <b>Платеж не удался</b>

К сожалению, ваш платеж не был успешно обработан.

📋 <b>Детали:</b>
• Номер заказа: <code>{order_id}</code>
"""
            
            if error_message:
                text += f"• Причина: {error_message}\n"
            
            text += """
💡 <b>Что делать:</b>
• Попробуйте создать новый счет для оплаты
• Проверьте данные карты и баланс
• Обратитесь в поддержку, если проблема повторяется

Мы поможем решить любые вопросы с оплатой!
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔄 Попробовать снова",
                    callback_data="buy_subscription"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "🆘 Связаться с поддержкой",
                    callback_data="ask_question"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Уведомление о неудачной оплате отправлено пользователю {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки уведомления о неудачной оплате пользователю {user_id}: {str(e)}")
            return False
    
    def send_invite_link_notification(self, user_id: int, invite_url: str, subscription_end_date: datetime) -> bool:
        """
        Отправляет пригласительную ссылку на канал
        
        Args:
            user_id: ID пользователя Telegram
            invite_url: Пригласительная ссылка
            subscription_end_date: Дата окончания подписки
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            end_date_str = subscription_end_date.strftime('%d.%m.%Y в %H:%M')
            
            text = f"""
🎉 <b>Добро пожаловать в приватный канал!</b>

Ваша подписка активирована и готова к использованию.

📅 <b>Подписка действует до:</b> {end_date_str}

👇 <b>Нажмите кнопку ниже, чтобы присоединиться к каналу:</b>
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔗 Присоединиться к каналу",
                    url=invite_url
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "📊 Статус подписки",
                    callback_data="subscription_status"
                ),
                types.InlineKeyboardButton(
                    "ℹ️ О канале",
                    callback_data="channel_info"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Пригласительная ссылка отправлена пользователю {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки пригласительной ссылки пользователю {user_id}: {str(e)}")
            return False
    
    def send_subscription_expiry_warning(self, user_id: int, days_left: int) -> bool:
        """
        Отправляет предупреждение об истечении подписки
        
        Args:
            user_id: ID пользователя Telegram
            days_left: Количество дней до истечения
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            if days_left <= 0:
                emoji = "🔴"
                status = "истекла"
                message = "Ваша подписка истекла. Для продолжения доступа к каналу необходимо продлить подписку."
            elif days_left == 1:
                emoji = "⚠️"
                status = "истекает завтра"
                message = "Ваша подписка истекает завтра. Рекомендуем продлить её заранее, чтобы не потерять доступ к каналу."
            elif days_left <= 3:
                emoji = "⚠️"
                status = f"истекает через {days_left} дня"
                message = f"Ваша подписка истекает через {days_left} дня. Не забудьте продлить её, чтобы сохранить доступ к каналу."
            else:
                emoji = "📅"
                status = f"истекает через {days_left} дней"
                message = f"Напоминаем, что ваша подписка истекает через {days_left} дней."
            
            text = f"""
{emoji} <b>Уведомление о подписке</b>

{message}

📊 <b>Статус:</b> {status}

💡 <b>Продлить подписку можно прямо сейчас:</b>
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "💳 Продлить подписку",
                    callback_data="buy_subscription"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "📊 Статус подписки",
                    callback_data="subscription_status"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Предупреждение об истечении подписки отправлено пользователю {user_id} ({days_left} дней)")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки предупреждения об истечении подписки пользователю {user_id}: {str(e)}")
            return False
    
    def send_subscription_expired_notification(self, user_id: int) -> bool:
        """
        Отправляет уведомление об истечении подписки
        
        Args:
            user_id: ID пользователя Telegram
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            text = """
⏰ <b>Подписка истекла</b>

Ваша подписка на приватный канал истекла. Доступ к каналу приостановлен.

💡 <b>Чтобы восстановить доступ:</b>
• Выберите подходящий тарифный план
• Оплатите подписку
• Получите новую пригласительную ссылку

Мы будем рады видеть вас снова в нашем канале!
            """
            
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton(
                    "💳 Продлить подписку",
                    callback_data="buy_subscription"
                )
            )
            keyboard.add(
                types.InlineKeyboardButton(
                    "ℹ️ О канале",
                    callback_data="channel_info"
                ),
                types.InlineKeyboardButton(
                    "🆘 Поддержка",
                    callback_data="ask_question"
                )
            )
            
            self.bot.send_message(
                user_id,
                text,
                parse_mode='HTML',
                reply_markup=keyboard
            )
            
            logger.info(f"Уведомление об истечении подписки отправлено пользователю {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки уведомления об истечении подписки пользователю {user_id}: {str(e)}")
            return False