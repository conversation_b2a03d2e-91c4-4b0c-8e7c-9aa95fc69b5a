# Step-by-step admin panel update
param(
    [string]$ServerIP = "**************",
    [string]$Username = "ubuntu"
)

Write-Host "=== STEP-BY-STEP ADMIN PANEL UPDATE ===" -ForegroundColor Green

# Step 1: Create archive
Write-Host ""
Write-Host "STEP 1: Creating archive..." -ForegroundColor Yellow
if (Test-Path "admin_panel_fix.zip") {
    Remove-Item "admin_panel_fix.zip" -Force
}
Compress-Archive -Path "app\admin.py", "templates\*" -DestinationPath "admin_panel_fix.zip" -Force
$fileInfo = Get-Item "admin_panel_fix.zip"
Write-Host "Archive created: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Green

# Step 2: Get password
Write-Host ""
Write-Host "STEP 2: Authentication..." -ForegroundColor Yellow
$SecurePassword = Read-Host "Enter server password" -AsSecureString
$Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))

# Step 3: Upload file
Write-Host ""
Write-Host "STEP 3: Uploading file..." -ForegroundColor Yellow
Write-Host "Trying to upload admin_panel_fix.zip to server..." -ForegroundColor Gray

try {
    & pscp -pw $Password admin_panel_fix.zip $Username@${ServerIP}:/home/<USER>/
    if ($LASTEXITCODE -eq 0) {
        Write-Host "File uploaded successfully!" -ForegroundColor Green
    } else {
        throw "Upload failed"
    }
} catch {
    Write-Host "Upload failed. Please upload manually:" -ForegroundColor Red
    Write-Host "scp admin_panel_fix.zip $Username@${ServerIP}:/home/<USER>/" -ForegroundColor White
    Write-Host "Press Enter when file is uploaded..." -ForegroundColor Yellow
    Read-Host
}

# Step 4: Execute update commands
Write-Host ""
Write-Host "STEP 4: Executing update on server..." -ForegroundColor Yellow

$UpdateCommands = @"
cd /home/<USER>/telegram_bot
echo 'Stopping service...'
echo '$Password' | sudo -S systemctl stop telegram-bot
echo 'Creating backup...'
echo '$Password' | sudo -S cp -r app templates backup_`$(date +%Y%m%d_%H%M%S)
echo 'Extracting updates...'
unzip -o /home/<USER>/admin_panel_fix.zip
echo 'Setting permissions...'
echo '$Password' | sudo -S chown -R ubuntu:ubuntu app templates
echo '$Password' | sudo -S chmod -R 755 app templates
echo 'Starting service...'
echo '$Password' | sudo -S systemctl start telegram-bot
echo 'Checking status...'
echo '$Password' | sudo -S systemctl status telegram-bot --no-pager -l
echo 'Verifying updates...'
if grep -q 'safe_url_for' app/admin.py; then echo 'admin.py: OK'; else echo 'admin.py: FAILED'; fi
if grep -q 'safe_url_for' templates/base.html; then echo 'templates: OK'; else echo 'templates: FAILED'; fi
echo 'Update completed!'
"@

Write-Host "Executing commands on server..." -ForegroundColor Gray
echo $UpdateCommands | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host ""
Write-Host "=== UPDATE PROCESS COMPLETED ===" -ForegroundColor Green
Write-Host ""
Write-Host "TEST THE ADMIN PANEL:" -ForegroundColor Cyan
Write-Host "1. Open: https://$ServerIP/admin/login" -ForegroundColor White
Write-Host "2. Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "If you see any issues, run:" -ForegroundColor Yellow
Write-Host ".\restart_services.ps1" -ForegroundColor White