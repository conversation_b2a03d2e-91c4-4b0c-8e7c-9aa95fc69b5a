"""
Комплексная система обработки ошибок с повторными попытками и восстановлением
"""

import logging
import time
import functools
import traceback
from typing import Any, Callable, Dict, Optional, Type, Union, List
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
import sqlite3
import requests
from telebot.apihelper import ApiException

logger = logging.getLogger('error_handler')

class ErrorSeverity(Enum):
    """Уровни серьезности ошибок"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Категории ошибок"""
    DATABASE = "database"
    API = "api"
    NETWORK = "network"
    TELEGRAM = "telegram"
    PAYMENT = "payment"
    VALIDATION = "validation"
    SYSTEM = "system"

@dataclass
class ErrorInfo:
    """Информация об ошибке"""
    error_type: str
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime
    context: Dict[str, Any]
    traceback_info: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

class RetryConfig:
    """Конфигурация повторных попыток"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, exponential_base: float = 2.0,
                 jitter: bool = True):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter

class ErrorHandler:
    """Централизованный обработчик ошибок"""
    
    # Конфигурации повторных попыток для разных типов операций
    RETRY_CONFIGS = {
        ErrorCategory.API: RetryConfig(max_retries=3, base_delay=2.0, max_delay=30.0),
        ErrorCategory.NETWORK: RetryConfig(max_retries=5, base_delay=1.0, max_delay=60.0),
        ErrorCategory.DATABASE: RetryConfig(max_retries=2, base_delay=0.5, max_delay=5.0),
        ErrorCategory.TELEGRAM: RetryConfig(max_retries=3, base_delay=1.0, max_delay=10.0),
        ErrorCategory.PAYMENT: RetryConfig(max_retries=2, base_delay=3.0, max_delay=15.0),
    }
    
    # Исключения, которые не следует повторять
    NON_RETRYABLE_EXCEPTIONS = {
        ValueError,
        TypeError,
        KeyError,
        AttributeError,
        sqlite3.IntegrityError,  # Нарушение ограничений БД
    }
    
    # HTTP коды, которые не следует повторять
    NON_RETRYABLE_HTTP_CODES = {400, 401, 403, 404, 422}
    
    def __init__(self):
        self.error_stats = {
            'total_errors': 0,
            'errors_by_category': {cat.value: 0 for cat in ErrorCategory},
            'errors_by_severity': {sev.value: 0 for sev in ErrorSeverity},
            'retry_attempts': 0,
            'successful_retries': 0,
        }
        self.recent_errors: List[ErrorInfo] = []
        self.max_recent_errors = 100
    
    def handle_error(self, error: Exception, category: ErrorCategory, 
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    context: Optional[Dict[str, Any]] = None,
                    should_retry: bool = True) -> ErrorInfo:
        """
        Обрабатывает ошибку и создает информацию о ней
        
        Args:
            error: Исключение
            category: Категория ошибки
            severity: Серьезность ошибки
            context: Дополнительный контекст
            should_retry: Следует ли повторять операцию
            
        Returns:
            Информация об ошибке
        """
        error_info = ErrorInfo(
            error_type=type(error).__name__,
            message=str(error),
            category=category,
            severity=severity,
            timestamp=datetime.now(),
            context=context or {},
            traceback_info=traceback.format_exc(),
            max_retries=self.RETRY_CONFIGS.get(category, RetryConfig()).max_retries
        )
        
        # Обновляем статистику
        self._update_error_stats(error_info)
        
        # Добавляем в список недавних ошибок
        self._add_to_recent_errors(error_info)
        
        # Логируем ошибку
        self._log_error(error_info)
        
        # Определяем, можно ли повторить операцию
        if should_retry and self._is_retryable(error, error_info):
            error_info.max_retries = self.RETRY_CONFIGS.get(category, RetryConfig()).max_retries
        else:
            error_info.max_retries = 0
        
        return error_info
    
    def _is_retryable(self, error: Exception, error_info: ErrorInfo) -> bool:
        """
        Определяет, можно ли повторить операцию после ошибки
        
        Args:
            error: Исключение
            error_info: Информация об ошибке
            
        Returns:
            True если операцию можно повторить
        """
        # Проверяем тип исключения
        if type(error) in self.NON_RETRYABLE_EXCEPTIONS:
            return False
        
        # Проверяем HTTP ошибки
        if isinstance(error, requests.exceptions.HTTPError):
            if hasattr(error, 'response') and error.response.status_code in self.NON_RETRYABLE_HTTP_CODES:
                return False
        
        # Проверяем Telegram API ошибки
        if isinstance(error, ApiException):
            # Некоторые ошибки Telegram API не следует повторять
            if error.error_code in [400, 401, 403, 404]:
                return False
        
        # Критические ошибки обычно не повторяем
        if error_info.severity == ErrorSeverity.CRITICAL:
            return False
        
        return True
    
    def _calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """
        Вычисляет задержку перед повторной попыткой
        
        Args:
            attempt: Номер попытки (начиная с 1)
            config: Конфигурация повторных попыток
            
        Returns:
            Задержка в секундах
        """
        delay = config.base_delay * (config.exponential_base ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        # Добавляем случайный джиттер для избежания thundering herd
        if config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay
    
    def _update_error_stats(self, error_info: ErrorInfo):
        """Обновляет статистику ошибок"""
        self.error_stats['total_errors'] += 1
        self.error_stats['errors_by_category'][error_info.category.value] += 1
        self.error_stats['errors_by_severity'][error_info.severity.value] += 1
    
    def _add_to_recent_errors(self, error_info: ErrorInfo):
        """Добавляет ошибку в список недавних"""
        self.recent_errors.append(error_info)
        
        # Ограничиваем размер списка
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors = self.recent_errors[-self.max_recent_errors:]
    
    def _log_error(self, error_info: ErrorInfo):
        """Логирует ошибку с соответствующим уровнем"""
        log_message = (
            f"[{error_info.category.value.upper()}] {error_info.error_type}: {error_info.message}"
        )
        
        if error_info.context:
            log_message += f" | Context: {error_info.context}"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
            if error_info.traceback_info:
                logger.critical(f"Traceback:\n{error_info.traceback_info}")
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
            if error_info.traceback_info:
                logger.error(f"Traceback:\n{error_info.traceback_info}")
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Возвращает статистику ошибок"""
        return self.error_stats.copy()
    
    def get_recent_errors(self, limit: Optional[int] = None) -> List[ErrorInfo]:
        """Возвращает список недавних ошибок"""
        if limit:
            return self.recent_errors[-limit:]
        return self.recent_errors.copy()
    
    def clear_error_stats(self):
        """Очищает статистику ошибок"""
        self.error_stats = {
            'total_errors': 0,
            'errors_by_category': {cat.value: 0 for cat in ErrorCategory},
            'errors_by_severity': {sev.value: 0 for sev in ErrorSeverity},
            'retry_attempts': 0,
            'successful_retries': 0,
        }
        self.recent_errors.clear()

# Глобальный экземпляр обработчика ошибок
error_handler = ErrorHandler()

def with_error_handling(category: ErrorCategory, severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                       context_func: Optional[Callable] = None,
                       fallback_result: Any = None,
                       should_retry: bool = True):
    """
    Декоратор для автоматической обработки ошибок
    
    Args:
        category: Категория ошибки
        severity: Серьезность ошибки
        context_func: Функция для получения контекста
        fallback_result: Результат по умолчанию при ошибке
        should_retry: Следует ли повторять операцию
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {}
                if context_func:
                    try:
                        context = context_func(*args, **kwargs)
                    except:
                        pass
                
                error_info = error_handler.handle_error(
                    error=e,
                    category=category,
                    severity=severity,
                    context=context,
                    should_retry=should_retry
                )
                
                # Если это критическая ошибка, поднимаем исключение
                if severity == ErrorSeverity.CRITICAL:
                    raise
                
                return fallback_result
        
        return wrapper
    return decorator

def with_retry(category: ErrorCategory, max_retries: Optional[int] = None,
               context_func: Optional[Callable] = None):
    """
    Декоратор для автоматических повторных попыток
    
    Args:
        category: Категория операции
        max_retries: Максимальное количество повторов (None = использовать конфигурацию по умолчанию)
        context_func: Функция для получения контекста
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            config = error_handler.RETRY_CONFIGS.get(category, RetryConfig())
            retries = max_retries if max_retries is not None else config.max_retries
            
            last_error = None
            
            for attempt in range(retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_error = e
                    
                    # Получаем контекст
                    context = {'attempt': attempt + 1, 'max_retries': retries}
                    if context_func:
                        try:
                            context.update(context_func(*args, **kwargs))
                        except:
                            pass
                    
                    # Обрабатываем ошибку
                    error_info = error_handler.handle_error(
                        error=e,
                        category=category,
                        context=context,
                        should_retry=attempt < retries
                    )
                    
                    error_handler.error_stats['retry_attempts'] += 1
                    
                    # Если это последняя попытка или ошибка не повторяемая
                    if attempt >= retries or not error_handler._is_retryable(e, error_info):
                        break
                    
                    # Вычисляем задержку и ждем
                    delay = error_handler._calculate_delay(attempt + 1, config)
                    logger.info(f"Повторная попытка {attempt + 1}/{retries} через {delay:.2f}с для {func.__name__}")
                    time.sleep(delay)
            
            # Если дошли до сюда, все попытки исчерпаны
            if last_error:
                raise last_error
        
        return wrapper
    return decorator

class CircuitBreaker:
    """
    Реализация паттерна Circuit Breaker для предотвращения каскадных сбоев
    """
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60,
                 expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def __call__(self, func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if self.state == 'OPEN':
                if self._should_attempt_reset():
                    self.state = 'HALF_OPEN'
                else:
                    raise Exception(f"Circuit breaker is OPEN for {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except self.expected_exception as e:
                self._on_failure()
                raise
        
        return wrapper
    
    def _should_attempt_reset(self) -> bool:
        """Проверяет, следует ли попытаться сбросить circuit breaker"""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self):
        """Вызывается при успешном выполнении"""
        self.failure_count = 0
        self.state = 'CLOSED'
    
    def _on_failure(self):
        """Вызывается при неудачном выполнении"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'

class HealthChecker:
    """
    Система проверки здоровья компонентов системы
    """
    
    def __init__(self):
        self.health_checks = {}
        self.last_check_results = {}
    
    def register_check(self, name: str, check_func: Callable[[], bool], 
                      interval: int = 60):
        """
        Регистрирует проверку здоровья
        
        Args:
            name: Имя проверки
            check_func: Функция проверки (должна возвращать bool)
            interval: Интервал проверки в секундах
        """
        self.health_checks[name] = {
            'func': check_func,
            'interval': interval,
            'last_check': 0,
            'last_result': None
        }
    
    def check_health(self, name: Optional[str] = None) -> Dict[str, Any]:
        """
        Выполняет проверки здоровья
        
        Args:
            name: Имя конкретной проверки (None = все проверки)
            
        Returns:
            Результаты проверок
        """
        current_time = time.time()
        results = {}
        
        checks_to_run = [name] if name else self.health_checks.keys()
        
        for check_name in checks_to_run:
            if check_name not in self.health_checks:
                continue
            
            check_info = self.health_checks[check_name]
            
            # Проверяем, нужно ли выполнять проверку
            if (current_time - check_info['last_check']) >= check_info['interval']:
                try:
                    result = check_info['func']()
                    check_info['last_result'] = result
                    check_info['last_check'] = current_time
                except Exception as e:
                    logger.error(f"Ошибка проверки здоровья {check_name}: {e}")
                    check_info['last_result'] = False
                    check_info['last_check'] = current_time
            
            results[check_name] = {
                'healthy': check_info['last_result'],
                'last_check': check_info['last_check'],
                'interval': check_info['interval']
            }
        
        return results
    
    def is_healthy(self, name: Optional[str] = None) -> bool:
        """
        Проверяет, здоров ли компонент или система в целом
        
        Args:
            name: Имя компонента (None = вся система)
            
        Returns:
            True если здоров
        """
        results = self.check_health(name)
        
        if name:
            return results.get(name, {}).get('healthy', False)
        
        return all(result.get('healthy', False) for result in results.values())

# Глобальный экземпляр проверки здоровья
health_checker = HealthChecker()

class RecoveryManager:
    """
    Менеджер восстановления после сбоев
    """
    
    def __init__(self):
        self.recovery_strategies = {}
    
    def register_recovery_strategy(self, error_type: Type[Exception], 
                                 strategy_func: Callable[[Exception], bool]):
        """
        Регистрирует стратегию восстановления для типа ошибки
        
        Args:
            error_type: Тип исключения
            strategy_func: Функция восстановления (должна возвращать bool)
        """
        self.recovery_strategies[error_type] = strategy_func
    
    def attempt_recovery(self, error: Exception) -> bool:
        """
        Пытается восстановиться после ошибки
        
        Args:
            error: Исключение
            
        Returns:
            True если восстановление успешно
        """
        error_type = type(error)
        
        # Ищем точное соответствие типа
        if error_type in self.recovery_strategies:
            try:
                return self.recovery_strategies[error_type](error)
            except Exception as e:
                logger.error(f"Ошибка при восстановлении от {error_type}: {e}")
                return False
        
        # Ищем среди родительских классов
        for registered_type, strategy in self.recovery_strategies.items():
            if isinstance(error, registered_type):
                try:
                    return strategy(error)
                except Exception as e:
                    logger.error(f"Ошибка при восстановлении от {registered_type}: {e}")
                    return False
        
        return False

# Глобальный экземпляр менеджера восстановления
recovery_manager = RecoveryManager()