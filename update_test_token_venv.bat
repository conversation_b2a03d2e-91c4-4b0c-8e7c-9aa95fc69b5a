@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Copying test_token.py to server...
pscp -pw %PASSWORD% test_token.py ubuntu@195.49.212.172:/home/<USER>/test_token.py
echo Executing remote commands...
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S mv /home/<USER>/test_token.py /home/<USER>/app/test_token.py && echo '%PASSWORD%' | sudo -S chown telegrambot:telegrambot /home/<USER>/app/test_token.py && echo '%PASSWORD%' | sudo -S chmod 644 /home/<USER>/app/test_token.py && echo '%PASSWORD%' | sudo -S -u telegrambot bash -c 'cd /home/<USER>/app && /home/<USER>/venv/bin/python3 test_token.py'"
echo Done.