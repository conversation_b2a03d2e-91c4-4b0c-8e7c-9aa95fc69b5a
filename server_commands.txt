Вот команды, которые нужно выполнить на сервере:

1. Подключитесь к серверу:
   ssh ubuntu@195.49.212.172
   (пароль: dkomqgTaijxro7in^bxd)

2. Проверьте домашнюю директорию:
   ls -la ~/

3. Найдите файлы .env:
   sudo find /home -name '.env' -type f 2>/dev/null

4. Проверьте запущенные процессы:
   ps aux | grep -E 'python|node|telegram|bot' | grep -v grep

5. Проверьте системные сервисы:
   sudo systemctl list-units --type=service | grep -E 'bot|telegram|node|python'

6. Проверьте веб-директории:
   ls -la /var/www/
   ls -la /srv/

7. Проверьте конфигурацию nginx (если используется):
   ls -la /etc/nginx/sites-enabled/
   cat /etc/nginx/sites-enabled/default 2>/dev/null

После выполнения этих команд сообщите мне вывод, и я помогу определить, куда нужно загрузить .env файл.
