#!/bin/bash

# Переходим в директорию приложения
cd /home/<USER>/app

# Останавливаем сервис
sudo systemctl stop telegram-payment-bot.service

# Активируем виртуальное окружение и инициализируем базу данных
sudo -u telegrambot bash << 'EOF'
source venv/bin/activate
python3 -c "
import sys
sys.path.append('/home/<USER>/app')
from app.models.database import DatabaseService
print('Initializing database...')
try:
    db = DatabaseService()
    db.init_database()
    print('Database initialized successfully')
except Exception as e:
    print(f'Error: {e}')
"
EOF

# Перезапускаем сервис
sudo systemctl start telegram-payment-bot.service
sudo systemctl enable telegram-payment-bot.service

# Проверяем статус
echo "=== Service Status ==="
sudo systemctl status telegram-payment-bot.service --no-pager

echo "=== Testing web interface ==="
sleep 3
curl -s http://localhost:5000/admin/login | head -10

echo "=== Service logs ==="
sudo journalctl -u telegram-payment-bot.service -n 10 --no-pager