# Secure Upload Script for .env file

# Prompt for password securely
$password = Read-Host "Enter server password" -AsSecureString
$plainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))

$server = "**************"
$user = "ubuntu"
$remoteTempPath = "/home/<USER>/temp_env"
$remoteTargetPath = "/home/<USER>/app/.env"

# Accept host key using plink
echo y | plink -ssh $user@$server -pw $plainPassword "exit 0"

# Upload file to temporary location using pscp
pscp -pw $plainPassword server_env_file $user@$server`:$remoteTempPath

# Move file to target location with sudo via plink
echo "echo '$plainPassword' | sudo -S mv $remoteTempPath $remoteTargetPath" | plink -ssh $user@$server -pw $plainPassword

# Restart services with sudo via plink
echo "echo '$plainPassword' | sudo -S systemctl restart telegram-payment-bot" | plink -ssh $user@$server -pw $plainPassword
echo "echo '$plainPassword' | sudo -S systemctl restart nginx" | plink -ssh $user@$server -pw $plainPassword

Write-Output "Upload and restart completed."