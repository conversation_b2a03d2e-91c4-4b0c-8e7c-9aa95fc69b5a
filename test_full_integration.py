#!/usr/bin/env python3
"""
Полный тест интеграции Telegram бота с Lava.top API
"""

import sys
import os
import json
import time
import requests
from datetime import datetime
from decimal import Decimal

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.payment_service import PaymentService
from app.services.webhook_handler import WebhookHandler
from app.services.notification_service import NotificationService
from app.models.database import DatabaseService
from config import Config

def test_full_integration():
    """Тестирует полную интеграцию от создания счета до webhook"""
    
    print("=== Полный тест интеграции ===")
    
    # Инициализируем сервисы
    try:
        db_service = DatabaseService()
        payment_service = PaymentService()
        notification_service = NotificationService(None, db_service)  # bot_handler будет None для теста
        webhook_handler = WebhookHandler(payment_service, notification_service, db_service)
        
        print("✅ Сервисы инициализированы")
    except Exception as e:
        print(f"❌ Ошибка инициализации сервисов: {e}")
        return False
    
    # Тестовые данные
    test_user_id = 123456789
    test_plan_months = 1
    test_payment_method = 'card_ru'
    
    print(f"\n📋 Тестовые данные:")
    print(f"   User ID: {test_user_id}")
    print(f"   План: {test_plan_months} месяц")
    print(f"   Способ оплаты: {test_payment_method}")
    
    # Шаг 1: Создаем пользователя в БД
    print(f"\n🔄 Шаг 1: Создание пользователя в БД...")
    try:
        # Проверяем, существует ли пользователь
        existing_user = db_service.get_user_by_telegram_id(test_user_id)
        if existing_user:
            print(f"   ✅ Пользователь уже существует: ID {existing_user.id}")
            user = existing_user
        else:
            # Создаем тестового пользователя с правильными параметрами
            user = db_service.create_user(
                telegram_id=test_user_id,
                username='test_user',
                first_name='Test',
                last_name='User'
            )
            print(f"   ✅ Пользователь создан: ID {user.id}")
            
    except Exception as e:
        print(f"   ❌ Ошибка создания пользователя: {e}")
        return False
    
    # Шаг 2: Создаем счет для оплаты
    print(f"\n🔄 Шаг 2: Создание счета для оплаты...")
    try:
        invoice_result = payment_service.create_invoice(test_user_id, test_plan_months, test_payment_method)
        
        if invoice_result['success']:
            print(f"   ✅ Счет создан успешно:")
            print(f"      Order ID: {invoice_result['order_id']}")
            print(f"      Invoice ID: {invoice_result['invoice_id']}")
            print(f"      Сумма: {invoice_result['amount']} {invoice_result['currency']}")
            print(f"      URL: {invoice_result['payment_url'][:50]}...")
            
            order_id = invoice_result['order_id']
            invoice_id = invoice_result['invoice_id']
            amount = invoice_result['amount']
            
        else:
            print(f"   ❌ Ошибка создания счета: {invoice_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"   ❌ Исключение при создании счета: {e}")
        return False
    
    # Шаг 3: Сохраняем платеж в БД
    print(f"\n🔄 Шаг 3: Сохранение платежа в БД...")
    try:
        payment = db_service.create_payment(
            user_id=user.id,
            lava_invoice_id=order_id,
            amount=Decimal(str(amount)),
            payment_method=test_payment_method,
            payment_url=invoice_result['payment_url'],
            expires_at=invoice_result['expires_at']
        )
        print(f"   ✅ Платеж сохранен в БД: ID {payment.id}")
        
    except Exception as e:
        print(f"   ❌ Ошибка сохранения платежа: {e}")
        return False
    
    # Шаг 4: Симулируем webhook от Lava.top
    print(f"\n🔄 Шаг 4: Симуляция webhook от Lava.top...")
    try:
        # Создаем тестовые данные webhook для успешного платежа
        webhook_data = {
            'id': invoice_id,
            'orderId': order_id,
            'status': 'completed',
            'amountTotal': {
                'currency': 'RUB',
                'amount': float(amount)
            },
            'custom': {
                'user_id': test_user_id,
                'plan_months': test_plan_months,
                'payment_method': test_payment_method
            },
            'timestamp': datetime.now().isoformat()
        }
        
        print(f"   📤 Отправляем webhook данные:")
        print(f"      {json.dumps(webhook_data, indent=6, ensure_ascii=False)}")
        
        # Обрабатываем webhook (симулируем Flask request)
        class MockRequest:
            def __init__(self, data):
                self._data = json.dumps(data)
            
            def get_data(self, as_text=True):
                return self._data
            
            @property
            def headers(self):
                return {}
            
            @property
            def remote_addr(self):
                return '127.0.0.1'
        
        # Временно заменяем request объект
        import app.services.webhook_handler
        original_request = getattr(app.services.webhook_handler, 'request', None)
        app.services.webhook_handler.request = MockRequest(webhook_data)
        
        try:
            webhook_result = webhook_handler.handle_lava_webhook()
            print(f"   📥 Результат обработки webhook:")
            print(f"      {json.dumps(webhook_result, indent=6, ensure_ascii=False)}")
            
            if webhook_result.get('success'):
                print(f"   ✅ Webhook обработан успешно")
            else:
                print(f"   ❌ Ошибка обработки webhook: {webhook_result.get('error')}")
                return False
                
        finally:
            # Восстанавливаем оригинальный request
            if original_request:
                app.services.webhook_handler.request = original_request
        
    except Exception as e:
        print(f"   ❌ Ошибка симуляции webhook: {e}")
        return False
    
    # Шаг 5: Проверяем результаты в БД
    print(f"\n🔄 Шаг 5: Проверка результатов в БД...")
    try:
        # Проверяем статус платежа
        updated_payment = db_service.get_payment_by_order_id(order_id)
        if updated_payment:
            print(f"   💳 Статус платежа: {updated_payment.status}")
            if updated_payment.status == 'completed':
                print(f"   ✅ Платеж успешно обновлен")
            else:
                print(f"   ⚠️  Платеж не обновлен: {updated_payment.status}")
        else:
            print(f"   ❌ Платеж не найден в БД")
            return False
        
        # Проверяем подписку
        subscription = db_service.get_user_active_subscription(user.id)
        if subscription:
            print(f"   📅 Подписка создана:")
            print(f"      Начало: {subscription.start_date}")
            print(f"      Конец: {subscription.end_date}")
            print(f"      Активна: {subscription.is_active()}")
            print(f"      Дней осталось: {subscription.days_until_expiry()}")
            print(f"   ✅ Подписка успешно создана")
        else:
            print(f"   ❌ Подписка не создана")
            return False
        
    except Exception as e:
        print(f"   ❌ Ошибка проверки результатов: {e}")
        return False
    
    # Шаг 6: Тестируем различные статусы платежей
    print(f"\n🔄 Шаг 6: Тест различных статусов платежей...")
    
    test_statuses = ['failed', 'cancelled', 'expired']
    
    for status in test_statuses:
        try:
            print(f"   🔄 Тестируем статус: {status}")
            
            # Создаем новый тестовый платеж
            test_order_id = f"test_{status}_{int(time.time())}"
            
            test_payment = db_service.create_payment(
                user_id=user.id,
                lava_invoice_id=test_order_id,
                amount=Decimal('299.00'),
                payment_method='card_ru',
                payment_url='https://example.com/pay',
                expires_at=datetime.now()
            )
            
            # Создаем webhook для этого статуса
            test_webhook_data = {
                'id': f"test_invoice_{status}",
                'orderId': test_order_id,
                'status': status,
                'amountTotal': {
                    'currency': 'RUB',
                    'amount': 299.0
                },
                'timestamp': datetime.now().isoformat()
            }
            
            # Обрабатываем webhook
            app.services.webhook_handler.request = MockRequest(test_webhook_data)
            test_result = webhook_handler.handle_lava_webhook()
            
            if test_result.get('success'):
                # Проверяем, что статус обновился
                updated_test_payment = db_service.get_payment_by_order_id(test_order_id)
                if updated_test_payment and updated_test_payment.status == status:
                    print(f"      ✅ Статус {status} обработан корректно")
                else:
                    print(f"      ❌ Статус {status} не обновился")
            else:
                print(f"      ❌ Ошибка обработки статуса {status}: {test_result.get('error')}")
                
        except Exception as e:
            print(f"      ❌ Исключение при тестировании статуса {status}: {e}")
    
    print(f"\n🎉 Полный тест интеграции завершен успешно!")
    print(f"\n📊 Итоговая статистика:")
    print(f"   • Создание счета: ✅")
    print(f"   • Сохранение в БД: ✅")
    print(f"   • Обработка webhook: ✅")
    print(f"   • Создание подписки: ✅")
    print(f"   • Различные статусы: ✅")
    
    return True

def test_webhook_endpoint():
    """Тестирует webhook endpoint через HTTP запрос"""
    
    print(f"\n=== Тест HTTP webhook endpoint ===")
    
    webhook_url = f"{Config.WEBHOOK_URL}/webhook"
    print(f"Webhook URL: {webhook_url}")
    
    # Тестовые данные webhook
    test_webhook_data = {
        'id': 'test-invoice-123',
        'orderId': f'tg_123456789_{int(time.time())}',
        'status': 'completed',
        'amountTotal': {
            'currency': 'RUB',
            'amount': 299.0
        },
        'custom': {
            'user_id': 123456789,
            'plan_months': 1,
            'payment_method': 'ru_card'
        },
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        print(f"🔄 Отправляем POST запрос на webhook...")
        
        response = requests.post(
            webhook_url,
            json=test_webhook_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📥 Ответ сервера:")
        print(f"   Статус: {response.status_code}")
        print(f"   Заголовки: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"   Данные: {json.dumps(response_data, indent=6, ensure_ascii=False)}")
        except:
            print(f"   Текст: {response.text}")
        
        if response.status_code == 200:
            print(f"   ✅ Webhook endpoint работает")
        else:
            print(f"   ❌ Ошибка webhook endpoint")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Ошибка HTTP запроса: {e}")
        print(f"   💡 Убедитесь, что Flask приложение запущено")

if __name__ == '__main__':
    print("🚀 Запуск полного теста интеграции с Lava.top API")
    print("=" * 60)
    
    # Основной тест интеграции
    success = test_full_integration()
    
    if success:
        print(f"\n🎯 Все тесты пройдены успешно!")
        print(f"💡 Интеграция с Lava.top API готова к использованию")
        
        # Дополнительный тест HTTP endpoint (опционально)
        try:
            test_webhook_endpoint()
        except Exception as e:
            print(f"⚠️  HTTP тест пропущен: {e}")
    else:
        print(f"\n❌ Некоторые тесты не прошли")
        print(f"💡 Проверьте логи и исправьте ошибки")
    
    print("=" * 60)