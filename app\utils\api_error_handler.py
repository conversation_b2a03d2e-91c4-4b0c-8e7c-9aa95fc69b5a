"""
Специализированная обработка ошибок API с повторными попытками и восстановлением
"""

import logging
import time
import functools
import requests
from typing import Optional, Dict, Any, Callable, Union
from datetime import datetime, timedelta
from telebot.apihelper import ApiException

from app.utils.error_handler import (
    <PERSON>rrorHand<PERSON>, ErrorCategory, ErrorSeverity, 
    with_error_handling, with_retry, CircuitBreaker
)

logger = logging.getLogger('api_error_handler')

class APIErrorHandler:
    """Специализированный обработчик ошибок API"""
    
    # HTTP коды, которые следует повторять
    RETRYABLE_HTTP_CODES = {500, 502, 503, 504, 429}
    
    # HTTP коды, которые не следует повторять
    NON_RETRYABLE_HTTP_CODES = {400, 401, 403, 404, 422}
    
    # Telegram API коды ошибок, которые следует повторять
    RETRYABLE_TELEGRAM_CODES = {429, 500, 502, 503, 504}
    
    def __init__(self):
        self.error_handler = ErrorHandler()
        self.circuit_breakers = {}
        self.api_stats = {
            'total_requests': 0,
            'failed_requests': 0,
            'retried_requests': 0,
            'circuit_breaker_trips': 0,
            'rate_limit_hits': 0
        }
    
    def get_circuit_breaker(self, api_name: str) -> CircuitBreaker:
        """
        Получает или создает circuit breaker для API
        
        Args:
            api_name: Имя API
            
        Returns:
            Circuit breaker для API
        """
        if api_name not in self.circuit_breakers:
            self.circuit_breakers[api_name] = CircuitBreaker(
                failure_threshold=5,
                recovery_timeout=60,
                expected_exception=requests.exceptions.RequestException
            )
        
        return self.circuit_breakers[api_name]
    
    def handle_http_error(self, error: requests.exceptions.RequestException,
                         context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Обрабатывает HTTP ошибки
        
        Args:
            error: HTTP ошибка
            context: Контекст ошибки
            
        Returns:
            Информация об обработке ошибки
        """
        self.api_stats['failed_requests'] += 1
        
        error_info = {
            'error_type': type(error).__name__,
            'message': str(error),
            'retryable': False,
            'severity': ErrorSeverity.MEDIUM,
            'suggested_delay': 1.0
        }
        
        if isinstance(error, requests.exceptions.HTTPError):
            status_code = error.response.status_code if error.response else 0
            error_info.update(self._analyze_http_status_code(status_code))
            
        elif isinstance(error, requests.exceptions.Timeout):
            error_info.update({
                'retryable': True,
                'severity': ErrorSeverity.MEDIUM,
                'suggested_delay': 2.0
            })
            
        elif isinstance(error, requests.exceptions.ConnectionError):
            error_info.update({
                'retryable': True,
                'severity': ErrorSeverity.HIGH,
                'suggested_delay': 5.0
            })
            
        elif isinstance(error, requests.exceptions.TooManyRedirects):
            error_info.update({
                'retryable': False,
                'severity': ErrorSeverity.LOW
            })
        
        # Логируем ошибку
        self.error_handler.handle_error(
            error=error,
            category=ErrorCategory.API,
            severity=error_info['severity'],
            context=context or {}
        )
        
        return error_info
    
    def handle_telegram_error(self, error: ApiException,
                            context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Обрабатывает ошибки Telegram API
        
        Args:
            error: Ошибка Telegram API
            context: Контекст ошибки
            
        Returns:
            Информация об обработке ошибки
        """
        self.api_stats['failed_requests'] += 1
        
        error_code = getattr(error, 'error_code', 0)
        error_description = getattr(error, 'description', str(error))
        
        error_info = {
            'error_type': 'TelegramApiException',
            'error_code': error_code,
            'message': error_description,
            'retryable': error_code in self.RETRYABLE_TELEGRAM_CODES,
            'severity': self._get_telegram_error_severity(error_code),
            'suggested_delay': self._get_telegram_retry_delay(error_code, error_description)
        }
        
        # Специальная обработка rate limiting
        if error_code == 429:
            self.api_stats['rate_limit_hits'] += 1
            retry_after = self._extract_retry_after(error_description)
            error_info['suggested_delay'] = retry_after
        
        # Логируем ошибку
        self.error_handler.handle_error(
            error=error,
            category=ErrorCategory.TELEGRAM,
            severity=error_info['severity'],
            context=context or {}
        )
        
        return error_info
    
    def _analyze_http_status_code(self, status_code: int) -> Dict[str, Any]:
        """
        Анализирует HTTP статус код и возвращает информацию об ошибке
        
        Args:
            status_code: HTTP статус код
            
        Returns:
            Информация об ошибке
        """
        if status_code in self.RETRYABLE_HTTP_CODES:
            return {
                'retryable': True,
                'severity': ErrorSeverity.MEDIUM if status_code != 429 else ErrorSeverity.LOW,
                'suggested_delay': 30.0 if status_code == 429 else 5.0
            }
        elif status_code in self.NON_RETRYABLE_HTTP_CODES:
            return {
                'retryable': False,
                'severity': ErrorSeverity.LOW if status_code in {400, 404} else ErrorSeverity.MEDIUM
            }
        elif 500 <= status_code < 600:
            return {
                'retryable': True,
                'severity': ErrorSeverity.HIGH,
                'suggested_delay': 10.0
            }
        else:
            return {
                'retryable': False,
                'severity': ErrorSeverity.MEDIUM
            }
    
    def _get_telegram_error_severity(self, error_code: int) -> ErrorSeverity:
        """
        Определяет серьезность ошибки Telegram API
        
        Args:
            error_code: Код ошибки Telegram
            
        Returns:
            Уровень серьезности
        """
        if error_code == 429:  # Rate limit
            return ErrorSeverity.LOW
        elif error_code in {400, 404}:  # Bad request, not found
            return ErrorSeverity.LOW
        elif error_code in {401, 403}:  # Unauthorized, forbidden
            return ErrorSeverity.HIGH
        elif error_code >= 500:  # Server errors
            return ErrorSeverity.HIGH
        else:
            return ErrorSeverity.MEDIUM
    
    def _get_telegram_retry_delay(self, error_code: int, description: str) -> float:
        """
        Определяет задержку для повторной попытки Telegram API
        
        Args:
            error_code: Код ошибки
            description: Описание ошибки
            
        Returns:
            Задержка в секундах
        """
        if error_code == 429:
            return self._extract_retry_after(description)
        elif error_code >= 500:
            return 5.0
        else:
            return 1.0
    
    def _extract_retry_after(self, description: str) -> float:
        """
        Извлекает значение retry_after из описания ошибки
        
        Args:
            description: Описание ошибки
            
        Returns:
            Время ожидания в секундах
        """
        import re
        
        # Ищем паттерн "retry after X seconds"
        match = re.search(r'retry after (\d+)', description.lower())
        if match:
            return float(match.group(1))
        
        # Значение по умолчанию для rate limiting
        return 30.0
    
    def get_api_stats(self) -> Dict[str, Any]:
        """Возвращает статистику API"""
        stats = self.api_stats.copy()
        
        # Добавляем информацию о circuit breakers
        stats['circuit_breakers'] = {}
        for api_name, cb in self.circuit_breakers.items():
            stats['circuit_breakers'][api_name] = {
                'state': cb.state,
                'failure_count': cb.failure_count,
                'last_failure_time': cb.last_failure_time
            }
        
        return stats
    
    def reset_api_stats(self):
        """Сбрасывает статистику API"""
        self.api_stats = {
            'total_requests': 0,
            'failed_requests': 0,
            'retried_requests': 0,
            'circuit_breaker_trips': 0,
            'rate_limit_hits': 0
        }

def with_api_error_handling(api_name: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM):
    """
    Декоратор для обработки ошибок API
    
    Args:
        api_name: Имя API для circuit breaker
        severity: Серьезность ошибок по умолчанию
    """
    def decorator(func: Callable) -> Callable:
        api_handler = APIErrorHandler()
        circuit_breaker = api_handler.get_circuit_breaker(api_name)
        
        @functools.wraps(func)
        @circuit_breaker
        def wrapper(*args, **kwargs):
            api_handler.api_stats['total_requests'] += 1
            
            try:
                return func(*args, **kwargs)
            except (requests.exceptions.RequestException, ApiException) as e:
                if isinstance(e, requests.exceptions.RequestException):
                    error_info = api_handler.handle_http_error(e)
                else:
                    error_info = api_handler.handle_telegram_error(e)
                
                # Если ошибка критическая, поднимаем исключение
                if error_info['severity'] == ErrorSeverity.CRITICAL:
                    raise
                
                return None
        
        return wrapper
    return decorator

def with_api_retry(api_name: str, max_retries: Optional[int] = None,
                  custom_delay_func: Optional[Callable[[int, Exception], float]] = None):
    """
    Декоратор для повторных попыток API запросов
    
    Args:
        api_name: Имя API
        max_retries: Максимальное количество повторов
        custom_delay_func: Пользовательская функция для вычисления задержки
    """
    def decorator(func: Callable) -> Callable:
        api_handler = APIErrorHandler()
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            retries = max_retries or 3
            last_error = None
            
            for attempt in range(retries + 1):
                try:
                    api_handler.api_stats['total_requests'] += 1
                    return func(*args, **kwargs)
                    
                except Exception as e:
                    last_error = e
                    api_handler.api_stats['failed_requests'] += 1
                    
                    # Анализируем ошибку
                    if isinstance(e, requests.exceptions.RequestException):
                        error_info = api_handler.handle_http_error(e)
                    elif isinstance(e, ApiException):
                        error_info = api_handler.handle_telegram_error(e)
                    else:
                        # Неизвестная ошибка, не повторяем
                        break
                    
                    # Проверяем, можно ли повторить
                    if attempt >= retries or not error_info.get('retryable', False):
                        break
                    
                    api_handler.api_stats['retried_requests'] += 1
                    
                    # Вычисляем задержку
                    if custom_delay_func:
                        delay = custom_delay_func(attempt + 1, e)
                    else:
                        delay = error_info.get('suggested_delay', 1.0)
                        # Экспоненциальная задержка с джиттером
                        delay *= (2 ** attempt)
                        delay = min(delay, 60.0)  # Максимум 60 секунд
                        
                        # Добавляем джиттер
                        import random
                        delay *= (0.5 + random.random() * 0.5)
                    
                    logger.info(f"Повторная попытка {attempt + 1}/{retries} для {api_name} через {delay:.2f}с")
                    time.sleep(delay)
            
            # Если дошли до сюда, все попытки исчерпаны
            if last_error:
                raise last_error
        
        return wrapper
    return decorator

class RateLimiter:
    """
    Ограничитель скорости запросов для API
    """
    
    def __init__(self, max_requests: int, time_window: int):
        """
        Args:
            max_requests: Максимальное количество запросов
            time_window: Временное окно в секундах
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    def can_make_request(self) -> bool:
        """
        Проверяет, можно ли сделать запрос
        
        Returns:
            True если запрос разрешен
        """
        now = time.time()
        
        # Удаляем старые запросы
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.time_window]
        
        return len(self.requests) < self.max_requests
    
    def record_request(self):
        """Записывает факт выполнения запроса"""
        self.requests.append(time.time())
    
    def wait_time(self) -> float:
        """
        Возвращает время ожидания до следующего разрешенного запроса
        
        Returns:
            Время ожидания в секундах
        """
        if self.can_make_request():
            return 0.0
        
        # Время до истечения самого старого запроса
        oldest_request = min(self.requests)
        return self.time_window - (time.time() - oldest_request)

def with_rate_limiting(max_requests: int, time_window: int):
    """
    Декоратор для ограничения скорости запросов
    
    Args:
        max_requests: Максимальное количество запросов
        time_window: Временное окно в секундах
    """
    rate_limiter = RateLimiter(max_requests, time_window)
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Проверяем, можно ли сделать запрос
            if not rate_limiter.can_make_request():
                wait_time = rate_limiter.wait_time()
                logger.info(f"Rate limit достигнут, ожидание {wait_time:.2f}с")
                time.sleep(wait_time)
            
            # Записываем запрос и выполняем функцию
            rate_limiter.record_request()
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

class APIHealthMonitor:
    """
    Монитор здоровья API
    """
    
    def __init__(self, api_name: str):
        self.api_name = api_name
        self.health_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'last_success_time': None,
            'last_failure_time': None,
            'consecutive_failures': 0
        }
        self.response_times = []
        self.max_response_times = 100
    
    def record_request(self, success: bool, response_time: float):
        """
        Записывает результат запроса
        
        Args:
            success: Успешность запроса
            response_time: Время ответа в секундах
        """
        self.health_stats['total_requests'] += 1
        
        if success:
            self.health_stats['successful_requests'] += 1
            self.health_stats['last_success_time'] = datetime.now()
            self.health_stats['consecutive_failures'] = 0
        else:
            self.health_stats['failed_requests'] += 1
            self.health_stats['last_failure_time'] = datetime.now()
            self.health_stats['consecutive_failures'] += 1
        
        # Записываем время ответа
        self.response_times.append(response_time)
        if len(self.response_times) > self.max_response_times:
            self.response_times = self.response_times[-self.max_response_times:]
        
        # Обновляем среднее время ответа
        if self.response_times:
            self.health_stats['average_response_time'] = sum(self.response_times) / len(self.response_times)
    
    def is_healthy(self) -> bool:
        """
        Проверяет, здоров ли API
        
        Returns:
            True если API здоров
        """
        # API считается нездоровым если:
        # 1. Более 5 последовательных неудач
        # 2. Процент успешных запросов менее 80% за последние 100 запросов
        # 3. Среднее время ответа более 30 секунд
        
        if self.health_stats['consecutive_failures'] > 5:
            return False
        
        if self.health_stats['total_requests'] > 10:
            success_rate = (self.health_stats['successful_requests'] / 
                          self.health_stats['total_requests'])
            if success_rate < 0.8:
                return False
        
        if self.health_stats['average_response_time'] > 30.0:
            return False
        
        return True
    
    def get_health_report(self) -> Dict[str, Any]:
        """Возвращает отчет о здоровье API"""
        report = self.health_stats.copy()
        report['api_name'] = self.api_name
        report['is_healthy'] = self.is_healthy()
        
        if self.health_stats['total_requests'] > 0:
            report['success_rate'] = (self.health_stats['successful_requests'] / 
                                    self.health_stats['total_requests'])
        else:
            report['success_rate'] = 0.0
        
        return report

def with_api_monitoring(api_name: str):
    """
    Декоратор для мониторинга здоровья API
    
    Args:
        api_name: Имя API
    """
    monitor = APIHealthMonitor(api_name)
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            success = False
            
            try:
                result = func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                raise
            finally:
                response_time = time.time() - start_time
                monitor.record_request(success, response_time)
        
        return wrapper
    return decorator