@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Setting up virtual environment and start script...
echo.
echo Creating start.sh script:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S -u telegrambot bash -c 'cat > /home/<USER>/app/start.sh << \"EOF\"
#!/bin/bash
cd /home/<USER>/app
source venv/bin/activate
python main.py
EOF'"
echo.
echo Making start.sh executable:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S chmod +x /home/<USER>/app/start.sh"
echo.
echo Installing Python dependencies in virtual environment:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S -u telegrambot bash -c 'cd /home/<USER>/app && source venv/bin/activate && pip install -r requirements.txt'"
echo.
echo Checking start.sh content:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "cat /home/<USER>/app/start.sh"
echo.
echo Testing bot configuration:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S -u telegrambot bash -c 'cd /home/<USER>/app && source venv/bin/activate && python -c \"import config; print(\\\"Config loaded successfully\\\")\"'"
echo Done.