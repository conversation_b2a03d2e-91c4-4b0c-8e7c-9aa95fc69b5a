# Update admin panel on server using plink
param(
    [string]$ServerIP = "**************",
    [string]$Username = "ubuntu"
)

Write-Host "=== ADMIN PANEL UPDATE SCRIPT ===" -ForegroundColor Green
Write-Host ""

# Create archive first
Write-Host "1. Creating archive..." -ForegroundColor Yellow
if (Test-Path "admin_panel_fix.zip") {
    Remove-Item "admin_panel_fix.zip" -Force
}
Compress-Archive -Path "app\admin.py", "templates\*" -DestinationPath "admin_panel_fix.zip" -Force
$fileInfo = Get-Item "admin_panel_fix.zip"
Write-Host "   Archive created: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Green

# Get password securely
Write-Host ""
Write-Host "2. Server authentication..." -ForegroundColor Yellow
$SecurePassword = Read-Host "Enter server password" -AsSecureString
$Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))

Write-Host ""
Write-Host "3. Uploading archive to server..." -ForegroundColor Yellow

# Upload file using pscp (PuTTY's scp)
& pscp -pw $Password admin_panel_fix.zip $Username@${ServerIP}:/home/<USER>/
if ($LASTEXITCODE -ne 0) {
    Write-Host "   Failed to upload with pscp, trying with plink..." -ForegroundColor Yellow
    # Alternative: create upload command
    Write-Host "   Please upload admin_panel_fix.zip manually to /home/<USER>/ on server" -ForegroundColor Red
    Write-Host "   Press Enter when file is uploaded..." -ForegroundColor Yellow
    Read-Host
} else {
    Write-Host "   Archive uploaded successfully" -ForegroundColor Green
}

Write-Host ""
Write-Host "4. Stopping telegram-bot service..." -ForegroundColor Yellow

$StopService = @"
echo '$Password' | sudo -S systemctl stop telegram-bot
"@

echo $StopService | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host ""
Write-Host "5. Creating backup..." -ForegroundColor Yellow

$CreateBackup = @"
cd /home/<USER>/telegram_bot
echo '$Password' | sudo -S cp -r app templates backup_`$(date +%Y%m%d_%H%M%S)
echo "Backup created"
"@

echo $CreateBackup | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host ""
Write-Host "6. Extracting updates..." -ForegroundColor Yellow

$ExtractUpdates = @"
cd /home/<USER>/telegram_bot
unzip -o /home/<USER>/admin_panel_fix.zip
echo "Updates extracted"
"@

echo $ExtractUpdates | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host ""
Write-Host "7. Setting permissions..." -ForegroundColor Yellow

$SetPermissions = @"
cd /home/<USER>/telegram_bot
echo '$Password' | sudo -S chown -R ubuntu:ubuntu app templates
echo '$Password' | sudo -S chmod -R 755 app templates
echo "Permissions set"
"@

echo $SetPermissions | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host ""
Write-Host "8. Starting telegram-bot service..." -ForegroundColor Yellow

$StartService = @"
echo '$Password' | sudo -S systemctl start telegram-bot
"@

echo $StartService | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host ""
Write-Host "9. Checking service status..." -ForegroundColor Yellow

$CheckStatus = @"
echo '$Password' | sudo -S systemctl status telegram-bot --no-pager -l
"@

echo $CheckStatus | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host ""
Write-Host "10. Verifying updates..." -ForegroundColor Yellow

$VerifyUpdates = @"
cd /home/<USER>/telegram_bot
if grep -q 'safe_url_for' app/admin.py; then
    echo "✓ admin.py updated successfully"
else
    echo "✗ admin.py update failed"
fi

if grep -q 'safe_url_for' templates/base.html; then
    echo "✓ templates updated successfully"
else
    echo "✗ templates update failed"
fi
"@

echo $VerifyUpdates | plink -ssh -batch -pw $Password $Username@$ServerIP

Write-Host ""
Write-Host "=== UPDATE COMPLETED ===" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Open: https://$ServerIP/admin/login" -ForegroundColor White
Write-Host "2. Enter password: admin123" -ForegroundColor White
Write-Host "3. Test all admin panel sections" -ForegroundColor White
Write-Host ""
Write-Host "If there are issues, check logs:" -ForegroundColor Yellow
Write-Host "ssh $Username@$ServerIP 'sudo journalctl -u telegram-bot -f'" -ForegroundColor White
Write-Host ""
Write-Host "To restart service if needed:" -ForegroundColor Yellow
Write-Host ".\restart_services.ps1" -ForegroundColor White