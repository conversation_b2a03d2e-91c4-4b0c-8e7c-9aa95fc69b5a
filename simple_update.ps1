# Simple manual update script
# This script will prepare files and show you the commands to run

Write-Host "=== ADMIN PANEL UPDATE PREPARATION ===" -ForegroundColor Green
Write-Host ""

# Create archive
Write-Host "1. Creating archive..." -ForegroundColor Yellow
if (Test-Path "admin_panel_fix.zip") {
    Remove-Item "admin_panel_fix.zip" -Force
}
Compress-Archive -Path "app\admin.py", "templates\*" -DestinationPath "admin_panel_fix.zip" -Force
Write-Host "   Archive 'admin_panel_fix.zip' created successfully" -ForegroundColor Green

# Show file info
$fileInfo = Get-Item "admin_panel_fix.zip"
Write-Host "   Size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Gray

Write-Host ""
Write-Host "2. MANUAL UPLOAD COMMANDS:" -ForegroundColor Cyan
Write-Host ""
Write-Host "Copy and paste these commands one by one:" -ForegroundColor Yellow
Write-Host ""

Write-Host "# Upload archive to server" -ForegroundColor Gray
Write-Host "scp admin_panel_fix.zip ubuntu@**************:/home/<USER>/" -ForegroundColor White
Write-Host ""

Write-Host "# Connect to server" -ForegroundColor Gray
Write-Host "ssh ubuntu@**************" -ForegroundColor White
Write-Host "# Password: dkomqgTaijxro7in^bxd" -ForegroundColor Gray
Write-Host ""

Write-Host "# On server, run these commands:" -ForegroundColor Gray
Write-Host "cd /home/<USER>/telegram_bot" -ForegroundColor White
Write-Host "sudo systemctl stop telegram-bot" -ForegroundColor White
Write-Host "sudo cp -r app templates backup_`$(date +%Y%m%d_%H%M%S)" -ForegroundColor White
Write-Host "unzip -o /home/<USER>/admin_panel_fix.zip" -ForegroundColor White
Write-Host "sudo chown -R ubuntu:ubuntu app templates" -ForegroundColor White
Write-Host "sudo chmod -R 755 app templates" -ForegroundColor White
Write-Host "sudo systemctl start telegram-bot" -ForegroundColor White
Write-Host "sudo systemctl status telegram-bot" -ForegroundColor White
Write-Host ""

Write-Host "3. TESTING:" -ForegroundColor Cyan
Write-Host "   Open: https://**************/admin/login" -ForegroundColor White
Write-Host "   Password: admin123" -ForegroundColor White
Write-Host ""

Write-Host "4. CHECK LOGS (if needed):" -ForegroundColor Cyan
Write-Host "   sudo journalctl -u telegram-bot -f" -ForegroundColor White
Write-Host ""

Write-Host "=== PREPARATION COMPLETE ===" -ForegroundColor Green
Write-Host "Archive is ready for upload!" -ForegroundColor Green