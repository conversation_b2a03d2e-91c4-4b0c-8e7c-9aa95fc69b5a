{% extends "base.html" %}

{% block title %}Логи системы{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-journal-text"></i> Логи системы
    </h1>
</div>

<div class="card shadow">
    <div class="card-body">
        {% if logs %}
        <div class="table-responsive">
            <table class="table table-hover table-sm">
                <thead>
                    <tr>
                        <th>Время</th>
                        <th>Администратор</th>
                        <th>Действие</th>
                        <th>Пользователь</th>
                        <th>Детали</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs %}
                    <tr>
                        <td>{{ log.created_at.strftime('%d.%m.%Y %H:%M:%S') if log.created_at else '-' }}</td>
                        <td>
                            {% if log.admin_telegram_id == 0 %}
                                <span class="badge bg-primary">Веб-админ</span>
                            {% else %}
                                <code>{{ log.admin_telegram_id }}</code>
                            {% endif %}
                        </td>
                        <td>
                            {% set action_colors = {
                                'view_users': 'secondary',
                                'view_subscriptions': 'secondary', 
                                'view_payments': 'secondary',
                                'grant_subscription': 'success',
                                'revoke_subscription': 'warning',
                                'cancel_subscription': 'warning',
                                'bulk_grant_subscription': 'success',
                                'bulk_revoke_subscription': 'warning',
                                'manual_payment_processing': 'info'
                            } %}
                            <span class="badge bg-{{ action_colors.get(log.action, 'info') }}">{{ log.action }}</span>
                        </td>
                        <td>
                            {% if log.target_user_id %}
                                {% if hasattr(log, 'target_user_name') and log.target_user_name %}
                                    <a href="{{ safe_url_for('admin.user_detail', user_id=log.target_user_id) }}" class="text-decoration-none">
                                        <i class="bi bi-person-circle"></i> {{ log.target_user_name }}
                                    </a>
                                    <br><small class="text-muted">ID: {{ log.target_user_id }}</small>
                                {% else %}
                                    <a href="{{ safe_url_for('admin.user_detail', user_id=log.target_user_id) }}" class="text-decoration-none">
                                        <i class="bi bi-person-circle"></i> User {{ log.target_user_id }}
                                    </a>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if log.details %}
                                <small class="text-muted">{{ log.details }}</small>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-journal-x text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">Логи не найдены</h4>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}