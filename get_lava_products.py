#!/usr/bin/env python3
"""
Скрипт для получения списка продуктов и офферов из Lava.top API
"""

import os
import requests
import json
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

def get_lava_products():
    """Получает список продуктов из Lava API"""
    
    api_key = os.getenv('LAVA_API_KEY')
    base_url = os.getenv('LAVA_BASE_URL', 'https://gate.lava.top')
    
    if not api_key:
        print("❌ LAVA_API_KEY не установлен")
        return
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("=== Получение списка продуктов ===")
    
    try:
        response = requests.get(f"{base_url}/api/v2/products", headers=headers, timeout=10)
        
        print(f"Статус: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Успешно получен список продуктов:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # Извлекаем продукты
            if 'items' in data and data['items']:
                print("\n📋 Доступные продукты:")
                for i, product in enumerate(data['items'], 1):
                    print(f"{i}. ID: {product.get('id', 'N/A')}")
                    print(f"   Название: {product.get('name', 'N/A')}")
                    print(f"   Описание: {product.get('description', 'N/A')}")
                    print(f"   Цена: {product.get('price', 'N/A')}")
                    print(f"   Валюта: {product.get('currency', 'N/A')}")
                    print()
                    
                # Возвращаем первый продукт для использования
                if data['items']:
                    first_product = data['items'][0]
                    print(f"🎯 Рекомендуемый ID для использования: {first_product.get('id')}")
                    return first_product.get('id')
            else:
                print("❌ Продукты не найдены")
                
        else:
            print(f"❌ Ошибка {response.status_code}")
            print(f"Ответ: {response.text}")
            
    except Exception as e:
        print(f"❌ Ошибка запроса: {e}")
    
    return None

def test_invoice_with_product_id(product_id):
    """Тестирует создание счета с реальным ID продукта"""
    
    if not product_id:
        print("❌ ID продукта не получен")
        return
    
    api_key = os.getenv('LAVA_API_KEY')
    base_url = os.getenv('LAVA_BASE_URL', 'https://gate.lava.top')
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    # Данные для создания счета с реальным ID продукта
    invoice_data = {
        "orderId": f"test_{int(time.time())}",
        "sum": 299.0,
        "currency": "RUB",
        "offerId": product_id,
        "email": "<EMAIL>",
        "description": "Тестовый платеж - подписка на канал",
        "successUrl": "https://example.com/success",
        "failUrl": "https://example.com/fail",
        "hookUrl": "https://example.com/webhook"
    }
    
    print(f"\n=== Тест создания счета с ID продукта: {product_id} ===")
    
    try:
        response = requests.post(
            f"{base_url}/api/v2/invoice",
            headers=headers,
            json=invoice_data,
            timeout=10
        )
        
        print(f"Статус: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("✅ Счет успешно создан!")
            data = response.json()
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ Ошибка {response.status_code}")
            print(f"Ответ: {response.text}")
            
    except Exception as e:
        print(f"❌ Ошибка запроса: {e}")

if __name__ == '__main__':
    import time
    
    # Получаем список продуктов
    product_id = get_lava_products()
    
    # Тестируем создание счета с реальным ID
    if product_id:
        test_invoice_with_product_id(product_id)
    else:
        print("\n❌ Не удалось получить ID продукта для тестирования")
        print("💡 Возможно, нужно создать продукт в личном кабинете Lava.top")