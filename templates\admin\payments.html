{% extends "base.html" %}

{% block title %}Платежи{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-credit-card"></i> Управление платежами
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ safe_url_for('admin.reports') }}" class="btn btn-outline-primary">
            <i class="bi bi-graph-up"></i> Отчеты
        </a>
    </div>
</div>

<!-- Статистика платежей -->
{% if payment_stats %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Всего платежей</h6>
                        <h3 class="mb-0">{{ payment_stats.total_payments or 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-credit-card" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Завершенные</h6>
                        <h3 class="mb-0">{{ payment_stats.completed_payments or 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">В ожидании</h6>
                        <h3 class="mb-0">{{ payment_stats.pending_payments or 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Общий доход</h6>
                        <h3 class="mb-0">{{ "%.0f"|format(payment_stats.total_revenue or 0) }} ₽</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-currency-exchange" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Фильтры -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Поиск</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="ID, пользователь, invoice...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Статус</label>
                <select class="form-select" id="status" name="status">
                    <option value="">Все статусы</option>
                    <option value="pending" {{ 'selected' if status_filter == 'pending' }}>В ожидании</option>
                    <option value="completed" {{ 'selected' if status_filter == 'completed' }}>Завершен</option>
                    <option value="failed" {{ 'selected' if status_filter == 'failed' }}>Неудачный</option>
                    <option value="expired" {{ 'selected' if status_filter == 'expired' }}>Истек</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="payment_method" class="form-label">Способ оплаты</label>
                <select class="form-select" id="payment_method" name="payment_method">
                    <option value="">Все способы</option>
                    <option value="card_ru" {{ 'selected' if payment_method_filter == 'card_ru' }}>Карта РФ</option>
                    <option value="card_foreign" {{ 'selected' if payment_method_filter == 'card_foreign' }}>Карта иностранная</option>
                    <option value="crypto" {{ 'selected' if payment_method_filter == 'crypto' }}>Криптовалюта</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">Дата от</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">Дата до</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Таблица платежей -->
<div class="card shadow">
    <div class="card-body">
        {% if payments %}
        <div class="d-flex justify-content-between align-items-center mb-3">
            <span class="text-muted">Найдено {{ total_count }} платежей</span>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Пользователь</th>
                        <th>Сумма</th>
                        <th>Способ</th>
                        <th>Статус</th>
                        <th>Создан</th>
                        <th>Завершен</th>
                        <th>Действия</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td>
                            <strong>#{{ payment.id }}</strong>
                        </td>
                        <td>
                            {% if payment.user_first_name or payment.user_last_name %}
                                {{ payment.user_first_name }} {{ payment.user_last_name }}
                                {% if payment.user_username %}
                                    <br><small class="text-muted">@{{ payment.user_username }}</small>
                                {% endif %}
                            {% else %}
                                ID: {{ payment.user_id }}
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ "%.2f"|format(payment.amount) }} {{ payment.currency }}</strong>
                        </td>
                        <td>
                            {% if payment.payment_method == 'card_ru' %}
                                <span class="badge bg-primary">Карта РФ</span>
                            {% elif payment.payment_method == 'card_foreign' %}
                                <span class="badge bg-info">Карта иностранная</span>
                            {% elif payment.payment_method == 'crypto' %}
                                <span class="badge bg-warning">Криптовалюта</span>
                            {% else %}
                                {{ payment.payment_method }}
                            {% endif %}
                        </td>
                        <td>
                            {% if payment.status == 'completed' %}
                                <span class="badge bg-success">Завершен</span>
                            {% elif payment.status == 'pending' %}
                                <span class="badge bg-warning">В ожидании</span>
                            {% elif payment.status == 'failed' %}
                                <span class="badge bg-danger">Неудачный</span>
                            {% elif payment.status == 'expired' %}
                                <span class="badge bg-secondary">Истек</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ payment.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ payment.created_at.strftime('%d.%m.%Y %H:%M') if payment.created_at else '-' }}
                        </td>
                        <td>
                            {{ payment.completed_at.strftime('%d.%m.%Y %H:%M') if payment.completed_at else '-' }}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ safe_url_for('admin.payment_detail', payment_id=payment.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Подробнее">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if payment.status == 'pending' %}
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            data-bs-toggle="dropdown" title="Обработать">
                                        <i class="bi bi-gear"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item text-success" href="#" 
                                               onclick="processPayment({{ payment.id }}, 'complete')">
                                            <i class="bi bi-check-circle"></i> Завершить
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#" 
                                               onclick="processPayment({{ payment.id }}, 'fail')">
                                            <i class="bi bi-x-circle"></i> Отклонить
                                        </a></li>
                                        <li><a class="dropdown-item text-warning" href="#" 
                                               onclick="processPayment({{ payment.id }}, 'cancel')">
                                            <i class="bi bi-clock"></i> Отменить
                                        </a></li>
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Пагинация -->
        {% if total_pages > 1 %}
        <nav aria-label="Пагинация платежей">
            <ul class="pagination justify-content-center">
                {% if has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ safe_url_for('admin.payments', page=page-1, search=search, status=status_filter, payment_method=payment_method_filter, date_from=date_from, date_to=date_to) }}">
                        Предыдущая
                    </a>
                </li>
                {% endif %}
                
                {% for p in range(1, total_pages + 1) %}
                    {% if p == page %}
                    <li class="page-item active">
                        <span class="page-link">{{ p }}</span>
                    </li>
                    {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                    <li class="page-item">
                        <a class="page-link" href="{{ safe_url_for('admin.payments', page=p, search=search, status=status_filter, payment_method=payment_method_filter, date_from=date_from, date_to=date_to) }}">{{ p }}</a>
                    </li>
                    {% elif p == 4 and page > 5 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% elif p == total_pages - 3 and page < total_pages - 4 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ safe_url_for('admin.payments', page=page+1, search=search, status=status_filter, payment_method=payment_method_filter, date_from=date_from, date_to=date_to) }}">
                        Следующая
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-credit-card text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">Платежи не найдены</h4>
            {% if search or status_filter or payment_method_filter or date_from or date_to %}
            <p class="text-muted">Попробуйте изменить параметры поиска</p>
            <a href="{{ safe_url_for('admin.payments') }}" class="btn btn-outline-primary">Сбросить фильтры</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<script>
function processPayment(paymentId, action) {
    const actionText = {
        'complete': 'завершить',
        'fail': 'отклонить', 
        'cancel': 'отменить'
    };
    
    if (confirm(`Вы уверены, что хотите ${actionText[action]} этот платеж?`)) {
        fetch('/admin/api/process-payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                payment_id: paymentId,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Произошла ошибка при обработке платежа');
        });
    }
}
</script>
{% endblock %}