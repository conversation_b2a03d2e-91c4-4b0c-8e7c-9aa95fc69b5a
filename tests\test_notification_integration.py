"""
Интеграционные тесты для системы уведомлений
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal

from app.services.notification_service import NotificationService
from app.services.bot_handler import TelegramBotHandler
from app.models.database import DatabaseService, User, Subscription


class TestNotificationIntegration(unittest.TestCase):
    """Интеграционные тесты для системы уведомлений"""
    
    def setUp(self):
        """Настройка интеграционных тестов"""
        # Создаем реальные объекты с мокированными зависимостями
        self.mock_db_service = Mock(spec=DatabaseService)
        self.mock_payment_service = Mock()
        
        # Создаем mock для Telegram бота
        with patch('app.services.bot_handler.telebot.TeleBot') as mock_bot_class:
            self.mock_bot = Mock()
            mock_bot_class.return_value = self.mock_bot
            
            self.bot_handler = TelegramBotHandler(
                self.mock_db_service, 
                self.mock_payment_service
            )
            self.bot_handler.bot = self.mock_bot
        
        # Создаем сервис уведомлений
        self.notification_service = NotificationService(
            self.bot_handler, 
            self.mock_db_service
        )
        
        # Тестовые данные
        self.test_user = User(
            id=1,
            telegram_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.test_subscription = Subscription(
            id=1,
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=7),  # Истекает через 7 дней
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    def test_full_payment_notification_flow(self):
        """Тест полного цикла уведомлений при оплате"""
        # Arrange
        invoice_data = {
            'order_id': 'test_order_123',
            'amount': 500.0,
            'currency': 'RUB',
            'payment_method': 'ru_card',
            'payment_url': 'https://test-payment-url.com',
            'expires_at': datetime.now() + timedelta(hours=1)
        }
        
        invite_url = "https://t.me/+test_invite_link"
        subscription_end_date = datetime.now() + timedelta(days=30)
        
        self.mock_bot.send_message.return_value = True
        self.mock_payment_service.get_subscription_plans.return_value = {
            1: {'name': '1 месяц', 'price': 500}
        }
        self.mock_payment_service.get_available_payment_methods.return_value = {
            'ru_card': {'name': 'Карта РФ'}
        }
        
        # Act - отправляем ссылку для оплаты
        payment_link_result = self.notification_service.send_payment_link_notification(
            self.test_user.telegram_id, invoice_data, 1
        )
        
        # Act - отправляем подтверждение оплаты
        payment_confirmation_result = self.notification_service.send_payment_confirmation(
            self.test_user.telegram_id, invoice_data['amount'], invoice_data['payment_method']
        )
        
        # Act - отправляем пригласительную ссылку
        invite_link_result = self.notification_service.send_invite_link_notification(
            self.test_user.telegram_id, invite_url, subscription_end_date
        )
        
        # Assert
        self.assertTrue(payment_link_result)
        self.assertTrue(payment_confirmation_result)
        self.assertTrue(invite_link_result)
        
        # Проверяем, что все сообщения были отправлены
        self.assertEqual(self.mock_bot.send_message.call_count, 3)
        
        # Проверяем содержимое сообщений
        calls = self.mock_bot.send_message.call_args_list
        
        # Первое сообщение - ссылка для оплаты
        payment_link_call = calls[0]
        self.assertEqual(payment_link_call[0][0], self.test_user.telegram_id)
        self.assertIn("Счет для оплаты создан!", payment_link_call[0][1])
        
        # Второе сообщение - подтверждение оплаты
        confirmation_call = calls[1]
        self.assertEqual(confirmation_call[0][0], self.test_user.telegram_id)
        self.assertIn("Платеж успешно обработан!", confirmation_call[0][1])
        
        # Третье сообщение - пригласительная ссылка
        invite_call = calls[2]
        self.assertEqual(invite_call[0][0], self.test_user.telegram_id)
        self.assertIn("Оплата прошла успешно!", invite_call[0][1])
    
    def test_subscription_expiration_notification_flow(self):
        """Тест полного цикла уведомлений об истечении подписки"""
        # Arrange
        # Подписка истекает через 7 дней
        subscription_7_days = Subscription(
            id=1,
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now() - timedelta(days=23),
            end_date=datetime.now() + timedelta(days=7),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # Подписка истекает через 1 день
        subscription_1_day = Subscription(
            id=2,
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now() - timedelta(days=29),
            end_date=datetime.now() + timedelta(days=1),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # Истекшая подписка
        expired_subscription = Subscription(
            id=3,
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now() - timedelta(days=31),
            end_date=datetime.now() - timedelta(days=1),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.mock_db_service.get_subscriptions_expiring_in_days.side_effect = [
            [subscription_7_days],  # Для 7 дней
            [subscription_1_day]    # Для 1 дня
        ]
        self.mock_db_service.get_expired_subscriptions.return_value = [expired_subscription]
        self.mock_db_service.get_user_by_id.return_value = self.test_user
        self.mock_bot.send_message.return_value = True
        
        # Act - выполняем плановую проверку уведомлений
        result = self.notification_service.schedule_notification_check()
        
        # Assert
        expected_result = {
            'warnings_7_days': 1,
            'warnings_1_day': 1,
            'expired_notifications': 1,
            'total_errors': 0
        }
        self.assertEqual(result, expected_result)
        
        # Проверяем количество отправленных сообщений
        self.assertEqual(self.mock_bot.send_message.call_count, 3)
        
        # Проверяем вызовы к базе данных
        self.mock_db_service.get_subscriptions_expiring_in_days.assert_any_call(7)
        self.mock_db_service.get_subscriptions_expiring_in_days.assert_any_call(1)
        self.mock_db_service.get_expired_subscriptions.assert_called_once()
        
        # Проверяем содержимое сообщений
        calls = self.mock_bot.send_message.call_args_list
        
        # Предупреждение за 7 дней
        warning_7_call = calls[0]
        self.assertIn("Подписка истекает через неделю", warning_7_call[0][1])
        self.assertIn("⚠️", warning_7_call[0][1])
        
        # Предупреждение за 1 день
        warning_1_call = calls[1]
        self.assertIn("Подписка истекает завтра!", warning_1_call[0][1])
        self.assertIn("🔴", warning_1_call[0][1])
        
        # Уведомление об истечении
        expired_call = calls[2]
        self.assertIn("Ваша подписка истекла", expired_call[0][1])
        self.assertIn("❌", expired_call[0][1])
    
    def test_bulk_notification_with_mixed_results(self):
        """Тест массовой отправки с частичными ошибками"""
        # Arrange
        user_ids = [123, 456, 789, 101112]
        message_text = "Важное уведомление для всех пользователей"
        
        # Настраиваем mock так, чтобы некоторые отправки были неудачными
        def mock_send_message(user_id, text, **kwargs):
            if user_id in [456, 101112]:  # Эти пользователи недоступны
                raise Exception("User blocked bot")
            return True
        
        self.mock_bot.send_message.side_effect = mock_send_message
        
        # Act
        with patch('time.sleep'):  # Мокаем sleep для ускорения тестов
            result = self.notification_service.send_bulk_notification(
                user_ids, message_text, "important"
            )
        
        # Assert
        expected_result = {
            'successful': 2,
            'failed': 2,
            'total': 4
        }
        self.assertEqual(result, expected_result)
        
        # Проверяем количество попыток отправки
        self.assertEqual(self.mock_bot.send_message.call_count, 4)
    
    def test_notification_stats_integration(self):
        """Тест интеграции получения статистики уведомлений"""
        # Arrange
        active_subscriptions = [
            self.test_subscription,
            Subscription(id=2, user_id=2, plan_type="yearly", status="active",
                        start_date=datetime.now(), end_date=datetime.now() + timedelta(days=365),
                        created_at=datetime.now(), updated_at=datetime.now())
        ]
        
        expiring_7_days = [self.test_subscription]
        expiring_1_day = []
        expired_subscriptions = []
        
        self.mock_db_service.get_all_active_subscriptions.return_value = active_subscriptions
        self.mock_db_service.get_subscriptions_expiring_in_days.side_effect = [
            expiring_7_days,  # Для 7 дней
            expiring_1_day    # Для 1 дня
        ]
        self.mock_db_service.get_expired_subscriptions.return_value = expired_subscriptions
        
        # Act
        stats = self.notification_service.get_notification_stats()
        
        # Assert
        expected_stats = {
            'active_subscriptions': 2,
            'expiring_in_7_days': 1,
            'expiring_in_1_day': 0,
            'expired_subscriptions': 0
        }
        self.assertEqual(stats, expected_stats)
        
        # Проверяем вызовы к базе данных
        self.mock_db_service.get_all_active_subscriptions.assert_called_once()
        self.mock_db_service.get_subscriptions_expiring_in_days.assert_any_call(7)
        self.mock_db_service.get_subscriptions_expiring_in_days.assert_any_call(1)
        self.mock_db_service.get_expired_subscriptions.assert_called_once()
    
    def test_error_handling_in_notification_flow(self):
        """Тест обработки ошибок в потоке уведомлений"""
        # Arrange
        self.mock_db_service.get_subscriptions_expiring_in_days.side_effect = Exception("Database error")
        
        # Act
        result = self.notification_service.send_expiration_warnings()
        
        # Assert
        expected_result = {
            'warnings_7_days': 0,
            'warnings_1_day': 0,
            'errors': 1
        }
        self.assertEqual(result, expected_result)
    
    def test_admin_notification_integration(self):
        """Тест интеграции административных уведомлений"""
        # Arrange
        admin_user_ids = [111111, 222222]
        admin_message = "Критическая ошибка в системе платежей"
        
        self.mock_bot.send_message.return_value = True
        
        # Act
        result = self.notification_service.send_admin_notification(
            admin_user_ids, admin_message
        )
        
        # Assert
        self.assertTrue(result)
        self.assertEqual(self.mock_bot.send_message.call_count, 2)
        
        # Проверяем, что сообщения отправлены правильным получателям
        calls = self.mock_bot.send_message.call_args_list
        sent_user_ids = [call[0][0] for call in calls]
        self.assertEqual(set(sent_user_ids), set(admin_user_ids))
        
        # Проверяем содержимое сообщений
        for call in calls:
            self.assertEqual(call[0][1], admin_message)


if __name__ == '__main__':
    unittest.main()