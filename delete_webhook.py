#!/usr/bin/env python3
"""
Скрипт для удаления webhook и запуска бота
"""
import os
import requests
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')

def delete_webhook():
    """Удаляет webhook для бота"""
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/deleteWebhook"
    
    try:
        response = requests.post(url)
        result = response.json()
        
        if result.get('ok'):
            print("✅ Webhook успешно удален")
            return True
        else:
            print(f"❌ Ошибка удаления webhook: {result.get('description')}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при удалении webhook: {e}")
        return False

def get_webhook_info():
    """Получает информацию о текущем webhook"""
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getWebhookInfo"
    
    try:
        response = requests.get(url)
        result = response.json()
        
        if result.get('ok'):
            webhook_info = result.get('result', {})
            webhook_url = webhook_info.get('url', '')
            
            if webhook_url:
                print(f"🔗 Активный webhook: {webhook_url}")
                return True
            else:
                print("✅ Webhook не установлен")
                return False
        else:
            print(f"❌ Ошибка получения информации о webhook: {result.get('description')}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при получении информации о webhook: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Проверяем статус webhook...")
    
    if get_webhook_info():
        print("🗑️ Удаляем webhook...")
        if delete_webhook():
            print("✅ Готово! Теперь можно запускать бота в режиме polling")
        else:
            print("❌ Не удалось удалить webhook")
    else:
        print("✅ Webhook уже отсутствует, можно запускать бота")