"""
Модели данных с валидацией для Telegram Payment Bot
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation
from typing import Optional, List, Dict, Any
import re
import logging

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Исключение для ошибок валидации"""
    pass

@dataclass
class User:
    """Модель пользователя с валидацией"""
    id: Optional[int] = None
    telegram_id: int = 0
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Автоматическая валидация после создания объекта"""
        self.validate()
    
    def validate(self) -> bool:
        """Валидация данных пользователя"""
        errors = []
        
        # Валидация telegram_id
        if not isinstance(self.telegram_id, int) or self.telegram_id <= 0:
            errors.append("telegram_id должен быть положительным целым числом")
        
        # Валидация username (если указан)
        if self.username is not None:
            if not isinstance(self.username, str):
                errors.append("username должен быть строкой")
            elif len(self.username.strip()) == 0:
                errors.append("username не может быть пустым")
            elif len(self.username) > 32:
                errors.append("username не может быть длиннее 32 символов")
            elif not re.match(r'^[a-zA-Z0-9_]+$', self.username):
                errors.append("username может содержать только буквы, цифры и подчеркивания")
        
        # Валидация first_name (если указано)
        if self.first_name is not None:
            if not isinstance(self.first_name, str):
                errors.append("first_name должен быть строкой")
            elif len(self.first_name.strip()) == 0:
                errors.append("first_name не может быть пустым")
            elif len(self.first_name) > 64:
                errors.append("first_name не может быть длиннее 64 символов")
        
        # Валидация last_name (если указано)
        if self.last_name is not None:
            if not isinstance(self.last_name, str):
                errors.append("last_name должен быть строкой")
            elif len(self.last_name.strip()) == 0:
                errors.append("last_name не может быть пустым")
            elif len(self.last_name) > 64:
                errors.append("last_name не может быть длиннее 64 символов")
        
        # Валидация дат
        if self.created_at is not None and not isinstance(self.created_at, datetime):
            errors.append("created_at должен быть объектом datetime")
        
        if self.updated_at is not None and not isinstance(self.updated_at, datetime):
            errors.append("updated_at должен быть объектом datetime")
        
        if errors:
            raise ValidationError(f"Ошибки валидации User: {'; '.join(errors)}")
        
        return True
    
    def get_display_name(self) -> str:
        """Получить отображаемое имя пользователя"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return f"@{self.username}"
        else:
            return f"User {self.telegram_id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Преобразовать в словарь"""
        return {
            'id': self.id,
            'telegram_id': self.telegram_id,
            'username': self.username,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

@dataclass
class Subscription:
    """Модель подписки с валидацией"""
    id: Optional[int] = None
    user_id: int = 0
    plan_type: str = ""
    status: str = "active"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # Допустимые значения
    VALID_PLAN_TYPES = ['monthly', 'quarterly', 'semi_annual', 'yearly']
    VALID_STATUSES = ['active', 'expired', 'cancelled', 'pending']
    
    def __post_init__(self):
        """Автоматическая валидация после создания объекта"""
        # Устанавливаем даты по умолчанию если не указаны
        if self.start_date is None:
            self.start_date = datetime.now()
        if self.end_date is None and self.start_date:
            self.end_date = self._calculate_end_date()
        
        self.validate()
    
    def validate(self) -> bool:
        """Валидация данных подписки"""
        errors = []
        
        # Валидация user_id
        if not isinstance(self.user_id, int) or self.user_id <= 0:
            errors.append("user_id должен быть положительным целым числом")
        
        # Валидация plan_type
        if not isinstance(self.plan_type, str):
            errors.append("plan_type должен быть строкой")
        elif self.plan_type not in self.VALID_PLAN_TYPES:
            errors.append(f"plan_type должен быть одним из: {', '.join(self.VALID_PLAN_TYPES)}")
        
        # Валидация status
        if not isinstance(self.status, str):
            errors.append("status должен быть строкой")
        elif self.status not in self.VALID_STATUSES:
            errors.append(f"status должен быть одним из: {', '.join(self.VALID_STATUSES)}")
        
        # Валидация дат
        if self.start_date is not None and not isinstance(self.start_date, datetime):
            errors.append("start_date должен быть объектом datetime")
        
        if self.end_date is not None and not isinstance(self.end_date, datetime):
            errors.append("end_date должен быть объектом datetime")
        
        if (self.start_date and self.end_date and 
            self.start_date >= self.end_date):
            errors.append("end_date должен быть позже start_date")
        
        if self.created_at is not None and not isinstance(self.created_at, datetime):
            errors.append("created_at должен быть объектом datetime")
        
        if self.updated_at is not None and not isinstance(self.updated_at, datetime):
            errors.append("updated_at должен быть объектом datetime")
        
        if errors:
            raise ValidationError(f"Ошибки валидации Subscription: {'; '.join(errors)}")
        
        return True
    
    def _calculate_end_date(self) -> datetime:
        """Вычислить дату окончания подписки на основе типа плана"""
        if not self.start_date:
            return datetime.now()
        
        months_map = {
            'monthly': 1,
            'quarterly': 3,
            'semi_annual': 6,
            'yearly': 12
        }
        
        months = months_map.get(self.plan_type, 1)
        return self.start_date + timedelta(days=months * 30)  # Приблизительно
    
    def is_active(self) -> bool:
        """Проверить, активна ли подписка"""
        return (self.status == 'active' and 
                self.end_date and 
                self.end_date > datetime.now())
    
    def is_expired(self) -> bool:
        """Проверить, истекла ли подписка"""
        return (self.end_date and 
                self.end_date <= datetime.now())
    
    def days_until_expiry(self) -> int:
        """Количество дней до истечения подписки"""
        if not self.end_date:
            return 0
        
        delta = self.end_date - datetime.now()
        return max(0, delta.days)
    
    def extend_subscription(self, additional_months: int) -> None:
        """Продлить подписку на дополнительные месяцы"""
        if not isinstance(additional_months, int) or additional_months <= 0:
            raise ValidationError("additional_months должен быть положительным целым числом")
        
        if not self.end_date:
            self.end_date = datetime.now()
        
        self.end_date += timedelta(days=additional_months * 30)
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Преобразовать в словарь"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'plan_type': self.plan_type,
            'status': self.status,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active(),
            'days_until_expiry': self.days_until_expiry()
        }

@dataclass
class Payment:
    """Модель платежа с валидацией"""
    id: Optional[int] = None
    user_id: int = 0
    subscription_id: Optional[int] = None
    lava_invoice_id: str = ""
    amount: Decimal = Decimal('0.00')
    currency: str = "RUB"
    payment_method: str = ""
    status: str = "pending"
    payment_url: Optional[str] = None
    created_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    
    # Допустимые значения
    VALID_PAYMENT_METHODS = ['card_ru', 'card_foreign', 'crypto']
    VALID_STATUSES = ['pending', 'completed', 'failed', 'expired', 'cancelled']
    VALID_CURRENCIES = ['RUB', 'USD', 'EUR', 'BTC', 'ETH', 'USDT']
    
    def __post_init__(self):
        """Автоматическая валидация после создания объекта"""
        # Преобразуем amount в Decimal если это не так
        if not isinstance(self.amount, Decimal):
            try:
                self.amount = Decimal(str(self.amount))
            except (InvalidOperation, ValueError):
                raise ValidationError("amount должен быть числом")
        
        # Устанавливаем дату истечения по умолчанию (24 часа)
        if self.expires_at is None and self.created_at:
            self.expires_at = self.created_at + timedelta(hours=24)
        elif self.expires_at is None:
            self.expires_at = datetime.now() + timedelta(hours=24)
        
        self.validate()
    
    def validate(self) -> bool:
        """Валидация данных платежа"""
        errors = []
        
        # Валидация user_id
        if not isinstance(self.user_id, int) or self.user_id <= 0:
            errors.append("user_id должен быть положительным целым числом")
        
        # Валидация lava_invoice_id
        if not isinstance(self.lava_invoice_id, str):
            errors.append("lava_invoice_id должен быть строкой")
        elif len(self.lava_invoice_id.strip()) == 0:
            errors.append("lava_invoice_id не может быть пустым")
        elif len(self.lava_invoice_id) > 255:
            errors.append("lava_invoice_id не может быть длиннее 255 символов")
        
        # Валидация amount
        if not isinstance(self.amount, Decimal):
            errors.append("amount должен быть объектом Decimal")
        elif self.amount <= 0:
            errors.append("amount должен быть положительным числом")
        elif self.amount > Decimal('999999.99'):
            errors.append("amount не может превышать 999999.99")
        
        # Валидация currency
        if not isinstance(self.currency, str):
            errors.append("currency должен быть строкой")
        elif self.currency not in self.VALID_CURRENCIES:
            errors.append(f"currency должен быть одним из: {', '.join(self.VALID_CURRENCIES)}")
        
        # Валидация payment_method
        if not isinstance(self.payment_method, str):
            errors.append("payment_method должен быть строкой")
        elif self.payment_method not in self.VALID_PAYMENT_METHODS:
            errors.append(f"payment_method должен быть одним из: {', '.join(self.VALID_PAYMENT_METHODS)}")
        
        # Валидация status
        if not isinstance(self.status, str):
            errors.append("status должен быть строкой")
        elif self.status not in self.VALID_STATUSES:
            errors.append(f"status должен быть одним из: {', '.join(self.VALID_STATUSES)}")
        
        # Валидация payment_url (если указан)
        if self.payment_url is not None:
            if not isinstance(self.payment_url, str):
                errors.append("payment_url должен быть строкой")
            elif len(self.payment_url.strip()) == 0:
                errors.append("payment_url не может быть пустым")
            elif not self._is_valid_url(self.payment_url):
                errors.append("payment_url должен быть валидным URL")
        
        # Валидация дат
        if self.created_at is not None and not isinstance(self.created_at, datetime):
            errors.append("created_at должен быть объектом datetime")
        
        if self.completed_at is not None and not isinstance(self.completed_at, datetime):
            errors.append("completed_at должен быть объектом datetime")
        
        if self.expires_at is not None and not isinstance(self.expires_at, datetime):
            errors.append("expires_at должен быть объектом datetime")
        
        # Логическая валидация дат
        if (self.created_at and self.completed_at and 
            self.completed_at < self.created_at):
            errors.append("completed_at не может быть раньше created_at")
        
        if (self.created_at and self.expires_at and 
            self.expires_at < self.created_at):
            errors.append("expires_at не может быть раньше created_at")
        
        if errors:
            raise ValidationError(f"Ошибки валидации Payment: {'; '.join(errors)}")
        
        return True
    
    def _is_valid_url(self, url: str) -> bool:
        """Проверить валидность URL"""
        url_pattern = re.compile(
            r'^https?://'  # http:// или https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # домен
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
            r'(?::\d+)?'  # порт
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
    
    def is_expired(self) -> bool:
        """Проверить, истек ли платеж"""
        return (self.expires_at and 
                self.expires_at <= datetime.now() and
                self.status == 'pending')
    
    def is_completed(self) -> bool:
        """Проверить, завершен ли платеж"""
        return self.status == 'completed'
    
    def is_pending(self) -> bool:
        """Проверить, ожидает ли платеж обработки"""
        return self.status == 'pending'
    
    def mark_completed(self, subscription_id: Optional[int] = None) -> None:
        """Отметить платеж как завершенный"""
        self.status = 'completed'
        self.completed_at = datetime.now()
        if subscription_id:
            self.subscription_id = subscription_id
    
    def mark_failed(self) -> None:
        """Отметить платеж как неудачный"""
        self.status = 'failed'
    
    def mark_expired(self) -> None:
        """Отметить платеж как истекший"""
        self.status = 'expired'
    
    def get_payment_method_display(self) -> str:
        """Получить отображаемое название способа оплаты"""
        method_names = {
            'card_ru': 'Карта РФ',
            'card_foreign': 'Карта иностранного банка',
            'crypto': 'Криптовалюта'
        }
        return method_names.get(self.payment_method, self.payment_method)
    
    def to_dict(self) -> Dict[str, Any]:
        """Преобразовать в словарь"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'subscription_id': self.subscription_id,
            'lava_invoice_id': self.lava_invoice_id,
            'amount': str(self.amount),
            'currency': self.currency,
            'payment_method': self.payment_method,
            'payment_method_display': self.get_payment_method_display(),
            'status': self.status,
            'payment_url': self.payment_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_expired': self.is_expired(),
            'is_completed': self.is_completed(),
            'is_pending': self.is_pending()
        }

@dataclass
class AdminLog:
    """Модель лога администратора с валидацией"""
    id: Optional[int] = None
    admin_telegram_id: int = 0
    action: str = ""
    target_user_id: Optional[int] = None
    details: Optional[str] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Автоматическая валидация после создания объекта"""
        self.validate()
    
    def validate(self) -> bool:
        """Валидация данных лога администратора"""
        errors = []
        
        # Валидация admin_telegram_id
        if not isinstance(self.admin_telegram_id, int) or self.admin_telegram_id <= 0:
            errors.append("admin_telegram_id должен быть положительным целым числом")
        
        # Валидация action
        if not isinstance(self.action, str):
            errors.append("action должен быть строкой")
        elif len(self.action.strip()) == 0:
            errors.append("action не может быть пустым")
        elif len(self.action) > 255:
            errors.append("action не может быть длиннее 255 символов")
        
        # Валидация target_user_id (если указан)
        if self.target_user_id is not None:
            if not isinstance(self.target_user_id, int) or self.target_user_id <= 0:
                errors.append("target_user_id должен быть положительным целым числом")
        
        # Валидация details (если указаны)
        if self.details is not None:
            if not isinstance(self.details, str):
                errors.append("details должен быть строкой")
            elif len(self.details) > 1000:
                errors.append("details не может быть длиннее 1000 символов")
        
        # Валидация created_at
        if self.created_at is not None and not isinstance(self.created_at, datetime):
            errors.append("created_at должен быть объектом datetime")
        
        if errors:
            raise ValidationError(f"Ошибки валидации AdminLog: {'; '.join(errors)}")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Преобразовать в словарь"""
        return {
            'id': self.id,
            'admin_telegram_id': self.admin_telegram_id,
            'action': self.action,
            'target_user_id': self.target_user_id,
            'details': self.details,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


# Утилитарные функции для создания моделей

def create_user_from_telegram(telegram_user) -> User:
    """Создать модель User из объекта Telegram пользователя"""
    return User(
        telegram_id=telegram_user.id,
        username=getattr(telegram_user, 'username', None),
        first_name=getattr(telegram_user, 'first_name', None),
        last_name=getattr(telegram_user, 'last_name', None),
        created_at=datetime.now()
    )

def create_subscription_for_plan(user_id: int, plan_type: str) -> Subscription:
    """Создать подписку для указанного плана"""
    return Subscription(
        user_id=user_id,
        plan_type=plan_type,
        status='pending',
        start_date=datetime.now()
    )

def create_payment_for_subscription(user_id: int, lava_invoice_id: str, 
                                  amount: Decimal, payment_method: str,
                                  payment_url: Optional[str] = None) -> Payment:
    """Создать платеж для подписки"""
    return Payment(
        user_id=user_id,
        lava_invoice_id=lava_invoice_id,
        amount=amount,
        payment_method=payment_method,
        payment_url=payment_url,
        created_at=datetime.now()
    )