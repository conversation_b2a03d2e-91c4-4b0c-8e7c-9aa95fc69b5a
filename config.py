import os
import logging
from dotenv import load_dotenv

# Загружаем переменные окружения из .env файла
load_dotenv('.env')

class Config:
    # Основные настройки
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    FLASK_ENV = os.environ.get('FLASK_ENV', 'development')
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    TESTING = os.environ.get('TESTING', 'False').lower() == 'true'
    
    # Telegram Bot настройки
    TELEGRAM_BOT_TOKEN = os.environ.get('TELEGRAM_BOT_TOKEN')
    TELEGRAM_API_TIMEOUT = int(os.environ.get('TELEGRAM_API_TIMEOUT', '30'))
    TELEGRAM_API_RETRIES = int(os.environ.get('TELEGRAM_API_RETRIES', '3'))
    
    # Административные настройки
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123'
    ADMIN_USER_IDS = os.environ.get('ADMIN_USER_IDS', '').split(',')
    ADMIN_USER_IDS = [int(uid.strip()) for uid in ADMIN_USER_IDS if uid.strip().isdigit()]
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL')
    
    # Настройки базы данных
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///payments.db'
    
    # Настройки платежей
    PAYMENT_PROVIDER_TOKEN = os.environ.get('PAYMENT_PROVIDER_TOKEN')
    CURRENCY = os.environ.get('CURRENCY', 'RUB')
    
    # Настройки Lava.top API
    LAVA_API_KEY = os.environ.get('LAVA_API_KEY')
    LAVA_SECRET_KEY = os.environ.get('LAVA_SECRET_KEY')
    LAVA_BASE_URL = os.environ.get('LAVA_BASE_URL') or 'https://api.lava.top'
    LAVA_API_TIMEOUT = int(os.environ.get('LAVA_API_TIMEOUT', '30'))
    LAVA_API_RETRIES = int(os.environ.get('LAVA_API_RETRIES', '3'))
    WEBHOOK_URL = os.environ.get('WEBHOOK_URL')
    
    # Настройки уведомлений
    NOTIFICATION_CHAT_ID = os.environ.get('NOTIFICATION_CHAT_ID')
    
    # Настройки канала
    CHANNEL_ID = os.environ.get('CHANNEL_ID')
    INVITE_LINK_EXPIRE_HOURS = int(os.environ.get('INVITE_LINK_EXPIRE_HOURS', '24'))
    MAX_INVITE_USES = int(os.environ.get('MAX_INVITE_USES', '1'))
    
    # Настройки тарифных планов
    PLAN_1_MONTHS = int(os.environ.get('PLAN_1_MONTHS', '1'))
    PLAN_1_PRICE = int(os.environ.get('PLAN_1_PRICE', '299'))
    PLAN_3_MONTHS = int(os.environ.get('PLAN_3_MONTHS', '3'))
    PLAN_3_PRICE = int(os.environ.get('PLAN_3_PRICE', '799'))
    PLAN_6_MONTHS = int(os.environ.get('PLAN_6_MONTHS', '6'))
    PLAN_6_PRICE = int(os.environ.get('PLAN_6_PRICE', '1499'))
    PLAN_12_MONTHS = int(os.environ.get('PLAN_12_MONTHS', '12'))
    PLAN_12_PRICE = int(os.environ.get('PLAN_12_PRICE', '2799'))
    
    # Способы оплаты
    PAYMENT_METHODS = os.environ.get('PAYMENT_METHODS', 'ru_card,foreign_card,crypto').split(',')
    
    # Настройки логирования
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'bot.log')
    ERROR_LOG_FILE = os.environ.get('ERROR_LOG_FILE', 'error.log')
    LOG_MAX_BYTES = int(os.environ.get('LOG_MAX_BYTES', '10485760'))  # 10MB
    LOG_BACKUP_COUNT = int(os.environ.get('LOG_BACKUP_COUNT', '5'))
    
    # Настройки безопасности
    RATE_LIMIT_ENABLED = os.environ.get('RATE_LIMIT_ENABLED', 'true').lower() == 'true'
    RATE_LIMIT_PER_MINUTE = int(os.environ.get('RATE_LIMIT_PER_MINUTE', '60'))
    RATE_LIMIT_PER_HOUR = int(os.environ.get('RATE_LIMIT_PER_HOUR', '1000'))
    WEBHOOK_IP_WHITELIST = os.environ.get('WEBHOOK_IP_WHITELIST', '').split(',') if os.environ.get('WEBHOOK_IP_WHITELIST') else []
    
    # Настройки сессий
    # ВАЖНО: SESSION_COOKIE_SECURE=true требует HTTPS. Для HTTP (разработка) установите false
    # Для production с HTTPS оставьте true для безопасности
    SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'false').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = os.environ.get('SESSION_COOKIE_HTTPONLY', 'true').lower() == 'true'
    SESSION_COOKIE_SAMESITE = os.environ.get('SESSION_COOKIE_SAMESITE', 'Lax')
    PERMANENT_SESSION_LIFETIME = int(os.environ.get('PERMANENT_SESSION_LIFETIME', '3600'))
    
    # Настройки мониторинга
    HEALTH_CHECK_ENABLED = os.environ.get('HEALTH_CHECK_ENABLED', 'true').lower() == 'true'
    HEALTH_CHECK_PATH = os.environ.get('HEALTH_CHECK_PATH', '/health')
    METRICS_ENABLED = os.environ.get('METRICS_ENABLED', 'true').lower() == 'true'
    METRICS_PATH = os.environ.get('METRICS_PATH', '/metrics')
    
    # Настройки резервного копирования
    BACKUP_ENABLED = os.environ.get('BACKUP_ENABLED', 'true').lower() == 'true'
    BACKUP_INTERVAL_HOURS = int(os.environ.get('BACKUP_INTERVAL_HOURS', '24'))
    BACKUP_RETENTION_DAYS = int(os.environ.get('BACKUP_RETENTION_DAYS', '30'))
    BACKUP_PATH = os.environ.get('BACKUP_PATH', './backups')
    
    # Настройки кэширования
    CACHE_TYPE = os.environ.get('CACHE_TYPE', 'simple')
    CACHE_DEFAULT_TIMEOUT = int(os.environ.get('CACHE_DEFAULT_TIMEOUT', '300'))
    REDIS_URL = os.environ.get('REDIS_URL')
    
    # Настройки планировщика
    SCHEDULER_ENABLED = os.environ.get('SCHEDULER_ENABLED', 'true').lower() == 'true'
    SCHEDULER_TIMEZONE = os.environ.get('SCHEDULER_TIMEZONE', 'Europe/Moscow')
    CHECK_SUBSCRIPTIONS_INTERVAL = int(os.environ.get('CHECK_SUBSCRIPTIONS_INTERVAL', '60'))
    SEND_NOTIFICATIONS_INTERVAL = int(os.environ.get('SEND_NOTIFICATIONS_INTERVAL', '1440'))
    CLEANUP_PAYMENTS_INTERVAL = int(os.environ.get('CLEANUP_PAYMENTS_INTERVAL', '1440'))
    BACKUP_INTERVAL = int(os.environ.get('BACKUP_INTERVAL', '1440'))
    
    # Email настройки
    SMTP_SERVER = os.environ.get('SMTP_SERVER')
    SMTP_PORT = int(os.environ.get('SMTP_PORT', '587'))
    SMTP_USERNAME = os.environ.get('SMTP_USERNAME')
    SMTP_PASSWORD = os.environ.get('SMTP_PASSWORD')
    SMTP_USE_TLS = os.environ.get('SMTP_USE_TLS', 'true').lower() == 'true'
    
    # Локализация
    LANGUAGE = os.environ.get('LANGUAGE', 'ru')
    TIMEZONE = os.environ.get('TZ', 'Europe/Moscow')
    
    # Sentry (опционально)
    SENTRY_DSN = os.environ.get('SENTRY_DSN')
    
    # AWS S3 (для резервного копирования)
    AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
    AWS_S3_BUCKET = os.environ.get('AWS_S3_BUCKET')
    AWS_S3_REGION = os.environ.get('AWS_S3_REGION')
    
    @staticmethod
    def validate_config():
        """Проверяет наличие обязательных переменных окружения"""
        required_vars = ['TELEGRAM_BOT_TOKEN', 'LAVA_API_KEY', 'LAVA_SECRET_KEY', 'WEBHOOK_URL']
        missing_vars = []
        
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Отсутствуют обязательные переменные окружения: {', '.join(missing_vars)}")
        
        return True
    
    @staticmethod
    def setup_logging():
        """Настройка системы логирования для production"""
        log_level = getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO)
        
        # Создаем форматтер
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Настраиваем основной логгер
        logger = logging.getLogger()
        logger.setLevel(log_level)
        
        # Удаляем существующие обработчики
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Файловый обработчик с ротацией
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            Config.LOG_FILE,
            maxBytes=Config.LOG_MAX_BYTES,
            backupCount=Config.LOG_BACKUP_COUNT
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(log_level)
        logger.addHandler(file_handler)
        
        # Обработчик ошибок
        error_handler = RotatingFileHandler(
            Config.ERROR_LOG_FILE,
            maxBytes=Config.LOG_MAX_BYTES,
            backupCount=Config.LOG_BACKUP_COUNT
        )
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        logger.addHandler(error_handler)
        
        # Консольный обработчик (только для development)
        if Config.FLASK_ENV != 'production':
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            console_handler.setLevel(log_level)
            logger.addHandler(console_handler)
        
        return logger
    
    @staticmethod
    def get_subscription_plans():
        """Возвращает конфигурацию тарифных планов"""
        return {
            '1': {
                'months': Config.PLAN_1_MONTHS,
                'price': Config.PLAN_1_PRICE,
                'name': f'{Config.PLAN_1_MONTHS} месяц'
            },
            '3': {
                'months': Config.PLAN_3_MONTHS,
                'price': Config.PLAN_3_PRICE,
                'name': f'{Config.PLAN_3_MONTHS} месяца'
            },
            '6': {
                'months': Config.PLAN_6_MONTHS,
                'price': Config.PLAN_6_PRICE,
                'name': f'{Config.PLAN_6_MONTHS} месяцев'
            },
            '12': {
                'months': Config.PLAN_12_MONTHS,
                'price': Config.PLAN_12_PRICE,
                'name': f'{Config.PLAN_12_MONTHS} месяцев'
            }
        }
    
    @staticmethod
    def get_payment_methods_config():
        """Возвращает конфигурацию способов оплаты"""
        return {
            'ru_card': {
                'name': 'Карта РФ',
                'service': 'card_ru',
                'enabled': 'ru_card' in Config.PAYMENT_METHODS
            },
            'foreign_card': {
                'name': 'Карта иностранного банка',
                'service': 'card_intl',
                'enabled': 'foreign_card' in Config.PAYMENT_METHODS
            },
            'crypto': {
                'name': 'Криптовалюта',
                'service': 'crypto',
                'enabled': 'crypto' in Config.PAYMENT_METHODS
            }
        }
    
    @staticmethod
    def is_production():
        """Проверяет, запущено ли приложение в production режиме"""
        return Config.FLASK_ENV == 'production'
    
    @staticmethod
    def get_system_info():
        """Возвращает информацию о системе для мониторинга"""
        import platform
        import sys
        
        return {
            'environment': Config.FLASK_ENV,
            'python_version': sys.version,
            'platform': platform.platform(),
            'debug_mode': Config.DEBUG,
            'scheduler_enabled': Config.SCHEDULER_ENABLED,
            'backup_enabled': Config.BACKUP_ENABLED,
            'rate_limit_enabled': Config.RATE_LIMIT_ENABLED,
            'health_check_enabled': Config.HEALTH_CHECK_ENABLED,
            'metrics_enabled': Config.METRICS_ENABLED
        }