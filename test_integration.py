#!/usr/bin/env python3
"""
Простой тест интеграции для проверки запуска приложения
"""

import os
import sys
import time
import threading
from unittest.mock import patch, MagicMock

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_app_integration():
    """Тест интеграции основных компонентов приложения"""
    
    # Мокаем переменные окружения для тестирования
    test_env = {
        'TELEGRAM_BOT_TOKEN': 'test_bot_token',
        'LAVA_API_KEY': 'test_lava_key',
        'LAVA_SECRET_KEY': 'test_lava_secret',
        'WEBHOOK_URL': 'https://test.example.com/webhook',
        'CHANNEL_ID': '@test_channel'
    }
    
    with patch.dict(os.environ, test_env):
        try:
            # Импортируем после установки переменных окружения
            import importlib.util
            spec = importlib.util.spec_from_file_location("app_module", "app.py")
            app_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(app_module)
            
            create_app = app_module.create_app
            bot_handler = getattr(app_module, 'bot_handler', None)
            scheduler_service = getattr(app_module, 'scheduler_service', None)
            
            print("✅ Импорт модулей успешен")
            
            # Создаем приложение
            app = create_app()
            print("✅ Flask приложение создано")
            
            # Проверяем, что сервисы инициализированы
            if bot_handler:
                print("✅ TelegramBotHandler инициализирован")
            else:
                print("❌ TelegramBotHandler не инициализирован")
                
            if scheduler_service:
                print("✅ SchedulerService инициализирован")
            else:
                print("❌ SchedulerService не инициализирован")
            
            # Проверяем маршруты
            with app.test_client() as client:
                # Тест health endpoint
                response = client.get('/health')
                print(f"✅ Health endpoint: {response.status_code}")
                
                # Тест webhook test endpoint
                response = client.get('/webhook/test')
                print(f"✅ Webhook test endpoint: {response.status_code}")
                
                # Тест страниц оплаты
                response = client.get('/success')
                print(f"✅ Success page: {response.status_code}")
                
                response = client.get('/fail')
                print(f"✅ Fail page: {response.status_code}")
            
            print("\n🎉 Интеграция прошла успешно!")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка интеграции: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🔄 Запуск теста интеграции...")
    success = test_app_integration()
    sys.exit(0 if success else 1)