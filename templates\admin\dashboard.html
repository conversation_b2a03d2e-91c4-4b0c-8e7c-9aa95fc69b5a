{% extends "base.html" %}

{% block title %}Панель управления{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="pt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active">
            <i class="bi bi-house"></i> Панель управления
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-speedometer2"></i> Панель управления
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Обновить
            </button>
        </div>
        <button type="button" class="btn btn-sm btn-primary" onclick="runCleanupTasks()">
            <i class="bi bi-gear"></i> Выполнить очистку
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Всего пользователей
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_users if stats and stats.total_users else 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people text-primary" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Активные подписки
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.active_subscriptions if stats and stats.active_subscriptions else 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Общий доход
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ "%.2f"|format(payment_stats.total_revenue if payment_stats and payment_stats.total_revenue else 0) }} ₽
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar text-info" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Истекают сегодня
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.expiring_today if stats and stats.expiring_today else 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Tables Row -->
<div class="row">
    <!-- Revenue Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bar-chart"></i> Доходы за последние 30 дней
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Types Pie Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-pie-chart"></i> Типы подписок
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="subscriptionChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="mr-2">
                        <i class="bi bi-circle-fill text-primary"></i> Месячные ({{ stats.monthly_subs if stats and stats.monthly_subs else 0 }})
                    </span>
                    <span class="mr-2">
                        <i class="bi bi-circle-fill text-success"></i> Квартальные ({{ stats.quarterly_subs if stats and stats.quarterly_subs else 0 }})
                    </span>
                    <span class="mr-2">
                        <i class="bi bi-circle-fill text-info"></i> Полугодовые ({{ stats.half_yearly_subs if stats and stats.half_yearly_subs else 0 }})
                    </span>
                    <span class="mr-2">
                        <i class="bi bi-circle-fill text-warning"></i> Годовые ({{ stats.yearly_subs if stats and stats.yearly_subs else 0 }})
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history"></i> Последние платежи
                </h6>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Пользователь</th>
                                    <th>Сумма</th>
                                    <th>Статус</th>
                                    <th>Дата</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in recent_payments %}
                                <tr>
                                    <td>{{ payment.user_id }}</td>
                                    <td>{{ "%.2f"|format(payment.amount) }} ₽</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if payment.status == 'completed' else 'warning' if payment.status == 'pending' else 'danger' }}">
                                            {{ payment.status }}
                                        </span>
                                    </td>
                                    <td>{{ payment.created_at.strftime('%d.%m %H:%M') if payment.created_at else '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Нет недавних платежей</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-exclamation-circle"></i> Требуют внимания
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% if stats and stats.expiring_today and stats.expiring_today > 0 %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-calendar-x text-danger"></i>
                            Подписки истекают сегодня
                        </div>
                        <span class="badge bg-danger rounded-pill">{{ stats.expiring_today }}</span>
                    </div>
                    {% endif %}
                    
                    {% if stats and stats.expiring_tomorrow and stats.expiring_tomorrow > 0 %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-calendar-event text-warning"></i>
                            Подписки истекают завтра
                        </div>
                        <span class="badge bg-warning rounded-pill">{{ stats.expiring_tomorrow }}</span>
                    </div>
                    {% endif %}
                    
                    {% if payment_stats and payment_stats.pending_payments and payment_stats.pending_payments > 0 %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-clock text-info"></i>
                            Ожидающие платежи
                        </div>
                        <span class="badge bg-info rounded-pill">{{ payment_stats.pending_payments }}</span>
                    </div>
                    {% endif %}
                    
                    {% if payment_stats and payment_stats.failed_payments and payment_stats.failed_payments > 0 %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-x-circle text-danger"></i>
                            Неудачные платежи
                        </div>
                        <span class="badge bg-danger rounded-pill">{{ payment_stats.failed_payments }}</span>
                    </div>
                    {% endif %}
                    
                    {% if (not stats or not stats.expiring_today or stats.expiring_today == 0) and (not stats or not stats.expiring_tomorrow or stats.expiring_tomorrow == 0) and (not payment_stats or not payment_stats.pending_payments or payment_stats.pending_payments == 0) and (not payment_stats or not payment_stats.failed_payments or payment_stats.failed_payments == 0) %}
                    <div class="list-group-item">
                        <i class="bi bi-check-circle text-success"></i>
                        Все в порядке! Нет проблем, требующих внимания.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-activity"></i> Статус системы
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="bi bi-database text-success" style="font-size: 2rem;"></i>
                        </div>
                        <h6>База данных</h6>
                        <span class="badge bg-success">Подключена</span>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="bi bi-robot text-success" style="font-size: 2rem;"></i>
                        </div>
                        <h6>Telegram бот</h6>
                        <span class="badge bg-success">Активен</span>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="bi bi-clock text-success" style="font-size: 2rem;"></i>
                        </div>
                        <h6>Планировщик</h6>
                        <span class="badge bg-success">Работает</span>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="bi bi-credit-card text-success" style="font-size: 2rem;"></i>
                        </div>
                        <h6>Платежи</h6>
                        <span class="badge bg-success">Доступны</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .text-xs {
        font-size: 0.7rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: {{ revenue_labels|safe }},
        datasets: [{
            label: 'Доходы (₽)',
            data: {{ revenue_data|safe }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Subscription Types Chart
const subscriptionCtx = document.getElementById('subscriptionChart').getContext('2d');
const subscriptionChart = new Chart(subscriptionCtx, {
    type: 'doughnut',
    data: {
        labels: ['Месячные', 'Квартальные', 'Полугодовые', 'Годовые'],
        datasets: [{
            data: [{{ stats.monthly_subs if stats and stats.monthly_subs else 0 }}, {{ stats.quarterly_subs if stats and stats.quarterly_subs else 0 }}, {{ stats.half_yearly_subs if stats and stats.half_yearly_subs else 0 }}, {{ stats.yearly_subs if stats and stats.yearly_subs else 0 }}],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Cleanup Tasks Function
function runCleanupTasks() {
    if (confirm('Запустить задачи очистки данных? Это может занять некоторое время.')) {
        fetch('/admin/cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Задачи очистки выполнены успешно!');
                location.reload();
            } else {
                alert('Ошибка выполнения задач очистки: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}
</script>
{% endblock %}