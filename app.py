#!/usr/bin/env python3
"""
Flask приложение для Telegram Payment Bot
"""

import logging
import threading
import signal
import sys
from datetime import datetime
from flask import Flask, request, jsonify, redirect, url_for
from config import Config

from app.services.payment_service import PaymentService
from app.services.webhook_handler import WebhookHandler
from app.services.bot_handler import TelegramBotHandler
from app.services.scheduler_service import SchedulerService
from app.services.channel_manager import ChannelManager
from app.services.notification_service import NotificationService
from app.models.database import DatabaseService
from app.admin import admin_bp, init_admin_services

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Глобальные переменные для сервисов
bot_handler = None
scheduler_service = None
bot_thread = None

def create_app():
    """Создает и настраивает Flask приложение"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Инициализация сервисов
    try:
        db_service = DatabaseService()
        payment_service = PaymentService()
        channel_manager = ChannelManager()
        notification_service = NotificationService(None, db_service)  # Will be updated after bot_handler is created
        webhook_handler = WebhookHandler(payment_service, notification_service, db_service)
        
        # Инициализация глобальных сервисов
        global bot_handler, scheduler_service
        bot_handler = TelegramBotHandler(db_service, payment_service, channel_manager)
        notification_service = NotificationService(bot_handler, db_service)
        scheduler_service = SchedulerService(db_service, notification_service, channel_manager)
        
        # Инициализация админ панели
        init_admin_services(db_service, scheduler_service)
        app.register_blueprint(admin_bp)
        
        logger.info("Сервисы инициализированы успешно")
        
    except Exception as e:
        logger.error(f"Ошибка инициализации сервисов: {str(e)}")
        raise
    
    # Главный маршрут - перенаправление на админ-панель
    @app.route('/')
    def index():
        """Главная страница - перенаправление на админ-панель"""
        return redirect(url_for('admin.dashboard'))
    
    # Маршруты для webhook
    @app.route('/webhook', methods=['POST'])
    def lava_webhook():
        """Endpoint для получения webhook от Lava.top"""
        try:
            result = webhook_handler.handle_lava_webhook()
            
            status_code = result.get('status_code', 200)
            response_data = {
                'success': result.get('success', False),
                'message': result.get('message', result.get('error', 'Unknown'))
            }
            
            if result.get('data'):
                response_data['data'] = result['data']
            
            return jsonify(response_data), status_code
            
        except Exception as e:
            logger.error(f"Критическая ошибка в webhook endpoint: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'Internal server error'
            }), 500
    
    @app.route('/webhook/test', methods=['GET'])
    def webhook_test():
        """Тестовый endpoint для проверки работы webhook"""
        return jsonify({
            'success': True,
            'message': 'Webhook endpoint is working',
            'stats': webhook_handler.get_webhook_stats()
        })
    
    # Маршруты для успешной и неудачной оплаты (редиректы от Lava.top)
    @app.route('/success')
    def payment_success():
        """Страница успешной оплаты"""
        return """
        <html>
        <head>
            <title>Оплата успешна</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>Оплата прошла успешно!</h1>
            <p>Спасибо за покупку подписки. Вы получите пригласительную ссылку в Telegram боте в течение нескольких минут.</p>
            <p>Вы можете закрыть эту страницу.</p>
        </body>
        </html>
        """
    
    @app.route('/fail')
    def payment_fail():
        """Страница неудачной оплаты"""
        return """
        <html>
        <head>
            <title>Ошибка оплаты</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>Ошибка оплаты</h1>
            <p>К сожалению, оплата не прошла. Пожалуйста, попробуйте еще раз или обратитесь в поддержку.</p>
            <p>Вы можете вернуться в Telegram бот и создать новый счет для оплаты.</p>
        </body>
        </html>
        """
    
    # Маршруты для мониторинга и метрик
    @app.route('/health')
    def health_check():
        """Проверка состояния приложения"""
        try:
            if Config.HEALTH_CHECK_ENABLED:
                from app.utils.monitoring import get_health_checker
                health_checker = get_health_checker()
                return jsonify(health_checker.perform_health_check())
            else:
                # Базовая проверка здоровья
                db_status = db_service.check_connection()
                scheduler_status = scheduler_service.get_scheduler_status() if scheduler_service else {'running': False}
                
                return jsonify({
                    'status': 'healthy',
                    'database': 'connected' if db_status else 'disconnected',
                    'services': {
                        'payment_service': 'initialized',
                        'webhook_handler': 'initialized',
                        'database_service': 'initialized',
                        'bot_handler': 'initialized' if bot_handler else 'not_initialized',
                        'scheduler_service': 'running' if scheduler_status.get('running') else 'stopped'
                    },
                    'scheduler': scheduler_status
                })
            
        except Exception as e:
            logger.error(f"Ошибка проверки здоровья: {str(e)}")
            return jsonify({
                'status': 'unhealthy',
                'error': str(e)
            }), 500
    
    @app.route('/metrics')
    def metrics():
        """Endpoint для получения метрик системы"""
        try:
            if not Config.METRICS_ENABLED:
                return jsonify({'error': 'Metrics disabled'}), 404
            
            from app.utils.monitoring import get_metrics_collector
            metrics_collector = get_metrics_collector()
            
            # Обновляем метрики из БД
            metrics_collector.update_database_metrics()
            
            system_metrics = metrics_collector.get_system_metrics()
            app_metrics = metrics_collector.get_application_metrics()
            
            return jsonify({
                'timestamp': datetime.now().isoformat(),
                'system': {
                    'cpu_percent': system_metrics.cpu_percent,
                    'memory_percent': system_metrics.memory_percent,
                    'disk_percent': system_metrics.disk_percent,
                    'active_connections': system_metrics.active_connections,
                    'uptime_seconds': system_metrics.uptime_seconds
                },
                'application': {
                    'total_users': app_metrics.total_users,
                    'active_subscriptions': app_metrics.active_subscriptions,
                    'pending_payments': app_metrics.pending_payments,
                    'completed_payments': app_metrics.completed_payments,
                    'failed_payments': app_metrics.failed_payments,
                    'webhook_requests': app_metrics.webhook_requests,
                    'api_errors': app_metrics.api_errors,
                    'bot_messages_sent': app_metrics.bot_messages_sent,
                    'bot_messages_received': app_metrics.bot_messages_received
                }
            })
            
        except Exception as e:
            logger.error(f"Ошибка получения метрик: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/status')
    def system_status():
        """Подробная информация о статусе системы"""
        try:
            from app.utils.monitoring import get_metrics_collector
            metrics_collector = get_metrics_collector()
            
            health_status = metrics_collector.get_health_status()
            system_info = Config.get_system_info()
            
            return jsonify({
                'health': health_status,
                'system_info': system_info,
                'configuration': {
                    'environment': Config.FLASK_ENV,
                    'debug': Config.DEBUG,
                    'scheduler_enabled': Config.SCHEDULER_ENABLED,
                    'backup_enabled': Config.BACKUP_ENABLED,
                    'rate_limit_enabled': Config.RATE_LIMIT_ENABLED
                }
            })
            
        except Exception as e:
            logger.error(f"Ошибка получения статуса системы: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    # Обработчик ошибок
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'success': False,
            'message': 'Endpoint not found'
        }), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return jsonify({
            'success': False,
            'message': 'Method not allowed'
        }), 405
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"Внутренняя ошибка сервера: {str(error)}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500
    
    return app

def start_bot_thread():
    """Запускает Telegram бота в отдельном потоке"""
    global bot_thread
    try:
        if bot_handler:
            logger.info("Запуск Telegram бота в отдельном потоке...")
            bot_thread = threading.Thread(target=bot_handler.start_polling, daemon=True)
            bot_thread.start()
            logger.info("Telegram бот запущен в отдельном потоке")
        else:
            logger.error("bot_handler не инициализирован")
    except Exception as e:
        logger.error(f"Ошибка запуска бота: {str(e)}")
        raise

def start_scheduler():
    """Запускает планировщик задач"""
    try:
        if scheduler_service:
            logger.info("Запуск планировщика задач...")
            scheduler_service.start()
            logger.info("Планировщик задач запущен")
        else:
            logger.error("scheduler_service не инициализирован")
    except Exception as e:
        logger.error(f"Ошибка запуска планировщика: {str(e)}")
        raise

def stop_services():
    """Останавливает все сервисы"""
    try:
        logger.info("Остановка сервисов...")
        
        # Останавливаем планировщик
        if scheduler_service:
            scheduler_service.stop()
            logger.info("Планировщик остановлен")
        
        # Останавливаем бота
        if bot_handler:
            bot_handler.stop_polling()
            logger.info("Telegram бот остановлен")
        
        # Ждем завершения потока бота
        if bot_thread and bot_thread.is_alive():
            bot_thread.join(timeout=5)
            logger.info("Поток бота завершен")
            
    except Exception as e:
        logger.error(f"Ошибка остановки сервисов: {str(e)}")

def signal_handler(signum, frame):
    """Обработчик сигналов для корректной остановки"""
    logger.info(f"Получен сигнал {signum}, останавливаем сервисы...")
    stop_services()
    sys.exit(0)

def main():
    """Главная функция запуска приложения"""
    try:
        # Проверяем конфигурацию
        Config.validate_config()
        logger.info("Конфигурация проверена успешно")
        
        # Настраиваем обработчики сигналов для корректной остановки
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Создаем Flask приложение
        app = create_app()
        
        # Запускаем Telegram бота в отдельном потоке
        start_bot_thread()
        
        # Запускаем планировщик задач
        start_scheduler()
        
        # Запускаем Flask приложение
        logger.info("Запуск Flask приложения...")
        logger.info("Все сервисы запущены. Приложение готово к работе!")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False
        )
        
    except ValueError as e:
        logger.error(f"Ошибка конфигурации: {e}")
        stop_services()
        return 1
    except Exception as e:
        logger.error(f"Неожиданная ошибка: {e}")
        stop_services()
        return 1
    finally:
        # Останавливаем сервисы при завершении
        stop_services()
    
    return 0

if __name__ == "__main__":
    exit(main())