"""
Scheduler Service для управления фоновыми задачами
"""

import logging
import functools
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR

from app.models.database import DatabaseService
from app.services.notification_service import NotificationService
from app.services.channel_manager import ChannelManager

logger = logging.getLogger('scheduler')

class SchedulerService:
    """Сервис для управления фоновыми задачами с использованием APScheduler"""
    
    def __init__(self, db_service: DatabaseService, notification_service: NotificationService, 
                 channel_manager: ChannelManager):
        """
        Инициализация сервиса планировщика
        
        Args:
            db_service: Сервис для работы с базой данных
            notification_service: Сервис уведомлений
            channel_manager: Менеджер канала
        """
        self.db_service = db_service
        self.notification_service = notification_service
        self.channel_manager = channel_manager
        
        # Инициализация планировщика
        self.scheduler = BackgroundScheduler()
        self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
        
        # Статистика выполнения задач
        self.job_stats = {
            'executed': 0,
            'errors': 0,
            'last_execution': None,
            'last_error': None
        }
        
        logger.info("SchedulerService инициализирован")
    
    def _job_listener(self, event):
        """Слушатель событий выполнения задач"""
        if event.exception:
            self.job_stats['errors'] += 1
            self.job_stats['last_error'] = datetime.now()
            logger.error(f"Ошибка выполнения задачи {event.job_id}: {event.exception}")
        else:
            self.job_stats['executed'] += 1
            self.job_stats['last_execution'] = datetime.now()
            logger.info(f"Задача {event.job_id} выполнена успешно")
    
    def start(self):
        """Запуск планировщика и добавление всех задач"""
        try:
            # Добавляем все задачи
            self._add_subscription_check_job()
            self._add_expiration_warning_job()
            self._add_expired_cleanup_job()
            
            # Добавляем задачи очистки данных
            self._add_expired_payments_cleanup_job()
            self._add_admin_logs_cleanup_job()
            self._add_database_backup_job()
            
            # Запускаем планировщик
            self.scheduler.start()
            logger.info("Планировщик задач запущен")
            
        except Exception as e:
            logger.error(f"Ошибка запуска планировщика: {e}")
            raise
    
    def stop(self):
        """Остановка планировщика"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown(wait=True)
                logger.info("Планировщик задач остановлен")
        except Exception as e:
            logger.error(f"Ошибка остановки планировщика: {e}")
    
    def _add_subscription_check_job(self):
        """Добавляет задачу проверки истекающих подписок каждый час"""
        self.scheduler.add_job(
            func=self.check_expiring_subscriptions,
            trigger=IntervalTrigger(hours=1),
            id='check_expiring_subscriptions',
            name='Проверка истекающих подписок',
            replace_existing=True,
            max_instances=1
        )
        logger.info("Добавлена задача проверки истекающих подписок (каждый час)")
    
    def _add_expiration_warning_job(self):
        """Добавляет задачу отправки предупреждений об истечении подписки"""
        self.scheduler.add_job(
            func=self.send_expiration_warnings,
            trigger=CronTrigger(hour=10, minute=0),  # Каждый день в 10:00
            id='send_expiration_warnings',
            name='Отправка предупреждений об истечении',
            replace_existing=True,
            max_instances=1
        )
        logger.info("Добавлена задача отправки предупреждений (ежедневно в 10:00)")
    
    def _add_expired_cleanup_job(self):
        """Добавляет задачу очистки истекших подписок"""
        self.scheduler.add_job(
            func=self.cleanup_expired_subscriptions,
            trigger=IntervalTrigger(hours=1),
            id='cleanup_expired_subscriptions',
            name='Очистка истекших подписок',
            replace_existing=True,
            max_instances=1
        )
        logger.info("Добавлена задача очистки истекших подписок (каждый час)")
    
    def check_expiring_subscriptions(self) -> Dict[str, int]:
        """
        Проверяет истекающие подписки и выполняет необходимые действия
        
        Returns:
            Словарь с результатами проверки
        """
        try:
            logger.info("Начинаем проверку истекающих подписок")
            
            results = {
                'checked': 0,
                'expiring_soon': 0,
                'expired': 0,
                'removed_from_channel': 0,
                'errors': 0
            }
            
            # Получаем все активные подписки
            all_active_subscriptions = self.db_service.get_all_active_subscriptions()
            results['checked'] = len(all_active_subscriptions)
            
            current_time = datetime.now()
            
            for subscription in all_active_subscriptions:
                try:
                    # Проверяем, истекла ли подписка
                    if subscription.end_date <= current_time:
                        # Подписка истекла
                        results['expired'] += 1
                        
                        # Обновляем статус подписки
                        self.db_service.update_subscription_status(subscription.id, 'expired')
                        
                        # Получаем пользователя
                        user = self.db_service.get_user_by_id(subscription.user_id)
                        if user:
                            # Удаляем пользователя из канала
                            removed = self.channel_manager.remove_user_from_channel(user.telegram_id)
                            if removed:
                                results['removed_from_channel'] += 1
                                logger.info(f"Пользователь {user.telegram_id} удален из канала (подписка истекла)")
                            else:
                                logger.warning(f"Не удалось удалить пользователя {user.telegram_id} из канала")
                    
                    # Проверяем, истекает ли подписка в ближайшие дни
                    elif subscription.end_date <= current_time + timedelta(days=7):
                        results['expiring_soon'] += 1
                        
                except Exception as e:
                    results['errors'] += 1
                    logger.error(f"Ошибка обработки подписки {subscription.id}: {e}")
            
            logger.info(f"Проверка истекающих подписок завершена: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка проверки истекающих подписок: {e}")
            return {'checked': 0, 'expiring_soon': 0, 'expired': 0, 'removed_from_channel': 0, 'errors': 1}
    
    def send_expiration_warnings(self) -> Dict[str, int]:
        """
        Отправляет предупреждения об истечении подписок
        
        Returns:
            Словарь с результатами отправки
        """
        try:
            logger.info("Начинаем отправку предупреждений об истечении подписок")
            
            # Используем метод из NotificationService
            results = self.notification_service.send_expiry_notifications()
            
            logger.info(f"Отправка предупреждений завершена: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка отправки предупреждений об истечении: {e}")
            return {'warnings_7_days': 0, 'warnings_1_day': 0, 'errors': 1}
    
    def cleanup_expired_subscriptions(self) -> Dict[str, int]:
        """
        Очищает истекшие подписки и удаляет пользователей из канала
        
        Returns:
            Словарь с результатами очистки
        """
        try:
            logger.info("Начинаем очистку истекших подписок")
            
            results = {
                'expired_found': 0,
                'users_removed': 0,
                'subscriptions_updated': 0,
                'errors': 0
            }
            
            # Получаем истекшие подписки
            expired_subscriptions = self.db_service.get_expired_subscriptions()
            results['expired_found'] = len(expired_subscriptions)
            
            for subscription in expired_subscriptions:
                try:
                    # Получаем пользователя
                    user = self.db_service.get_user_by_id(subscription.user_id)
                    if user:
                        # Проверяем, есть ли пользователь в канале
                        is_in_channel = self.channel_manager.check_user_in_channel(user.telegram_id)
                        
                        if is_in_channel:
                            # Удаляем пользователя из канала
                            removed = self.channel_manager.remove_user_from_channel(user.telegram_id)
                            if removed:
                                results['users_removed'] += 1
                                logger.info(f"Пользователь {user.telegram_id} удален из канала")
                        
                        # Обновляем статус подписки, если еще не обновлен
                        if subscription.status == 'active':
                            updated = self.db_service.update_subscription_status(subscription.id, 'expired')
                            if updated:
                                results['subscriptions_updated'] += 1
                
                except Exception as e:
                    results['errors'] += 1
                    logger.error(f"Ошибка очистки подписки {subscription.id}: {e}")
            
            logger.info(f"Очистка истекших подписок завершена: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка очистки истекших подписок: {e}")
            return {'expired_found': 0, 'users_removed': 0, 'subscriptions_updated': 0, 'errors': 1}
    
    def run_manual_check(self) -> Dict[str, Any]:
        """
        Выполняет ручную проверку всех задач
        
        Returns:
            Словарь с результатами всех проверок
        """
        try:
            logger.info("Начинаем ручную проверку всех задач")
            
            results = {
                'timestamp': datetime.now().isoformat(),
                'subscription_check': self.check_expiring_subscriptions(),
                'expiration_warnings': self.send_expiration_warnings(),
                'expired_cleanup': self.cleanup_expired_subscriptions()
            }
            
            logger.info(f"Ручная проверка завершена: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка ручной проверки: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """
        Получает статус планировщика и информацию о задачах
        
        Returns:
            Словарь со статусом планировщика
        """
        try:
            jobs_info = []
            
            if self.scheduler.running:
                for job in self.scheduler.get_jobs():
                    jobs_info.append({
                        'id': job.id,
                        'name': job.name,
                        'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                        'trigger': str(job.trigger)
                    })
            
            status = {
                'running': self.scheduler.running,
                'jobs_count': len(jobs_info),
                'jobs': jobs_info,
                'stats': self.job_stats.copy()
            }
            
            # Конвертируем datetime в строки для JSON сериализации
            if status['stats']['last_execution']:
                status['stats']['last_execution'] = status['stats']['last_execution'].isoformat()
            if status['stats']['last_error']:
                status['stats']['last_error'] = status['stats']['last_error'].isoformat()
            
            return status
            
        except Exception as e:
            logger.error(f"Ошибка получения статуса планировщика: {e}")
            return {
                'running': False,
                'error': str(e)
            }
    
    def add_custom_job(self, func, trigger, job_id: str, name: str, **kwargs) -> bool:
        """
        Добавляет пользовательскую задачу в планировщик
        
        Args:
            func: Функция для выполнения
            trigger: Триггер для задачи
            job_id: Уникальный ID задачи
            name: Название задачи
            **kwargs: Дополнительные параметры для задачи
            
        Returns:
            True если задача добавлена успешно
        """
        try:
            self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                name=name,
                replace_existing=True,
                max_instances=1,
                **kwargs
            )
            
            logger.info(f"Добавлена пользовательская задача: {name} ({job_id})")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка добавления пользовательской задачи {job_id}: {e}")
            return False
    
    def remove_job(self, job_id: str) -> bool:
        """
        Удаляет задачу из планировщика
        
        Args:
            job_id: ID задачи для удаления
            
        Returns:
            True если задача удалена успешно
        """
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Задача {job_id} удалена из планировщика")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка удаления задачи {job_id}: {e}")
            return False
    
    def pause_job(self, job_id: str) -> bool:
        """
        Приостанавливает выполнение задачи
        
        Args:
            job_id: ID задачи для приостановки
            
        Returns:
            True если задача приостановлена успешно
        """
        try:
            self.scheduler.pause_job(job_id)
            logger.info(f"Задача {job_id} приостановлена")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка приостановки задачи {job_id}: {e}")
            return False
    
    def resume_job(self, job_id: str) -> bool:
        """
        Возобновляет выполнение задачи
        
        Args:
            job_id: ID задачи для возобновления
            
        Returns:
            True если задача возобновлена успешно
        """
        try:
            self.scheduler.resume_job(job_id)
            logger.info(f"Задача {job_id} возобновлена")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка возобновления задачи {job_id}: {e}")
            return False
    
    def get_job_stats(self) -> Dict[str, Any]:
        """
        Получает статистику выполнения задач
        
        Returns:
            Словарь со статистикой
        """
        stats = self.job_stats.copy()
        
        # Конвертируем datetime в строки
        if stats['last_execution']:
            stats['last_execution'] = stats['last_execution'].isoformat()
        if stats['last_error']:
            stats['last_error'] = stats['last_error'].isoformat()
        
        return stats 
   
    def _add_expired_payments_cleanup_job(self):
        """Добавляет задачу удаления истекших платежей"""
        self.scheduler.add_job(
            func=self.cleanup_expired_payments,
            trigger=CronTrigger(hour=2, minute=0),  # Каждый день в 2:00
            id='cleanup_expired_payments',
            name='Очистка истекших платежей',
            replace_existing=True,
            max_instances=1
        )
        logger.info("Добавлена задача очистки истекших платежей (ежедневно в 2:00)")
    
    def _add_admin_logs_cleanup_job(self):
        """Добавляет задачу очистки старых логов администратора"""
        self.scheduler.add_job(
            func=self.cleanup_old_admin_logs,
            trigger=CronTrigger(hour=3, minute=0),  # Каждый день в 3:00
            id='cleanup_admin_logs',
            name='Очистка старых логов администратора',
            replace_existing=True,
            max_instances=1
        )
        logger.info("Добавлена задача очистки старых логов администратора (ежедневно в 3:00)")
    
    def _add_database_backup_job(self):
        """Добавляет задачу создания резервных копий базы данных"""
        self.scheduler.add_job(
            func=self.create_database_backup,
            trigger=CronTrigger(hour=4, minute=0),  # Каждый день в 4:00
            id='database_backup',
            name='Создание резервной копии БД',
            replace_existing=True,
            max_instances=1
        )
        logger.info("Добавлена задача создания резервной копии БД (ежедневно в 4:00)") 
   
    def cleanup_expired_payments(self) -> Dict[str, int]:
        """
        Очищает истекшие платежи (старше 30 дней)
        
        Returns:
            Словарь с результатами очистки
        """
        try:
            logger.info("Начинаем очистку истекших платежей")
            
            results = {
                'expired_found': 0,
                'deleted': 0,
                'errors': 0
            }
            
            # Получаем истекшие платежи старше 30 дней
            expired_payments = self.db_service.get_expired_payments_older_than_days(30)
            results['expired_found'] = len(expired_payments)
            
            for payment in expired_payments:
                try:
                    # Удаляем только платежи со статусом 'expired' или 'failed'
                    if payment.status in ['expired', 'failed']:
                        deleted = self.db_service.delete_payment(payment.id)
                        if deleted:
                            results['deleted'] += 1
                            logger.info(f"Удален истекший платеж {payment.id}")
                        
                except Exception as e:
                    results['errors'] += 1
                    logger.error(f"Ошибка удаления платежа {payment.id}: {e}")
            
            logger.info(f"Очистка истекших платежей завершена: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка очистки истекших платежей: {e}")
            return {'expired_found': 0, 'deleted': 0, 'errors': 1}
    
    def cleanup_old_admin_logs(self) -> Dict[str, int]:
        """
        Очищает старые логи администратора (старше 90 дней)
        
        Returns:
            Словарь с результатами очистки
        """
        try:
            logger.info("Начинаем очистку старых логов администратора")
            
            results = {
                'old_logs_found': 0,
                'deleted': 0,
                'errors': 0
            }
            
            # Получаем старые логи (старше 90 дней)
            old_logs = self.db_service.get_admin_logs_older_than_days(90)
            results['old_logs_found'] = len(old_logs)
            
            for log in old_logs:
                try:
                    deleted = self.db_service.delete_admin_log(log.id)
                    if deleted:
                        results['deleted'] += 1
                        logger.info(f"Удален старый лог администратора {log.id}")
                        
                except Exception as e:
                    results['errors'] += 1
                    logger.error(f"Ошибка удаления лога {log.id}: {e}")
            
            logger.info(f"Очистка старых логов администратора завершена: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка очистки старых логов администратора: {e}")
            return {'old_logs_found': 0, 'deleted': 0, 'errors': 1}
    
    def create_database_backup(self) -> Dict[str, Any]:
        """
        Создает резервную копию базы данных
        
        Returns:
            Словарь с результатами создания резервной копии
        """
        try:
            logger.info("Начинаем создание резервной копии базы данных")
            
            import shutil
            import os
            from datetime import datetime
            
            results = {
                'backup_created': False,
                'backup_path': None,
                'backup_size': 0,
                'error': None
            }
            
            # Получаем путь к базе данных
            db_path = self.db_service.db_manager.db_path
            
            if not os.path.exists(db_path):
                results['error'] = f"База данных не найдена: {db_path}"
                logger.error(results['error'])
                return results
            
            # Создаем папку для резервных копий
            backup_dir = "backups"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            # Генерируем имя файла резервной копии
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"payments_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # Создаем резервную копию
            shutil.copy2(db_path, backup_path)
            
            # Проверяем, что файл создан
            if os.path.exists(backup_path):
                results['backup_created'] = True
                results['backup_path'] = backup_path
                results['backup_size'] = os.path.getsize(backup_path)
                
                logger.info(f"Резервная копия создана: {backup_path} ({results['backup_size']} байт)")
                
                # Удаляем старые резервные копии (оставляем только последние 7)
                self._cleanup_old_backups(backup_dir, keep_count=7)
                
            else:
                results['error'] = "Не удалось создать резервную копию"
                logger.error(results['error'])
            
            return results
            
        except Exception as e:
            error_msg = f"Ошибка создания резервной копии: {e}"
            logger.error(error_msg)
            return {
                'backup_created': False,
                'backup_path': None,
                'backup_size': 0,
                'error': error_msg
            }
    
    def _cleanup_old_backups(self, backup_dir: str, keep_count: int = 7):
        """
        Удаляет старые резервные копии, оставляя только последние keep_count
        
        Args:
            backup_dir: Директория с резервными копиями
            keep_count: Количество резервных копий для сохранения
        """
        try:
            import os
            import glob
            
            # Получаем список всех файлов резервных копий
            backup_pattern = os.path.join(backup_dir, "payments_backup_*.db")
            backup_files = glob.glob(backup_pattern)
            
            # Сортируем по времени создания (новые первыми)
            backup_files.sort(key=os.path.getctime, reverse=True)
            
            # Удаляем старые файлы
            files_to_delete = backup_files[keep_count:]
            deleted_count = 0
            
            for file_path in files_to_delete:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    logger.info(f"Удалена старая резервная копия: {file_path}")
                except Exception as e:
                    logger.error(f"Ошибка удаления старой резервной копии {file_path}: {e}")
            
            if deleted_count > 0:
                logger.info(f"Удалено старых резервных копий: {deleted_count}")
                
        except Exception as e:
            logger.error(f"Ошибка очистки старых резервных копий: {e}")
    
    def run_cleanup_tasks(self) -> Dict[str, Any]:
        """
        Выполняет все задачи очистки данных вручную
        
        Returns:
            Словарь с результатами всех задач очистки
        """
        try:
            logger.info("Начинаем ручное выполнение всех задач очистки")
            
            results = {
                'timestamp': datetime.now().isoformat(),
                'expired_payments_cleanup': self.cleanup_expired_payments(),
                'admin_logs_cleanup': self.cleanup_old_admin_logs(),
                'database_backup': self.create_database_backup()
            }
            
            logger.info(f"Ручное выполнение задач очистки завершено: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка ручного выполнения задач очистки: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }