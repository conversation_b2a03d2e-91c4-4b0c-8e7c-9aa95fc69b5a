#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration file for Rinadzhi Telegram Payment Bot
"""

import os

class Config:
    """Configuration class for the bot"""
    
    # Telegram Bot Token
    TELEGRAM_BOT_TOKEN = "**********************************************"
    
    # Lava.top API credentials
    LAVA_API_KEY = os.getenv("LAVA_API_KEY", "your_lava_api_key_here")
    LAVA_SECRET_KEY = os.getenv("LAVA_SECRET_KEY", "your_lava_secret_key_here")
    
    # Database configuration
    DATABASE_PATH = "/home/<USER>/app/payments.db"
    
    # Channel configuration
    CHANNEL_ID = "@rinadzhi_channel"
    
    # Admin IDs
    ADMIN_IDS = [6354186398]
    
    # Webhook configuration (for production)
    WEBHOOK_URL = os.getenv("WEBHOOK_URL", "https://your-domain.com")
    WEBHOOK_PATH = "/webhook"
    
    # Server configuration
    HOST = "0.0.0.0"
    PORT = int(os.getenv("PORT", 5000))
    
    # Logging configuration
    LOG_LEVEL = "INFO"
    LOG_FILE = "/var/log/telegram-payment-bot/rinadzhi_bot.log"
