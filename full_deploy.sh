#!/bin/bash

# Полный скрипт развертывания Telegram Payment Bot
# Объединяет все этапы развертывания

set -e

echo "=== ПОЛНОЕ РАЗВЕРТЫВАНИЕ TELEGRAM PAYMENT BOT ==="

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Переменные
APP_DIR="/home/<USER>/app"
VENV_DIR="/home/<USER>/venv"
SERVICE_NAME="telegram-payment-bot"
BACKUP_DIR="/home/<USER>/backups"
LOG_DIR="/var/log/telegram-payment-bot"

# Проверка прав root
if [[ $EUID -ne 0 ]]; then
   log_error "Этот скрипт должен быть запущен с правами root (sudo -i)"
   exit 1
fi

# ===============================================
# ЭТАП 1: ПОДГОТОВКА СИСТЕМЫ
# ===============================================
log_step "ЭТАП 1: Подготовка системы"

log_info "Обновление системы..."
apt update && apt upgrade -y

log_info "Установка необходимых пакетов..."
apt install -y python3 python3-pip python3-venv nginx git curl wget unzip htop nano

# ===============================================
# ЭТАП 2: СОЗДАНИЕ ПОЛЬЗОВАТЕЛЯ И ДИРЕКТОРИЙ
# ===============================================
log_step "ЭТАП 2: Создание пользователя и директорий"

log_info "Создание пользователя telegrambot..."
if ! id "telegrambot" &>/dev/null; then
    useradd -m -s /bin/bash telegrambot
    log_info "Пользователь telegrambot создан"
else
    log_warn "Пользователь telegrambot уже существует"
fi

log_info "Создание директорий..."
mkdir -p $APP_DIR
mkdir -p $BACKUP_DIR
mkdir -p $LOG_DIR
mkdir -p /etc/telegram-payment-bot

# Установка прав доступа
chown -R telegrambot:telegrambot /home/<USER>
chown -R telegrambot:telegrambot $LOG_DIR

# ===============================================
# ЭТАП 3: ЗАГРУЗКА КОДА (ПАУЗА ДЛЯ ПОЛЬЗОВАТЕЛЯ)
# ===============================================
log_step "ЭТАП 3: Загрузка кода проекта"

log_warn "ВНИМАНИЕ! Необходимо загрузить код проекта в директорию $APP_DIR"
log_warn "Вы можете:"
log_warn "1. Использовать scp для копирования файлов"
log_warn "2. Использовать git clone (если код в репозитории)"
log_warn "3. Загрузить архив и распаковать"
log_warn ""
log_warn "Пример команды scp с локальной машины:"
log_warn "scp -r /path/to/project/* ubuntu@**************:/home/<USER>/app/"
log_warn ""

read -p "Нажмите Enter когда код будет загружен в $APP_DIR..."

# Проверка наличия основных файлов
if [ ! -f "$APP_DIR/app.py" ]; then
    log_error "Файл app.py не найден в $APP_DIR"
    exit 1
fi

if [ ! -f "$APP_DIR/requirements.txt" ]; then
    log_error "Файл requirements.txt не найден в $APP_DIR"
    exit 1
fi

if [ ! -f "$APP_DIR/.env.production" ]; then
    log_error "Файл .env.production не найден в $APP_DIR"
    exit 1
fi

log_info "Основные файлы проекта найдены"

# ===============================================
# ЭТАП 4: НАСТРОЙКА PYTHON ОКРУЖЕНИЯ
# ===============================================
log_step "ЭТАП 4: Настройка Python окружения"

log_info "Создание виртуального окружения..."
sudo -u telegrambot python3 -m venv $VENV_DIR

log_info "Обновление pip..."
sudo -u telegrambot $VENV_DIR/bin/pip install --upgrade pip

log_info "Установка зависимостей..."
sudo -u telegrambot $VENV_DIR/bin/pip install -r $APP_DIR/requirements.txt

# ===============================================
# ЭТАП 5: НАСТРОЙКА КОНФИГУРАЦИИ
# ===============================================
log_step "ЭТАП 5: Настройка конфигурации"

# Копирование production конфигурации
log_info "Настройка production конфигурации..."
sudo -u telegrambot cp $APP_DIR/.env.production $APP_DIR/.env

# Обновление webhook URL в конфигурации
log_info "Обновление webhook URL..."
sudo -u telegrambot sed -i 's|WEBHOOK_URL=.*|WEBHOOK_URL=http://**************/webhook|g' $APP_DIR/.env

# ===============================================
# ЭТАП 6: СОЗДАНИЕ SYSTEMD СЕРВИСА
# ===============================================
log_step "ЭТАП 6: Создание systemd сервиса"

log_info "Создание systemd сервиса..."
cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Telegram Payment Bot
After=network.target

[Service]
Type=exec
User=telegrambot
Group=telegrambot
WorkingDirectory=$APP_DIR
Environment=PATH=$VENV_DIR/bin
Environment=FLASK_ENV=production
ExecStart=$VENV_DIR/bin/gunicorn --config gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF

# ===============================================
# ЭТАП 7: НАСТРОЙКА NGINX
# ===============================================
log_step "ЭТАП 7: Настройка Nginx"

log_info "Создание конфигурации Nginx..."
cat > /etc/nginx/sites-available/$SERVICE_NAME << EOF
server {
    listen 80;
    server_name _;

    # Увеличиваем размер загружаемых файлов
    client_max_body_size 10M;

    # Основное приложение
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Webhook endpoint
    location /webhook {
        proxy_pass http://127.0.0.1:5000/webhook;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Health check
    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        access_log off;
    }

    # Логирование
    access_log /var/log/nginx/$SERVICE_NAME.access.log;
    error_log /var/log/nginx/$SERVICE_NAME.error.log;
}
EOF

# Активация сайта
ln -sf /etc/nginx/sites-available/$SERVICE_NAME /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# ===============================================
# ЭТАП 8: НАСТРОЙКА ЛОГИРОВАНИЯ
# ===============================================
log_step "ЭТАП 8: Настройка логирования"

log_info "Настройка logrotate..."
cat > /etc/logrotate.d/$SERVICE_NAME << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 telegrambot telegrambot
    postrotate
        systemctl reload $SERVICE_NAME
    endscript
}

/var/log/nginx/$SERVICE_NAME.*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
EOF

# ===============================================
# ЭТАП 9: НАСТРОЙКА БЕЗОПАСНОСТИ
# ===============================================
log_step "ЭТАП 9: Настройка безопасности"

# Настройка файрвола
if command -v ufw &> /dev/null; then
    log_info "Настройка UFW файрвола..."
    ufw allow 22/tcp
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw --force enable
else
    log_warn "UFW не установлен, пропускаем настройку файрвола"
fi

# ===============================================
# ЭТАП 10: ЗАПУСК СЕРВИСОВ
# ===============================================
log_step "ЭТАП 10: Запуск сервисов"

log_info "Перезагрузка systemd..."
systemctl daemon-reload

log_info "Включение автозапуска сервисов..."
systemctl enable $SERVICE_NAME
systemctl enable nginx

log_info "Проверка конфигурации Nginx..."
nginx -t

log_info "Перезапуск Nginx..."
systemctl restart nginx

log_info "Запуск Telegram Payment Bot..."
systemctl start $SERVICE_NAME

# ===============================================
# ЭТАП 11: ПРОВЕРКА РАБОТЫ
# ===============================================
log_step "ЭТАП 11: Проверка работы"

sleep 5

log_info "Проверка статуса сервисов..."
echo "=== Статус Telegram Payment Bot ==="
systemctl status $SERVICE_NAME --no-pager

echo ""
echo "=== Статус Nginx ==="
systemctl status nginx --no-pager

echo ""
echo "=== Проверка портов ==="
netstat -tlnp | grep -E ':80|:5000'

echo ""
echo "=== Тест health check ==="
curl -s http://localhost/health | python3 -m json.tool || echo "Health check недоступен"

# ===============================================
# ЗАВЕРШЕНИЕ
# ===============================================
log_step "РАЗВЕРТЫВАНИЕ ЗАВЕРШЕНО!"

echo ""
log_info "=== ИНФОРМАЦИЯ О РАЗВЕРТЫВАНИИ ==="
log_info "Сервер: **************"
log_info "Веб-интерфейс: http://**************"
log_info "Webhook URL: http://**************/webhook"
log_info "Health check: http://**************/health"
log_info ""
log_info "=== ПОЛЕЗНЫЕ КОМАНДЫ ==="
log_info "Статус сервиса: sudo systemctl status $SERVICE_NAME"
log_info "Логи сервиса: sudo journalctl -u $SERVICE_NAME -f"
log_info "Перезапуск: sudo systemctl restart $SERVICE_NAME"
log_info "Логи Nginx: sudo tail -f /var/log/nginx/$SERVICE_NAME.*.log"
log_info "Логи приложения: sudo tail -f $LOG_DIR/*.log"
log_info ""
log_info "=== СЛЕДУЮЩИЕ ШАГИ ==="
log_info "1. Проверьте настройки в файле $APP_DIR/.env"
log_info "2. Убедитесь, что все API ключи корректны"
log_info "3. Протестируйте webhook: curl -X POST http://**************/webhook/test"
log_info "4. Настройте SSL сертификат (рекомендуется)"

echo ""
echo "=== РАЗВЕРТЫВАНИЕ УСПЕШНО ЗАВЕРШЕНО ==="