# PowerShell скрипт для загрузки проекта на сервер
# Требует установленного OpenSSH Client в Windows

param(
    [string]$ServerIP = "**************",
    [string]$Username = "ubuntu",
    [string]$Password = "dkomqgTaijxro7in^bxd",
    [string]$ProjectPath = "."
)

Write-Host "=== Загрузка Telegram Payment Bot на сервер ===" -ForegroundColor Green

# Проверка наличия архива
$ArchivePath = Join-Path $ProjectPath "telegram_bot_deploy.tar.gz"
if (-not (Test-Path $ArchivePath)) {
    Write-Host "Создание архива проекта..." -ForegroundColor Yellow
    
    # Создаем архив с помощью tar (если доступен в Windows)
    try {
        $excludeParams = @(
            "--exclude=.git",
            "--exclude=__pycache__",
            "--exclude=*.pyc", 
            "--exclude=.pytest_cache",
            "--exclude=*.log",
            "--exclude=payments.db",
            "--exclude=backups"
        )
        
        & tar -czf $ArchivePath $excludeParams -C $ProjectPath .
        Write-Host "Архив создан: $ArchivePath" -ForegroundColor Green
    }
    catch {
        Write-Host "Ошибка создания архива: $_" -ForegroundColor Red
        Write-Host "Создайте архив вручную или используйте другой метод" -ForegroundColor Yellow
        exit 1
    }
}

# Функция для выполнения SCP
function Copy-ToServer {
    param($LocalPath, $RemotePath)
    
    Write-Host "Копирование $LocalPath -> $RemotePath" -ForegroundColor Yellow
    
    # Используем pscp (PuTTY) если доступен, иначе scp
    if (Get-Command pscp -ErrorAction SilentlyContinue) {
        $result = & pscp -pw $Password -batch $LocalPath "${Username}@${ServerIP}:${RemotePath}"
    } elseif (Get-Command scp -ErrorAction SilentlyContinue) {
        # Для scp нужно будет ввести пароль вручную
        Write-Host "Введите пароль сервера: $Password" -ForegroundColor Cyan
        $result = & scp -o StrictHostKeyChecking=no $LocalPath "${Username}@${ServerIP}:${RemotePath}"
    } else {
        Write-Host "Не найден scp или pscp. Установите OpenSSH Client или PuTTY" -ForegroundColor Red
        return $false
    }
    
    return $LASTEXITCODE -eq 0
}

# Функция для выполнения SSH команд
function Invoke-SSHCommand {
    param($Command)
    
    Write-Host "Выполнение команды: $Command" -ForegroundColor Yellow
    
    if (Get-Command plink -ErrorAction SilentlyContinue) {
        $result = & plink -pw $Password -batch "${Username}@${ServerIP}" $Command
    } elseif (Get-Command ssh -ErrorAction SilentlyContinue) {
        Write-Host "Введите пароль сервера: $Password" -ForegroundColor Cyan
        $result = & ssh -o StrictHostKeyChecking=no "${Username}@${ServerIP}" $Command
    } else {
        Write-Host "Не найден ssh или plink. Установите OpenSSH Client или PuTTY" -ForegroundColor Red
        return $false
    }
    
    return $LASTEXITCODE -eq 0
}

# Основной процесс загрузки
try {
    Write-Host "1. Загрузка архива на сервер..." -ForegroundColor Green
    
    if (-not (Copy-ToServer $ArchivePath "/tmp/telegram_bot_deploy.tar.gz")) {
        throw "Ошибка загрузки архива"
    }
    
    Write-Host "2. Загрузка скрипта развертывания..." -ForegroundColor Green
    
    $DeployScript = Join-Path $ProjectPath "full_deploy.sh"
    if (Test-Path $DeployScript) {
        if (-not (Copy-ToServer $DeployScript "/tmp/full_deploy.sh")) {
            throw "Ошибка загрузки скрипта развертывания"
        }
    }
    
    Write-Host "3. Подготовка к развертыванию..." -ForegroundColor Green
    
    # Создание базовых директорий
    $commands = @(
        "sudo mkdir -p /home/<USER>/app",
        "sudo chown -R ubuntu:ubuntu /home/<USER>",
        "cd /tmp && sudo tar -xzf telegram_bot_deploy.tar.gz -C /home/<USER>/app/",
        "sudo chown -R ubuntu:ubuntu /home/<USER>/app",
        "sudo chmod +x /tmp/full_deploy.sh"
    )
    
    foreach ($cmd in $commands) {
        Write-Host "Выполнение: $cmd" -ForegroundColor Cyan
        if (-not (Invoke-SSHCommand $cmd)) {
            Write-Host "Предупреждение: команда завершилась с ошибкой" -ForegroundColor Yellow
        }
    }
    
    Write-Host "`n=== ЗАГРУЗКА ЗАВЕРШЕНА ===" -ForegroundColor Green
    Write-Host "Файлы загружены на сервер. Теперь подключитесь к серверу и выполните:" -ForegroundColor Yellow
    Write-Host "ssh ubuntu@$ServerIP" -ForegroundColor Cyan
    Write-Host "sudo -i" -ForegroundColor Cyan
    Write-Host "cd /tmp && ./full_deploy.sh" -ForegroundColor Cyan
    Write-Host "`nИли выполните команды развертывания вручную согласно DEPLOYMENT_INSTRUCTIONS.md" -ForegroundColor Yellow
    
} catch {
    Write-Host "Ошибка: $_" -ForegroundColor Red
    Write-Host "`nАльтернативные способы загрузки:" -ForegroundColor Yellow
    Write-Host "1. Используйте WinSCP для графической загрузки файлов" -ForegroundColor Cyan
    Write-Host "2. Используйте PuTTY + PSCP" -ForegroundColor Cyan
    Write-Host "3. Загрузите файлы через веб-интерфейс (если доступен)" -ForegroundColor Cyan
    Write-Host "4. Используйте git clone (если код в репозитории)" -ForegroundColor Cyan
}

Write-Host "`nИнформация для подключения:" -ForegroundColor Green
Write-Host "IP: $ServerIP" -ForegroundColor White
Write-Host "Пользователь: $Username" -ForegroundColor White
Write-Host "Пароль: $Password" -ForegroundColor White