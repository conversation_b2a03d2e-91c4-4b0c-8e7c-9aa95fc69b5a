"""
Gunicorn configuration для production развертывания
"""

import os
import multiprocessing
from pathlib import Path

# Основные настройки
bind = "127.0.0.1:5000"
workers = int(os.environ.get('WORKERS', multiprocessing.cpu_count() * 2 + 1))
worker_class = os.environ.get('WORKER_CLASS', 'sync')
worker_connections = int(os.environ.get('WORKER_CONNECTIONS', 1000))

# Настройки производительности
max_requests = int(os.environ.get('MAX_REQUESTS', 1000))
max_requests_jitter = int(os.environ.get('MAX_REQUESTS_JITTER', 100))
timeout = int(os.environ.get('TIMEOUT', 30))
keepalive = int(os.environ.get('KEEPALIVE', 2))

# Настройки процессов
preload_app = True
daemon = False
user = os.environ.get('USER', 'telegrambot')
group = os.environ.get('GROUP', 'telegrambot')

# Настройки логирования
log_dir = Path('/var/log/telegram-payment-bot')
log_dir.mkdir(parents=True, exist_ok=True)

accesslog = str(log_dir / 'access.log')
errorlog = str(log_dir / 'error.log')
loglevel = os.environ.get('LOG_LEVEL', 'info').lower()
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Настройки безопасности
limit_request_line = 4096
limit_request_fields = 100
limit_request_field_size = 8190

# SSL настройки (если используется)
keyfile = os.environ.get('SSL_KEYFILE')
certfile = os.environ.get('SSL_CERTFILE')

# Настройки для graceful restart
graceful_timeout = 30
max_requests_jitter = 50

# Хуки для мониторинга
def on_starting(server):
    """Вызывается при запуске сервера"""
    server.log.info("Запуск Gunicorn сервера для Telegram Payment Bot")

def on_reload(server):
    """Вызывается при перезагрузке"""
    server.log.info("Перезагрузка Gunicorn сервера")

def worker_int(worker):
    """Вызывается при получении SIGINT worker'ом"""
    worker.log.info(f"Worker {worker.pid} получил SIGINT")

def pre_fork(server, worker):
    """Вызывается перед fork worker'а"""
    server.log.info(f"Создание worker {worker.pid}")

def post_fork(server, worker):
    """Вызывается после fork worker'а"""
    server.log.info(f"Worker {worker.pid} запущен")

def worker_abort(worker):
    """Вызывается при аварийном завершении worker'а"""
    worker.log.error(f"Worker {worker.pid} аварийно завершен")

# Настройки для статических файлов (если нужно)
static_map = {
    '/static': './static'
}

# Переменные окружения для приложения
raw_env = [
    'FLASK_ENV=production',
    f'WORKERS={workers}',
    f'WORKER_CLASS={worker_class}'
]