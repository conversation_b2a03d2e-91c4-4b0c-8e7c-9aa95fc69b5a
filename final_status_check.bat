@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Final status check...
echo.
echo Checking service status:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S systemctl is-active telegram-payment-bot"
echo.
echo Checking if service is enabled:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S systemctl is-enabled telegram-payment-bot"
echo.
echo Checking recent logs:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S journalctl -u telegram-payment-bot --no-pager -n 10"
echo.
echo Checking bot process:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "ps aux | grep python | grep main.py"
echo.
echo Checking web admin panel (port 5000):
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "netstat -tlnp | grep :5000"
echo Done.