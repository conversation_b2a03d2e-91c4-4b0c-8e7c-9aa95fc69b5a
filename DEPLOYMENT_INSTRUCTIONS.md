# Инструкция по развертыванию Telegram Payment Bot на сервере

## Данные для подключения к серверу
- **IP-адрес**: **************
- **Пользователь**: ubuntu
- **Пароль**: dkomqgTaijxro7in^bxd
- **Порт SSH**: 22

## Пошаговая инструкция

### 1. Подключение к серверу
```bash
ssh ubuntu@**************
```
Введите пароль: `dkomqgTaijxro7in^bxd`

### 2. Получение root-привилегий
```bash
sudo -i
```

### 3. Загрузка проекта на сервер

#### Вариант A: Загрузка архива (рекомендуется)
С локальной машины выполните:
```bash
scp telegram_bot_deploy.tar.gz ubuntu@**************:/tmp/
```

На сервере:
```bash
cd /tmp
tar -xzf telegram_bot_deploy.tar.gz -C /home/<USER>/app/
chown -R telegrambot:telegrambot /home/<USER>/app/
```

#### Вариант B: Прямое копирование файлов
С локальной машины:
```bash
scp -r ./* ubuntu@**************:/home/<USER>/app/
```

### 4. Запуск скрипта развертывания

На сервере под root:
```bash
# Загрузите скрипт развертывания
wget https://raw.githubusercontent.com/your-repo/full_deploy.sh
# или скопируйте содержимое full_deploy.sh в файл

chmod +x full_deploy.sh
./full_deploy.sh
```

### 5. Альтернативный способ - пошаговое развертывание

Если автоматический скрипт не работает, выполните команды вручную:

#### 5.1. Подготовка системы
```bash
apt update && apt upgrade -y
apt install -y python3 python3-pip python3-venv nginx git curl wget
```

#### 5.2. Создание пользователя
```bash
useradd -m -s /bin/bash telegrambot
mkdir -p /home/<USER>/app
mkdir -p /home/<USER>/backups
mkdir -p /var/log/telegram-payment-bot
chown -R telegrambot:telegrambot /home/<USER>
chown -R telegrambot:telegrambot /var/log/telegram-payment-bot
```

#### 5.3. Настройка Python окружения
```bash
sudo -u telegrambot python3 -m venv /home/<USER>/venv
sudo -u telegrambot /home/<USER>/venv/bin/pip install --upgrade pip
sudo -u telegrambot /home/<USER>/venv/bin/pip install -r /home/<USER>/app/requirements.txt
```

#### 5.4. Настройка конфигурации
```bash
cd /home/<USER>/app
sudo -u telegrambot cp .env.production .env
sudo -u telegrambot sed -i 's|WEBHOOK_URL=.*|WEBHOOK_URL=http://**************/webhook|g' .env
```

#### 5.5. Создание systemd сервиса
```bash
cat > /etc/systemd/system/telegram-payment-bot.service << 'EOF'
[Unit]
Description=Telegram Payment Bot
After=network.target

[Service]
Type=exec
User=telegrambot
Group=telegrambot
WorkingDirectory=/home/<USER>/app
Environment=PATH=/home/<USER>/venv/bin
Environment=FLASK_ENV=production
ExecStart=/home/<USER>/venv/bin/gunicorn --config gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=telegram-payment-bot

[Install]
WantedBy=multi-user.target
EOF
```

#### 5.6. Настройка Nginx
```bash
cat > /etc/nginx/sites-available/telegram-payment-bot << 'EOF'
server {
    listen 80;
    server_name _;

    client_max_body_size 10M;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /webhook {
        proxy_pass http://127.0.0.1:5000/webhook;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        access_log off;
    }

    access_log /var/log/nginx/telegram-payment-bot.access.log;
    error_log /var/log/nginx/telegram-payment-bot.error.log;
}
EOF

ln -sf /etc/nginx/sites-available/telegram-payment-bot /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
```

#### 5.7. Запуск сервисов
```bash
systemctl daemon-reload
systemctl enable telegram-payment-bot
systemctl enable nginx
nginx -t
systemctl restart nginx
systemctl start telegram-payment-bot
```

### 6. Проверка работы

#### 6.1. Проверка статуса сервисов
```bash
systemctl status telegram-payment-bot
systemctl status nginx
```

#### 6.2. Проверка портов
```bash
netstat -tlnp | grep -E ':80|:5000'
```

#### 6.3. Тест health check
```bash
curl http://localhost/health
curl http://**************/health
```

#### 6.4. Просмотр логов
```bash
# Логи приложения
journalctl -u telegram-payment-bot -f

# Логи Nginx
tail -f /var/log/nginx/telegram-payment-bot.*.log

# Логи приложения (если настроены)
tail -f /var/log/telegram-payment-bot/*.log
```

## Важные URL после развертывания

- **Веб-интерфейс**: http://**************
- **Админ-панель**: http://**************/admin
- **Webhook URL**: http://**************/webhook
- **Health Check**: http://**************/health
- **Метрики**: http://**************/metrics

## Настройка после развертывания

### 1. Проверка конфигурации
Отредактируйте файл `/home/<USER>/app/.env` и убедитесь, что все необходимые переменные заполнены:

```bash
nano /home/<USER>/app/.env
```

Особенно важно проверить:
- `TELEGRAM_BOT_TOKEN`
- `LAVA_API_KEY`
- `LAVA_SECRET_KEY`
- `WEBHOOK_URL`
- `CHANNEL_ID`
- `ADMIN_USER_IDS`

### 2. Перезапуск после изменения конфигурации
```bash
systemctl restart telegram-payment-bot
```

### 3. Настройка webhook в Telegram
Webhook будет автоматически настроен при запуске бота, но вы можете проверить его статус через Telegram Bot API.

## Полезные команды для администрирования

```bash
# Перезапуск сервиса
systemctl restart telegram-payment-bot

# Просмотр логов в реальном времени
journalctl -u telegram-payment-bot -f

# Проверка статуса
systemctl status telegram-payment-bot

# Остановка сервиса
systemctl stop telegram-payment-bot

# Запуск сервиса
systemctl start telegram-payment-bot

# Перезагрузка конфигурации Nginx
nginx -s reload

# Проверка конфигурации Nginx
nginx -t
```

## Устранение неполадок

### Если сервис не запускается:
1. Проверьте логи: `journalctl -u telegram-payment-bot -n 50`
2. Проверьте права доступа: `ls -la /home/<USER>/app/`
3. Проверьте виртуальное окружение: `sudo -u telegrambot /home/<USER>/venv/bin/python --version`

### Если webhook не работает:
1. Проверьте доступность: `curl http://**************/webhook/test`
2. Проверьте логи Nginx: `tail -f /var/log/nginx/telegram-payment-bot.error.log`
3. Проверьте настройки файрвола: `ufw status`

### Если база данных недоступна:
1. Проверьте права доступа к файлу БД
2. Проверьте наличие директории для БД
3. Проверьте логи приложения

## Безопасность

После развертывания рекомендуется:

1. **Настроить SSL сертификат** (Let's Encrypt)
2. **Изменить пароли по умолчанию**
3. **Настроить регулярные резервные копии**
4. **Обновить систему**: `apt update && apt upgrade`
5. **Настроить мониторинг**

## Резервное копирование

Важные файлы для резервного копирования:
- `/home/<USER>/app/.env` - конфигурация
- `/home/<USER>/app/payments.db` - база данных
- `/home/<USER>/backups/` - автоматические резервные копии
- `/etc/nginx/sites-available/telegram-payment-bot` - конфигурация Nginx
- `/etc/systemd/system/telegram-payment-bot.service` - конфигурация сервиса

## Troubleshooting

### Общие проблемы

1. **Ошибка подключения к базе данных**
   - Проверьте настройки DATABASE_URL в .env
   - Убедитесь, что PostgreSQL запущен

2. **Telegram бот не отвечает**
   - Проверьте TELEGRAM_BOT_TOKEN
   - Убедитесь, что webhook настроен правильно

3. **Ошибки платежей**
   - Проверьте настройки YOOKASSA_SHOP_ID и YOOKASSA_SECRET_KEY
   - Убедитесь, что webhook URL доступен извне

### Проблемы с админ-панелью

#### Не удается войти в админ-панель (форма не отправляется)

**Симптомы:**
- Форма входа загружается нормально
- При вводе правильного пароля (`admin123`) ничего не происходит
- Страница остается на `/admin/login` без сообщений об ошибке
- В логах нет ошибок

**Причина:**
Настройка `SESSION_COOKIE_SECURE = true` в config.py заставляет Flask устанавливать cookies только для HTTPS соединений. При использовании HTTP (например, для разработки или тестирования) cookies не сохраняются, что приводит к невозможности аутентификации.

**Решение:**

1. **Для HTTP (разработка/тестирование):**
   ```bash
   # Измените настройку в config.py
   sudo sed -i "s/SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'true')/SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'false')/" /home/<USER>/app/config.py

   # Перезапустите приложение
   sudo pkill -f gunicorn
   sudo bash -c "cd /home/<USER>/app && nohup sudo -u telegrambot /home/<USER>/app/venv/bin/gunicorn --bind 0.0.0.0:5000 --workers 2 --pid /tmp/gunicorn.pid wsgi:app > /var/log/telegram-payment-bot/gunicorn.log 2>&1 &"
   ```

2. **Для HTTPS (production):**
   ```bash
   # Установите переменную окружения
   export SESSION_COOKIE_SECURE=false
   # или добавьте в .env файл:
   echo "SESSION_COOKIE_SECURE=false" >> .env
   ```

3. **Альтернативное решение - настройка HTTPS:**
   - Настройте SSL сертификат
   - Используйте nginx с SSL
   - Настройте Let's Encrypt

**Проверка решения:**
```bash
# Проверьте, что настройка изменилась
grep "SESSION_COOKIE_SECURE" /home/<USER>/app/config.py

# Проверьте, что приложение работает
curl -I http://localhost:5000/admin/login

# Тестовый вход (должен вернуть редирект)
curl -c cookies.txt http://localhost:5000/admin/login | grep csrf_token
# Извлеките CSRF токен и сделайте POST запрос
curl -b cookies.txt -X POST -d "password=admin123&csrf_token=ВАШТОКЕН" http://localhost:5000/admin/login
```

#### Другие проблемы с админ-панелью

1. **CSRF токен не работает**
   - Убедитесь, что SECRET_KEY настроен правильно
   - Проверьте, что форма содержит `{{ form.hidden_tag() }}`

2. **Ошибки импорта модулей**
   - Проверьте, что все зависимости установлены: `pip install -r requirements.txt`
   - Убедитесь, что PYTHONPATH настроен правильно