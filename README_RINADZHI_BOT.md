# Rinadzhi Telegram Payment Bot

Полнофункциональный Telegram бот для продажи подписок на канал с интеграцией Lava.top.

## 🚀 Возможности

- ✅ **4 тарифа подписки**: 1/3/6/12 месяцев с автоматическими скидками
- ✅ **3 способа оплаты**: Карта РФ, международные карты, криптовалюта
- ✅ **Интеграция Lava.top**: Автоматическая генерация ссылок на оплату
- ✅ **База данных SQLite**: Хранение пользователей, подписок и платежей
- ✅ **Русский интерфейс**: Полностью локализованный интерфейс
- ✅ **Красивое меню**: Кнопки и inline-клавиатуры

## 📋 Тарифы

| План | Цена | Скидка | Длительность |
|------|------|--------|--------------|
| 1 месяц | 299 ₽ | - | 30 дней |
| 3 месяца | 799 ₽ | 33% | 90 дней |
| 6 месяцев | 1,399 ₽ | 42% | 180 дней |
| 12 месяцев | 2,499 ₽ | 58% | 365 дней |

## 🛠 Установка на сервер

### 🔗 Подключение к серверу

**Рабочая команда подключения:**
```bash
plink -ssh ubuntu@************** -pw dkomqgTaijxro7in^bxd "ls -la ~/"
```

**Для интерактивного подключения:**
```bash
plink -ssh ubuntu@************** -pw dkomqgTaijxro7in^bxd
```

**⚠️ ВАЖНО:** При подключении появится сообщение "Access granted. Press Return to begin session." - **обязательно нажмите Enter**, иначе подключение зависнет!

**Получение root прав:**
```bash
sudo -i
```

### 1. Подготовка сервера

```bash
# Создание пользователя и директорий
sudo useradd -m -s /bin/bash telegrambot
sudo mkdir -p /home/<USER>/app
sudo mkdir -p /var/log/telegram-payment-bot
sudo chown telegrambot:telegrambot /home/<USER>/app
sudo chown telegrambot:telegrambot /var/log/telegram-payment-bot
```

### 2. Установка зависимостей

```bash
# Переход в директорию
cd /home/<USER>/app

# Создание виртуального окружения
sudo -u telegrambot python3 -m venv venv
sudo -u telegrambot /home/<USER>/app/venv/bin/pip install --upgrade pip

# Установка библиотек
sudo -u telegrambot /home/<USER>/app/venv/bin/pip install pyTelegramBotAPI requests sqlite3
```

### 3. Копирование файлов

Скопируйте следующие файлы в `/home/<USER>/app/`:

- `rinadzhi_bot_final.py` → `rinadzhi_bot.py`
- `config_rinadzhi.py` → `config.py`
- `init_database.py`

```bash
# Установка прав доступа
sudo chown telegrambot:telegrambot /home/<USER>/app/*.py
sudo chmod +x /home/<USER>/app/rinadzhi_bot.py
```

### 4. Инициализация базы данных

```bash
sudo -u telegrambot /home/<USER>/app/venv/bin/python /home/<USER>/app/init_database.py
```

### 5. Настройка конфигурации

Отредактируйте файл `config.py`:

```python
# Обязательно настройте:
TELEGRAM_BOT_TOKEN = "ваш_токен_бота"
LAVA_API_KEY = "ваш_lava_api_ключ"
LAVA_SECRET_KEY = "ваш_lava_secret_ключ"
CHANNEL_ID = "@ваш_канал"
```

### 6. Запуск бота

```bash
# Запуск в фоновом режиме
sudo bash -c "cd /home/<USER>/app && sudo -u telegrambot /home/<USER>/app/venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &"

# Проверка логов
sudo tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log
```

## 📱 Команды бота

- `/start` - Начать работу с ботом
- `/buy` - Купить подписку
- `/status` - Статус подписки
- `/about` - О канале
- `/help` - Задать вопрос

## 🔧 Настройка Lava.top

1. Зарегистрируйтесь на [Lava.top](https://lava.top)
2. Получите API ключи в личном кабинете
3. Настройте webhook URL для получения уведомлений о платежах
4. Добавьте ключи в `config.py`

## 📊 Структура базы данных

### Таблицы:
- `users` - Пользователи бота
- `subscriptions` - Активные подписки
- `payments` - История платежей
- `logs` - Логи действий

## 🚨 Важные заметки

1. **Безопасность**: Никогда не публикуйте токены и API ключи
2. **Backup**: Регулярно создавайте резервные копии базы данных
3. **Мониторинг**: Следите за логами бота
4. **Обновления**: Проверяйте обновления библиотек

## 🔧 Управление ботом

**📋 Полное руководство по управлению:** См. файл [RINADZHI_BOT_MANAGEMENT.md](RINADZHI_BOT_MANAGEMENT.md)

**⚡ Быстрые команды:**

**Проверить статус:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ps aux | grep rinadzhi"
```

**Посмотреть логи:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

**Перезапустить бота:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot && sleep 3 && sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"
```

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи: `tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log`
2. Проверьте процессы: `ps aux | grep rinadzhi_bot`
3. Перезапустите бота при необходимости
4. См. полное руководство: [RINADZHI_BOT_MANAGEMENT.md](RINADZHI_BOT_MANAGEMENT.md)

## 🎯 Статус реализации

- ✅ Telegram бот с русским интерфейсом
- ✅ 4 тарифа подписки (1/3/6/12 месяцев)
- ✅ 3 способа оплаты
- ✅ Интеграция с Lava.top API
- ✅ База данных SQLite
- ✅ Система логирования
- ⚠️ Webhook обработка (требует настройки)
- ⚠️ Автоматическое управление каналом (требует доработки)

Бот готов к использованию! 🚀
