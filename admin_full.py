"""
Веб-админ панель для управления Telegram Payment Bot
"""

import logging
from datetime import datetime, timedelta
from functools import wraps
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify, current_app
from flask_wtf import FlaskForm
from wtforms import PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired

from config import Config
from app.models.database import DatabaseService
from app.services.scheduler_service import SchedulerService

logger = logging.getLogger('admin')

def safe_url_for(endpoint, **values):
    """Безопасная версия url_for с обработкой ошибок"""
    try:
        return url_for(endpoint, **values)
    except Exception as e:
        logger.error(f"Ошибка генерации URL для {endpoint}: {e}")
        # Возвращаем базовые URL в случае ошибки
        if endpoint == 'admin.login':
            return '/admin/login'
        elif endpoint == 'admin.dashboard':
            return '/admin/dashboard'
        elif endpoint == 'admin.users':
            return '/admin/users'
        elif endpoint == 'admin.payments':
            return '/admin/payments'
        elif endpoint == 'admin.logs':
            return '/admin/logs'
        elif endpoint == 'admin.settings':
            return '/admin/settings'
        elif endpoint == 'admin.subscriptions':
            return '/admin/subscriptions'
        else:
            return '/admin/dashboard'

# Создаем Blueprint для админ панели
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# Добавляем контекстный процессор для шаблонов
@admin_bp.app_context_processor
def inject_safe_url_for():
    """Добавляет safe_url_for в контекст всех шаблонов админ-панели"""
    return dict(safe_url_for=safe_url_for)

class LoginForm(FlaskForm):
    """Форма входа в админ панель"""
    password = PasswordField('Пароль', validators=[DataRequired()])
    remember_me = BooleanField('Запомнить меня')
    submit = SubmitField('Войти')

def login_required(f):
    """Декоратор для проверки авторизации"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('admin_logged_in'):
            return redirect(safe_url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function

def get_db_service():
    """Получить экземпляр DatabaseService"""
    try:
        return DatabaseService()
    except Exception as e:
        logger.error(f"Ошибка подключения к базе данных: {e}")
        return None

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Страница входа в админ панель"""
    if session.get('admin_logged_in'):
        return redirect(safe_url_for('admin.dashboard'))
    
    form = LoginForm()
    
    if form.validate_on_submit():
        if form.password.data == Config.ADMIN_PASSWORD:
            session['admin_logged_in'] = True
            session.permanent = form.remember_me.data
            logger.info("Успешный вход в админ панель")
            flash('Добро пожаловать в админ панель!', 'success')
            
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(safe_url_for('admin.dashboard'))
        else:
            logger.warning("Неудачная попытка входа в админ панель")
            flash('Неверный пароль', 'error')
    
    return render_template('admin/login.html', form=form)

@admin_bp.route('/logout')
def logout():
    """Выход из админ панели"""
    session.pop('admin_logged_in', None)
    logger.info("Выход из админ панели")
    flash('Вы вышли из системы', 'info')
    return redirect(safe_url_for('admin.login'))

@admin_bp.route('/')
@admin_bp.route('/dashboard')
@login_required
def dashboard():
    """Главная страница админ панели"""
    db = get_db_service()
    
    # Получаем статистику
    stats = {
        'total_users': 0,
        'active_subscriptions': 0,
        'total_payments': 0,
        'today_registrations': 0,
        'today_payments': 0,
        'total_revenue': 0
    }
    
    if db:
        try:
            # Общая статистика
            stats['total_users'] = db.get_total_users_count()
            stats['active_subscriptions'] = db.get_active_subscriptions_count()
            stats['total_payments'] = db.get_total_payments_count()
            stats['total_revenue'] = db.get_total_revenue()
            
            # Статистика за сегодня
            today = datetime.now().date()
            stats['today_registrations'] = db.get_registrations_count_by_date(today)
            stats['today_payments'] = db.get_payments_count_by_date(today)
            
        except Exception as e:
            logger.error(f"Ошибка получения статистики: {e}")
            flash('Ошибка загрузки статистики', 'error')
    
    # Получаем последние события
    recent_users = []
    recent_payments = []
    
    if db:
        try:
            recent_users = db.get_recent_users(limit=5)
            recent_payments = db.get_recent_payments(limit=5)
        except Exception as e:
            logger.error(f"Ошибка получения последних событий: {e}")
    
    return render_template('admin/dashboard.html', 
                         stats=stats, 
                         recent_users=recent_users,
                         recent_payments=recent_payments)

@admin_bp.route('/users')
@login_required
def users():
    """Страница управления пользователями"""
    db = get_db_service()
    
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    users_data = []
    total_users = 0
    
    if db:
        try:
            users_data, total_users = db.get_users_paginated(page, per_page)
        except Exception as e:
            logger.error(f"Ошибка получения пользователей: {e}")
            flash('Ошибка загрузки пользователей', 'error')
    
    # Вычисляем пагинацию
    total_pages = (total_users + per_page - 1) // per_page
    
    return render_template('admin/users.html', 
                         users=users_data,
                         page=page,
                         total_pages=total_pages,
                         total_users=total_users)

@admin_bp.route('/users/<int:user_id>')
@login_required
def user_detail(user_id):
    """Детальная информация о пользователе"""
    db = get_db_service()
    
    user_data = None
    user_payments = []
    user_subscriptions = []
    
    if db:
        try:
            user_data = db.get_user_by_id(user_id)
            if user_data:
                user_payments = db.get_user_payments(user_id)
                user_subscriptions = db.get_user_subscriptions(user_id)
            else:
                flash('Пользователь не найден', 'error')
                return redirect(safe_url_for('admin.users'))
        except Exception as e:
            logger.error(f"Ошибка получения данных пользователя {user_id}: {e}")
            flash('Ошибка загрузки данных пользователя', 'error')
            return redirect(safe_url_for('admin.users'))
    
    return render_template('admin/user_detail.html',
                         user=user_data,
                         payments=user_payments,
                         subscriptions=user_subscriptions)

@admin_bp.route('/payments')
@login_required
def payments():
    """Страница управления платежами"""
    db = get_db_service()
    
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    per_page = 20
    
    payments_data = []
    total_payments = 0
    
    if db:
        try:
            payments_data, total_payments = db.get_payments_paginated(page, per_page, status_filter)
        except Exception as e:
            logger.error(f"Ошибка получения платежей: {e}")
            flash('Ошибка загрузки платежей', 'error')
    
    # Вычисляем пагинацию
    total_pages = (total_payments + per_page - 1) // per_page
    
    return render_template('admin/payments.html',
                         payments=payments_data,
                         page=page,
                         total_pages=total_pages,
                         total_payments=total_payments,
                         status_filter=status_filter)

@admin_bp.route('/subscriptions')
@login_required
def subscriptions():
    """Страница управления подписками"""
    db = get_db_service()
    
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    per_page = 20
    
    subscriptions_data = []
    total_subscriptions = 0
    
    if db:
        try:
            subscriptions_data, total_subscriptions = db.get_subscriptions_paginated(page, per_page, status_filter)
        except Exception as e:
            logger.error(f"Ошибка получения подписок: {e}")
            flash('Ошибка загрузки подписок', 'error')
    
    # Вычисляем пагинацию
    total_pages = (total_subscriptions + per_page - 1) // per_page
    
    return render_template('admin/subscriptions.html',
                         subscriptions=subscriptions_data,
                         page=page,
                         total_pages=total_pages,
                         total_subscriptions=total_subscriptions,
                         status_filter=status_filter)

@admin_bp.route('/logs')
@login_required
def logs():
    """Страница просмотра логов"""
    log_level = request.args.get('level', 'INFO')
    lines = request.args.get('lines', 100, type=int)

    log_entries = []

    try:
        # Читаем лог файл
        log_file_path = '/var/log/telegram-payment-bot/gunicorn.log'
        with open(log_file_path, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()

        # Берем последние N строк
        recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

        # Фильтруем по уровню логирования
        for line in recent_lines:
            if log_level.upper() in line.upper() or log_level == 'ALL':
                log_entries.append({
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'level': log_level,
                    'message': line.strip()
                })

    except Exception as e:
        logger.error(f"Ошибка чтения логов: {e}")
        flash('Ошибка загрузки логов', 'error')

    return render_template('admin/logs.html',
                         log_entries=log_entries,
                         current_level=log_level,
                         lines_count=lines)

@admin_bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """Страница настроек"""
    if request.method == 'POST':
        try:
            # Здесь можно добавить логику сохранения настроек
            flash('Настройки сохранены', 'success')
        except Exception as e:
            logger.error(f"Ошибка сохранения настроек: {e}")
            flash('Ошибка сохранения настроек', 'error')

    # Получаем текущие настройки
    settings_data = {
        'bot_token': Config.TELEGRAM_BOT_TOKEN[:10] + '...' if Config.TELEGRAM_BOT_TOKEN else 'Не настроен',
        'channel_id': Config.CHANNEL_ID,
        'webhook_url': Config.WEBHOOK_URL,
        'lava_api_key': Config.LAVA_API_KEY[:10] + '...' if Config.LAVA_API_KEY else 'Не настроен',
        'admin_password': '***' if Config.ADMIN_PASSWORD else 'Не настроен'
    }

    return render_template('admin/settings.html', settings=settings_data)

# API endpoints для AJAX запросов
@admin_bp.route('/api/stats')
@login_required
def api_stats():
    """API для получения статистики"""
    db = get_db_service()

    stats = {
        'total_users': 0,
        'active_subscriptions': 0,
        'total_payments': 0,
        'total_revenue': 0
    }

    if db:
        try:
            stats['total_users'] = db.get_total_users_count()
            stats['active_subscriptions'] = db.get_active_subscriptions_count()
            stats['total_payments'] = db.get_total_payments_count()
            stats['total_revenue'] = db.get_total_revenue()
        except Exception as e:
            logger.error(f"Ошибка получения статистики через API: {e}")
            return jsonify({'error': 'Ошибка получения данных'}), 500

    return jsonify(stats)

@admin_bp.route('/api/user/<int:user_id>/toggle_subscription', methods=['POST'])
@login_required
def api_toggle_user_subscription(user_id):
    """API для включения/отключения подписки пользователя"""
    db = get_db_service()

    if not db:
        return jsonify({'error': 'Ошибка подключения к базе данных'}), 500

    try:
        action = request.json.get('action')  # 'activate' или 'deactivate'

        if action == 'activate':
            success = db.activate_user_subscription(user_id)
            message = 'Подписка активирована'
        elif action == 'deactivate':
            success = db.deactivate_user_subscription(user_id)
            message = 'Подписка деактивирована'
        else:
            return jsonify({'error': 'Неверное действие'}), 400

        if success:
            logger.info(f"Изменение подписки пользователя {user_id}: {action}")
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'error': 'Ошибка изменения подписки'}), 500

    except Exception as e:
        logger.error(f"Ошибка изменения подписки пользователя {user_id}: {e}")
        return jsonify({'error': 'Внутренняя ошибка сервера'}), 500

@admin_bp.route('/api/payment/<int:payment_id>/refund', methods=['POST'])
@login_required
def api_refund_payment(payment_id):
    """API для возврата платежа"""
    db = get_db_service()

    if not db:
        return jsonify({'error': 'Ошибка подключения к базе данных'}), 500

    try:
        # Здесь должна быть логика возврата через платежную систему
        success = db.mark_payment_refunded(payment_id)

        if success:
            logger.info(f"Возврат платежа {payment_id}")
            return jsonify({'success': True, 'message': 'Платеж возвращен'})
        else:
            return jsonify({'error': 'Ошибка возврата платежа'}), 500

    except Exception as e:
        logger.error(f"Ошибка возврата платежа {payment_id}: {e}")
        return jsonify({'error': 'Внутренняя ошибка сервера'}), 500

@admin_bp.route('/api/system/restart', methods=['POST'])
@login_required
def api_restart_system():
    """API для перезапуска системы"""
    try:
        # Здесь можно добавить логику перезапуска бота
        logger.info("Запрос на перезапуск системы")
        return jsonify({'success': True, 'message': 'Система перезапускается...'})
    except Exception as e:
        logger.error(f"Ошибка перезапуска системы: {e}")
        return jsonify({'error': 'Ошибка перезапуска'}), 500

# Обработчики ошибок для админ панели
@admin_bp.errorhandler(404)
def not_found_error(error):
    """Обработчик ошибки 404"""
    return render_template('admin/error.html',
                         error_code=404,
                         error_message='Страница не найдена'), 404

@admin_bp.errorhandler(500)
def internal_error(error):
    """Обработчик ошибки 500"""
    return render_template('admin/error.html',
                         error_code=500,
                         error_message='Внутренняя ошибка сервера'), 500
