#!/usr/bin/env python3
"""
Интеграционные тесты для webhook endpoint
"""

import json
import time
import hmac
import hashlib
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal

from app.services.webhook_handler import WebhookHandler
from app.services.payment_service import PaymentService
from app.services.notification_service import NotificationService
from app.models.database import DatabaseService
from config import Config


class TestWebhookIntegration:
    """Интеграционные тесты для webhook endpoint"""
    
    @pytest.fixture
    def mock_services(self):
        """Создает mock объекты сервисов"""
        payment_service = Mock(spec=PaymentService)
        notification_service = Mock(spec=NotificationService)
        db_service = Mock(spec=DatabaseService)
        
        # Настраиваем mock для валидации подписи
        payment_service.validate_webhook_signature.return_value = True
        
        return payment_service, notification_service, db_service
    
    @pytest.fixture
    def webhook_handler(self, mock_services):
        """Создает экземпляр WebhookHandler с mock сервисами"""
        payment_service, notification_service, db_service = mock_services
        return WebhookHandler(payment_service, notification_service, db_service)
    
    @pytest.fixture
    def mock_request(self):
        """Создает mock объект Flask request"""
        mock_req = Mock()
        mock_req.remote_addr = '127.0.0.1'
        mock_req.headers = {'X-Lava-Signature': 'valid_signature'}
        mock_req.get_data.return_value = '{"test": "data"}'
        return mock_req
    
    def create_valid_webhook_payload(self, order_id="tg_123456_1640995200", status="completed", amount="299.00"):
        """Создает валидный payload для webhook"""
        return {
            "order_id": order_id,
            "status": status,
            "amount": amount,
            "currency": "RUB",
            "paid_at": "2023-01-01T12:00:00Z",
            "type": "payment",
            "custom_fields": {
                "user_id": 123456,
                "plan_months": 1
            }
        }
    
    def test_successful_payment_webhook(self, webhook_handler, mock_services, mock_request):
        """Тест успешной обработки webhook для завершенного платежа"""
        payment_service, notification_service, db_service = mock_services
        
        # Подготавливаем данные
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Настраиваем mock для БД
        mock_payment = Mock()
        mock_payment.user_id = 1
        mock_payment.amount = Decimal('299.00')
        mock_payment.status = 'pending'
        mock_payment.order_id = webhook_data['order_id']
        
        mock_user = Mock()
        mock_user.id = 1
        mock_user.telegram_id = 123456
        
        db_service.get_payment_by_order_id.return_value = mock_payment
        db_service.get_user_by_id.return_value = mock_user
        db_service.create_or_extend_subscription.return_value = True
        
        # Выполняем тест
        result = webhook_handler.handle_lava_webhook()
        
        # Проверяем результат
        assert result['success'] is True
        assert result['status_code'] == 200
        assert 'Payment processed successfully' in result['message']
        
        # Проверяем вызовы методов
        db_service.update_payment_status_by_order_id.assert_called_once()
        db_service.create_or_extend_subscription.assert_called_once_with(
            user_id=1, plan_months=1
        )
    
    def test_failed_payment_webhook(self, webhook_handler, mock_services, mock_request):
        """Тест обработки webhook для неудачного платежа"""
        payment_service, notification_service, db_service = mock_services
        
        # Подготавливаем данные
        webhook_data = self.create_valid_webhook_payload(status="failed")
        webhook_data['error_message'] = 'Insufficient funds'
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Выполняем тест
        result = webhook_handler.handle_lava_webhook()
        
        # Проверяем результат
        assert result['success'] is True
        assert result['status_code'] == 200
        assert 'Failed payment processed' in result['message']
        
        # Проверяем вызов обновления статуса
        db_service.update_payment_status_by_order_id.assert_called_once_with(
            order_id=webhook_data['order_id'],
            status='failed',
            error_message='Insufficient funds'
        )
    
    def test_invalid_signature_rejection(self, webhook_handler, mock_services, mock_request):
        """Тест отклонения webhook с неверной подписью"""
        payment_service, notification_service, db_service = mock_services
        
        # Настраиваем неверную подпись
        payment_service.validate_webhook_signature.return_value = False
        
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Выполняем тест
        result = webhook_handler.handle_lava_webhook()
        
        # Проверяем отклонение
        assert result['success'] is False
        assert result['status_code'] == 401
        assert result['error'] == 'Invalid signature'
        
        # Проверяем, что статистика обновилась
        assert webhook_handler.security_stats['invalid_signatures'] == 1
    
    def test_rate_limiting(self, webhook_handler, mock_services, mock_request):
        """Тест rate limiting для webhook endpoint"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        client_ip = '***********00'
        mock_request.remote_addr = client_ip
        
        # Превышаем лимит запросов
        webhook_handler.RATE_LIMIT_REQUESTS = 2
        
        # Первые два запроса должны пройти
        for i in range(2):
            result = webhook_handler.handle_lava_webhook()
            assert result['success'] is True
        
        # Третий запрос должен быть заблокирован
        result = webhook_handler.handle_lava_webhook()
        assert result['success'] is False
        assert result['status_code'] == 429
        assert result['error'] == 'Rate limit exceeded'
        
        # Проверяем статистику
        assert webhook_handler.security_stats['rate_limited'] == 1
    
    def test_ip_address_validation(self, webhook_handler, mock_services, mock_request):
        """Тест валидации IP адресов"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Тестируем заблокированный IP
        mock_request.remote_addr = '***********00'  # Не в списке разрешенных
        
        result = webhook_handler.handle_lava_webhook()
        
        assert result['success'] is False
        assert result['status_code'] == 403
        assert result['error'] == 'Access denied'
        
        # Проверяем, что IP добавлен в заблокированные
        assert '***********00' in webhook_handler.security_stats['blocked_ips']
    
    def test_payload_size_limit(self, webhook_handler, mock_services, mock_request):
        """Тест ограничения размера payload"""
        payment_service, notification_service, db_service = mock_services
        
        # Создаем слишком большой payload
        large_payload = 'x' * (webhook_handler.MAX_PAYLOAD_SIZE + 1)
        mock_request.get_data.return_value = large_payload
        
        result = webhook_handler.handle_lava_webhook()
        
        assert result['success'] is False
        assert result['status_code'] == 413
        assert result['error'] == 'Payload too large'
        
        # Проверяем статистику
        assert webhook_handler.security_stats['malformed_requests'] == 1
    
    def test_malformed_json_handling(self, webhook_handler, mock_services, mock_request):
        """Тест обработки некорректного JSON"""
        payment_service, notification_service, db_service = mock_services
        
        # Некорректный JSON
        mock_request.get_data.return_value = '{"invalid": json,}'
        
        result = webhook_handler.handle_lava_webhook()
        
        assert result['success'] is False
        assert result['status_code'] == 400
        assert result['error'] == 'Invalid JSON'
        
        # Проверяем статистику
        assert webhook_handler.security_stats['malformed_requests'] == 1
    
    def test_missing_required_fields(self, webhook_handler, mock_services, mock_request):
        """Тест обработки отсутствующих обязательных полей"""
        payment_service, notification_service, db_service = mock_services
        
        # Payload без обязательных полей
        incomplete_data = {"amount": "299.00"}
        payload = json.dumps(incomplete_data)
        mock_request.get_data.return_value = payload
        
        result = webhook_handler.handle_lava_webhook()
        
        assert result['success'] is False
        assert result['status_code'] == 400
        assert 'Missing required fields' in result['error']
        
        # Проверяем статистику
        assert webhook_handler.security_stats['malformed_requests'] == 1
    
    def test_duplicate_webhook_prevention(self, webhook_handler, mock_services, mock_request):
        """Тест предотвращения дублированной обработки webhook"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Настраиваем mock для БД
        mock_payment = Mock()
        mock_payment.user_id = 1
        mock_payment.amount = Decimal('299.00')
        mock_payment.status = 'pending'
        mock_payment.order_id = webhook_data['order_id']
        
        mock_user = Mock()
        mock_user.id = 1
        
        db_service.get_payment_by_order_id.return_value = mock_payment
        db_service.get_user_by_id.return_value = mock_user
        db_service.create_or_extend_subscription.return_value = True
        
        # Первый запрос должен обработаться
        result1 = webhook_handler.handle_lava_webhook()
        assert result1['success'] is True
        
        # Второй идентичный запрос должен быть проигнорирован
        result2 = webhook_handler.handle_lava_webhook()
        assert result2['success'] is True
        assert result2['message'] == 'Already processed'
        
        # Проверяем, что обработка произошла только один раз
        assert db_service.create_or_extend_subscription.call_count == 1
    
    def test_refund_notification_handling(self, webhook_handler, mock_services, mock_request):
        """Тест обработки уведомления о возврате средств"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        webhook_data['type'] = 'refund'
        webhook_data['refund_amount'] = '299.00'
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Настраиваем mock для БД
        mock_payment = Mock()
        mock_payment.user_id = 1
        mock_user = Mock()
        mock_user.id = 1
        mock_user.telegram_id = 123456
        
        db_service.get_payment_by_order_id.return_value = mock_payment
        db_service.get_user_by_id.return_value = mock_user
        
        # Выполняем тест
        result = webhook_handler.handle_lava_webhook()
        
        # Проверяем результат
        assert result['success'] is True
        assert result['status_code'] == 200
        assert result['message'] == 'Refund processed'
        
        # Проверяем вызовы методов
        db_service.update_payment_status_by_order_id.assert_called_once_with(
            order_id=webhook_data['order_id'],
            status='refunded'
        )
        db_service.cancel_user_subscription.assert_called_once_with(1)
    
    def test_chargeback_notification_handling(self, webhook_handler, mock_services, mock_request):
        """Тест обработки уведомления о чарджбэке"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        webhook_data['type'] = 'chargeback'
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Настраиваем mock для БД
        mock_payment = Mock()
        mock_payment.user_id = 1
        mock_user = Mock()
        mock_user.id = 1
        mock_user.telegram_id = 123456
        
        db_service.get_payment_by_order_id.return_value = mock_payment
        db_service.get_user_by_id.return_value = mock_user
        
        # Выполняем тест
        result = webhook_handler.handle_lava_webhook()
        
        # Проверяем результат
        assert result['success'] is True
        assert result['status_code'] == 200
        assert result['message'] == 'Chargeback processed'
        
        # Проверяем вызовы методов
        db_service.update_payment_status_by_order_id.assert_called_once_with(
            order_id=webhook_data['order_id'],
            status='chargeback'
        )
        db_service.cancel_user_subscription.assert_called_once_with(1)
    
    def test_test_notification_handling(self, webhook_handler, mock_services, mock_request):
        """Тест обработки тестового уведомления"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        webhook_data['type'] = 'test'
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Выполняем тест
        result = webhook_handler.handle_lava_webhook()
        
        # Проверяем результат
        assert result['success'] is True
        assert result['status_code'] == 200
        assert result['message'] == 'Test notification received'
        assert result['data']['test'] is True
    
    def test_unknown_notification_type(self, webhook_handler, mock_services, mock_request):
        """Тест обработки неизвестного типа уведомления"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        webhook_data['type'] = 'unknown_type'
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Выполняем тест
        result = webhook_handler.handle_lava_webhook()
        
        # Проверяем результат
        assert result['success'] is True
        assert result['status_code'] == 200
        assert 'Unknown notification type: unknown_type' in result['message']
    
    def test_webhook_stats_collection(self, webhook_handler, mock_services, mock_request):
        """Тест сбора статистики webhook"""
        payment_service, notification_service, db_service = mock_services
        
        # Выполняем несколько запросов с разными результатами
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Успешный запрос
        mock_payment = Mock()
        mock_payment.user_id = 1
        mock_payment.amount = Decimal('299.00')
        mock_payment.status = 'pending'
        mock_payment.order_id = webhook_data['order_id']
        
        mock_user = Mock()
        mock_user.id = 1
        
        db_service.get_payment_by_order_id.return_value = mock_payment
        db_service.get_user_by_id.return_value = mock_user
        db_service.create_or_extend_subscription.return_value = True
        
        webhook_handler.handle_lava_webhook()
        
        # Запрос с неверной подписью
        payment_service.validate_webhook_signature.return_value = False
        webhook_handler.handle_lava_webhook()
        
        # Получаем статистику
        stats = webhook_handler.get_webhook_stats()
        
        # Проверяем статистику
        assert stats['security_stats']['total_requests'] == 2
        assert stats['security_stats']['invalid_signatures'] == 1
        assert stats['processed_webhooks_count'] == 1
    
    def test_order_id_validation(self, webhook_handler, mock_services, mock_request):
        """Тест валидации формата order_id"""
        payment_service, notification_service, db_service = mock_services
        
        # Тест с неверным префиксом
        webhook_data = self.create_valid_webhook_payload(order_id="invalid_123456_1640995200")
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        result = webhook_handler.handle_lava_webhook()
        
        assert result['success'] is False
        assert result['status_code'] == 400
        assert 'Invalid order_id prefix' in result['error']
        
        # Тест с неверным форматом
        webhook_data = self.create_valid_webhook_payload(order_id="tg_invalid")
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        result = webhook_handler.handle_lava_webhook()
        
        assert result['success'] is False
        assert result['status_code'] == 400
        assert 'Invalid order_id format' in result['error']
    
    def test_payment_not_found_handling(self, webhook_handler, mock_services, mock_request):
        """Тест обработки случая, когда платеж не найден в БД"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Платеж не найден в БД
        db_service.get_payment_by_order_id.return_value = None
        
        result = webhook_handler.handle_lava_webhook()
        
        assert result['success'] is False
        assert result['status_code'] == 404
        assert result['error'] == 'Payment not found'
    
    def test_already_processed_payment(self, webhook_handler, mock_services, mock_request):
        """Тест обработки уже завершенного платежа"""
        payment_service, notification_service, db_service = mock_services
        
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Платеж уже обработан
        mock_payment = Mock()
        mock_payment.status = 'completed'
        mock_payment.order_id = webhook_data['order_id']
        
        db_service.get_payment_by_order_id.return_value = mock_payment
        
        result = webhook_handler.handle_lava_webhook()
        
        assert result['success'] is True
        assert result['status_code'] == 200
        assert result['message'] == 'Payment already processed'
    
    def test_x_forwarded_for_header_handling(self, webhook_handler, mock_services, mock_request):
        """Тест обработки заголовка X-Forwarded-For"""
        payment_service, notification_service, db_service = mock_services
        
        # Настраиваем заголовки прокси
        mock_request.headers = {
            'X-Lava-Signature': 'valid_signature',
            'X-Forwarded-For': '127.0.0.1, ***********, 10.0.0.1'
        }
        mock_request.remote_addr = '10.0.0.1'
        
        webhook_data = self.create_valid_webhook_payload()
        payload = json.dumps(webhook_data)
        mock_request.get_data.return_value = payload
        
        # Настраиваем mock для успешной обработки
        mock_payment = Mock()
        mock_payment.user_id = 1
        mock_payment.amount = Decimal('299.00')
        mock_payment.status = 'pending'
        mock_payment.order_id = webhook_data['order_id']
        
        mock_user = Mock()
        mock_user.id = 1
        
        db_service.get_payment_by_order_id.return_value = mock_payment
        db_service.get_user_by_id.return_value = mock_user
        db_service.create_or_extend_subscription.return_value = True
        
        # Выполняем тест
        result = webhook_handler.handle_lava_webhook()
        
        # Должен использовать первый IP из X-Forwarded-For (127.0.0.1)
        assert result['success'] is True
        assert result['status_code'] == 200
    
    def test_rate_limit_cleanup(self, webhook_handler):
        """Тест очистки устаревших записей rate limiting"""
        # Добавляем старые записи
        old_time = time.time() - (webhook_handler.RATE_LIMIT_WINDOW * 3)
        webhook_handler.rate_limit_storage['old_ip'] = {
            'requests': 50,
            'window_start': old_time
        }
        
        current_time = time.time()
        webhook_handler.rate_limit_storage['current_ip'] = {
            'requests': 10,
            'window_start': current_time
        }
        
        # Выполняем очистку
        webhook_handler._cleanup_rate_limit_storage()
        
        # Проверяем, что старая запись удалена, а новая осталась
        assert 'old_ip' not in webhook_handler.rate_limit_storage
        assert 'current_ip' in webhook_handler.rate_limit_storage
    
    def test_security_stats_reset(self, webhook_handler):
        """Тест сброса статистики безопасности"""
        # Устанавливаем некоторые значения
        webhook_handler.security_stats['total_requests'] = 100
        webhook_handler.security_stats['invalid_signatures'] = 5
        webhook_handler.security_stats['blocked_ips'].add('***********')
        
        # Сбрасываем статистику
        webhook_handler.reset_security_stats()
        
        # Проверяем, что все значения сброшены
        assert webhook_handler.security_stats['total_requests'] == 0
        assert webhook_handler.security_stats['invalid_signatures'] == 0
        assert len(webhook_handler.security_stats['blocked_ips']) == 0


class TestWebhookEndpointFlask:
    """Тесты Flask endpoint для webhook"""
    
    @pytest.fixture
    def app(self):
        """Создает тестовое Flask приложение"""
        from app import create_app
        app = create_app()
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def client(self, app):
        """Создает тестовый клиент Flask"""
        return app.test_client()
    
    def create_webhook_signature(self, payload, secret_key="test_secret"):
        """Создает валидную подпись для webhook"""
        return hmac.new(
            secret_key.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    @patch('config.Config.LAVA_SECRET_KEY', 'test_secret')
    def test_webhook_endpoint_success(self, client):
        """Тест успешного вызова webhook endpoint"""
        webhook_data = {
            "order_id": "tg_123456_1640995200",
            "status": "completed",
            "amount": "299.00",
            "currency": "RUB",
            "paid_at": "2023-01-01T12:00:00Z"
        }
        
        payload = json.dumps(webhook_data)
        signature = self.create_webhook_signature(payload)
        
        with patch('app.services.webhook_handler.WebhookHandler.handle_lava_webhook') as mock_handler:
            mock_handler.return_value = {
                'success': True,
                'message': 'Payment processed successfully',
                'status_code': 200
            }
            
            response = client.post('/webhook',
                                 data=payload,
                                 headers={
                                     'Content-Type': 'application/json',
                                     'X-Lava-Signature': signature
                                 })
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] is True
            assert 'Payment processed successfully' in data['message']
    
    def test_webhook_endpoint_error_handling(self, client):
        """Тест обработки ошибок в webhook endpoint"""
        with patch('app.services.webhook_handler.WebhookHandler.handle_lava_webhook') as mock_handler:
            mock_handler.side_effect = Exception("Test error")
            
            response = client.post('/webhook',
                                 data='{"test": "data"}',
                                 headers={'Content-Type': 'application/json'})
            
            assert response.status_code == 500
            data = json.loads(response.data)
            assert data['success'] is False
            assert data['message'] == 'Internal server error'
    
    def test_webhook_test_endpoint(self, client):
        """Тест тестового endpoint для webhook"""
        with patch('app.services.webhook_handler.WebhookHandler.get_webhook_stats') as mock_stats:
            mock_stats.return_value = {
                'processed_webhooks_count': 10,
                'security_stats': {'total_requests': 15}
            }
            
            response = client.get('/webhook/test')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] is True
            assert data['message'] == 'Webhook endpoint is working'
            assert 'stats' in data


if __name__ == '__main__':
    pytest.main([__file__, '-v'])