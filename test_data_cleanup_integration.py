#!/usr/bin/env python3
"""
Интеграционный тест задач очистки данных
"""

import os
import sys
from unittest.mock import patch

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_cleanup_integration():
    """Тест интеграции задач очистки данных"""
    
    # Мокаем переменные окружения для тестирования
    test_env = {
        'TELEGRAM_BOT_TOKEN': 'test_bot_token',
        'LAVA_API_KEY': 'test_lava_key',
        'LAVA_SECRET_KEY': 'test_lava_secret',
        'WEBHOOK_URL': 'https://test.example.com/webhook',
        'CHANNEL_ID': '@test_channel',
        'ADMIN_USER_IDS': '123456789,987654321'
    }
    
    with patch.dict(os.environ, test_env):
        try:
            # Импортируем после установки переменных окружения
            from app.services.scheduler_service import SchedulerService
            from app.models.database import DatabaseService
            from app.services.notification_service import NotificationService
            from app.services.channel_manager import ChannelManager
            from app.services.bot_handler import TelegramBotHandler
            from app.services.payment_service import PaymentService
            
            print("✅ Импорт модулей успешен")
            
            # Создаем сервисы
            db_service = DatabaseService()
            payment_service = PaymentService()
            channel_manager = ChannelManager()
            bot_handler = TelegramBotHandler(db_service, payment_service, channel_manager)
            notification_service = NotificationService(bot_handler, db_service)
            
            # Создаем планировщик
            scheduler_service = SchedulerService(db_service, notification_service, channel_manager)
            print("✅ SchedulerService создан")
            
            # Проверяем наличие методов очистки данных
            cleanup_methods = [
                'cleanup_expired_payments',
                'cleanup_old_admin_logs',
                'create_database_backup',
                'run_cleanup_tasks'
            ]
            
            for method_name in cleanup_methods:
                if hasattr(scheduler_service, method_name):
                    print(f"✅ Метод {method_name} найден")
                else:
                    print(f"❌ Метод {method_name} не найден")
                    return False
            
            # Проверяем методы в DatabaseService
            db_cleanup_methods = [
                'get_expired_payments_older_than_days',
                'delete_payment',
                'get_admin_logs_older_than_days',
                'delete_admin_log'
            ]
            
            for method_name in db_cleanup_methods:
                if hasattr(db_service, method_name):
                    print(f"✅ Метод {method_name} найден в DatabaseService")
                else:
                    print(f"❌ Метод {method_name} не найден в DatabaseService")
                    return False
            
            # Тестируем выполнение задач очистки (без реальной БД)
            try:
                # Эти методы вернут пустые результаты, так как БД не инициализирована
                expired_payments_result = scheduler_service.cleanup_expired_payments()
                admin_logs_result = scheduler_service.cleanup_old_admin_logs()
                
                print(f"✅ Очистка истекших платежей: {expired_payments_result}")
                print(f"✅ Очистка логов администратора: {admin_logs_result}")
                
                # Проверяем структуру результатов
                expected_keys = ['expired_found', 'deleted', 'errors']
                for key in expected_keys:
                    if key not in expired_payments_result:
                        print(f"❌ Отсутствует ключ {key} в результате очистки платежей")
                        return False
                
                expected_keys = ['old_logs_found', 'deleted', 'errors']
                for key in expected_keys:
                    if key not in admin_logs_result:
                        print(f"❌ Отсутствует ключ {key} в результате очистки логов")
                        return False
                
            except Exception as e:
                print(f"⚠️ Ошибка выполнения задач очистки (ожидаемо без БД): {e}")
            
            # Тестируем создание резервной копии (без реальной БД)
            try:
                backup_result = scheduler_service.create_database_backup()
                print(f"✅ Создание резервной копии: {backup_result}")
                
                expected_keys = ['backup_created', 'backup_path', 'backup_size', 'error']
                for key in expected_keys:
                    if key not in backup_result:
                        print(f"❌ Отсутствует ключ {key} в результате создания резервной копии")
                        return False
                        
            except Exception as e:
                print(f"⚠️ Ошибка создания резервной копии (ожидаемо без БД): {e}")
            
            # Тестируем ручное выполнение всех задач
            try:
                all_tasks_result = scheduler_service.run_cleanup_tasks()
                print(f"✅ Выполнение всех задач очистки: {len(all_tasks_result)} результатов")
                
                expected_keys = ['timestamp', 'expired_payments_cleanup', 'admin_logs_cleanup', 'database_backup']
                for key in expected_keys:
                    if key not in all_tasks_result:
                        print(f"❌ Отсутствует ключ {key} в результате выполнения всех задач")
                        return False
                        
            except Exception as e:
                print(f"⚠️ Ошибка выполнения всех задач (ожидаемо без БД): {e}")
            
            print("\n🎉 Интеграционный тест задач очистки данных прошел успешно!")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка интеграционного теста: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🔄 Запуск интеграционного теста задач очистки данных...")
    success = test_data_cleanup_integration()
    sys.exit(0 if success else 1)