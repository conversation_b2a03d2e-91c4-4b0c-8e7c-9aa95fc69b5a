#!/usr/bin/env python3
"""
Главный файл запуска Telegram Payment Bot
"""

from config import Config
import logging

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """Главная функция запуска приложения"""
    try:
        # Проверяем конфигурацию
        Config.validate_config()
        logger.info("Конфигурация проверена успешно")
        
        # TODO: Инициализация Flask приложения и Telegram бота
        logger.info("Telegram Payment Bot готов к запуску")
        
    except ValueError as e:
        logger.error(f"Ошибка конфигурации: {e}")
        return 1
    except Exception as e:
        logger.error(f"Неожиданная ошибка: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())