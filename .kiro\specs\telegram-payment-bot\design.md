# Документ дизайна

## Обзор

Система представляет собой Flask веб-приложение с интегрированным Telegram-ботом, которое обрабатывает платные подписки через Lava.top API. Архитектура построена на модульном принципе с четким разделением ответственности между компонентами обработки платежей, управления ботом и работы с базой данных.

## Архитектура

### Высокоуровневая архитектура

```mermaid
graph TB
    User[Пользователь Telegram] --> Bot[Telegram Bot]
    Bot --> Flask[Flask Web Server]
    Flask --> DB[(SQLite Database)]
    Flask --> Lava[Lava.top API]
    Lava --> Webhook[Webhook Handler]
    Webhook --> Flask
    Flask --> Channel[Telegram Channel]
    
    subgraph "Основные компоненты"
        Bot
        Flask
        DB
        Webhook
    end
```

### Компоненты системы

1. **Telegram Bot Handler** - обрабатывает команды пользователей
2. **Payment Service** - интеграция с Lava.top API
3. **Webhook Handler** - обработка уведомлений о платежах
4. **Database Service** - управление данными подписок
5. **Channel Manager** - управление доступом к каналу
6. **Scheduler** - автоматические задачи (проверка истечения подписок)
7. **Admin Web Panel** - веб-интерфейс для администрирования системы

## Компоненты и интерфейсы

### 1. Telegram Bot Handler (`bot_handler.py`)

**Ответственность:**
- Обработка команд пользователей
- Отображение меню и клавиатур
- Отправка уведомлений

**Основные методы:**
```python
class TelegramBotHandler:
    def handle_start_command(user_id: int)
    def handle_buy_subscription(user_id: int)
    def handle_subscription_status(user_id: int)
    def handle_channel_info(user_id: int)
    def handle_ask_question(user_id: int)
    def send_payment_link(user_id: int, payment_url: str)
    def send_invite_link(user_id: int, invite_url: str)
    def send_expiration_warning(user_id: int, days_left: int)
```

### 2. Payment Service (`payment_service.py`)

**Ответственность:**
- Создание счетов через Lava.top API
- Обработка различных способов оплаты
- Валидация webhook-уведомлений

**Основные методы:**
```python
class PaymentService:
    def create_invoice(user_id: int, plan: str, payment_method: str) -> dict
    def validate_webhook(webhook_data: dict) -> bool
    def process_successful_payment(order_id: str, user_id: int)
    def get_payment_status(order_id: str) -> str
```

**Конфигурация способов оплаты:**
```python
PAYMENT_METHODS = {
    'ru_card': {'service': 'card_ru', 'name': 'Карта РФ'},
    'foreign_card': {'service': 'card_intl', 'name': 'Карта иностранного банка'},
    'crypto': {'service': 'crypto', 'name': 'Криптовалюта'}
}
```

### 3. Database Service (`database.py`)

**Ответственность:**
- Управление схемой базы данных
- CRUD операции для пользователей и подписок
- Транзакционная безопасность

**Схема базы данных:**
```sql
-- Пользователи
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    telegram_id INTEGER UNIQUE NOT NULL,
    username TEXT,
    first_name TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Подписки
CREATE TABLE subscriptions (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    plan_months INTEGER NOT NULL,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Платежи
CREATE TABLE payments (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    order_id TEXT UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    lava_invoice_id TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Логи администратора
CREATE TABLE admin_logs (
    id INTEGER PRIMARY KEY,
    admin_id INTEGER NOT NULL,
    action TEXT NOT NULL,
    target_user_id INTEGER,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Channel Manager (`channel_manager.py`)

**Ответственность:**
- Создание пригласительных ссылок
- Добавление/удаление пользователей из канала
- Проверка статуса участников

**Основные методы:**
```python
class ChannelManager:
    def create_invite_link(user_id: int) -> str
    def remove_user_from_channel(user_id: int) -> bool
    def check_user_in_channel(user_id: int) -> bool
    def get_channel_info() -> dict
```

### 5. Scheduler Service (`scheduler.py`)

**Ответственность:**
- Проверка истекающих подписок
- Отправка уведомлений
- Автоматическое удаление пользователей

**Задачи:**
```python
class SchedulerService:
    def check_expiring_subscriptions()  # Каждый час
    def send_expiration_warnings()      # Каждый день в 10:00
    def cleanup_expired_subscriptions() # Каждый час
    def cleanup_old_payments()          # Каждый день в 02:00
```

### 6. Admin Web Panel (`admin_panel.py`)

**Ответственность:**
- Веб-интерфейс для администрирования системы
- Управление пользователями и подписками
- Мониторинг системы и финансовая отчетность
- Настройки системы

**Основные компоненты:**
```python
class AdminPanel:
    def authenticate_admin(username: str, password: str) -> bool
    def get_dashboard_stats() -> dict
    def get_users_list(page: int, search: str) -> list
    def get_user_details(user_id: int) -> dict
    def manage_subscription(user_id: int, action: str) -> bool
    def get_payments_list(filters: dict) -> list
    def get_financial_reports(period: str) -> dict
    def get_system_logs(level: str, date_range: tuple) -> list
    def update_system_settings(settings: dict) -> bool
```

**Страницы админ-панели:**
1. **Dashboard** - общая статистика и метрики
2. **Users** - управление пользователями
3. **Subscriptions** - управление подписками
4. **Payments** - просмотр и управление платежами
5. **Reports** - финансовые отчеты и аналитика
6. **System** - логи, мониторинг, настройки
7. **Settings** - конфигурация системы

## Модели данных

### User Model
```python
@dataclass
class User:
    id: int
    telegram_id: int
    username: str
    first_name: str
    created_at: datetime
```

### Subscription Model
```python
@dataclass
class Subscription:
    id: int
    user_id: int
    plan_months: int
    start_date: datetime
    end_date: datetime
    status: str  # 'active', 'expired', 'cancelled'
    created_at: datetime
```

### Payment Model
```python
@dataclass
class Payment:
    id: int
    user_id: int
    order_id: str
    amount: Decimal
    payment_method: str
    status: str  # 'pending', 'completed', 'failed', 'expired'
    lava_invoice_id: str
    created_at: datetime
    completed_at: datetime
```

## Обработка ошибок

### Стратегия обработки ошибок

1. **Lava.top API ошибки:**
   - Повторные попытки с экспоненциальной задержкой
   - Логирование всех API вызовов
   - Уведомление пользователя о временных проблемах

2. **Telegram API ошибки:**
   - Обработка rate limiting
   - Повторные попытки для критических операций
   - Альтернативные способы уведомления

3. **База данных ошибки:**
   - Автоматическое создание резервных копий
   - Откат транзакций при ошибках
   - Восстановление из резервных копий

### Логирование

```python
import logging

# Конфигурация логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)

# Отдельные логгеры для компонентов
payment_logger = logging.getLogger('payment')
bot_logger = logging.getLogger('bot')
webhook_logger = logging.getLogger('webhook')
```

## Стратегия тестирования

### Модульные тесты

1. **Payment Service тесты:**
   - Создание счетов для разных способов оплаты
   - Валидация webhook данных
   - Обработка ошибок API

2. **Database Service тесты:**
   - CRUD операции
   - Транзакционная целостность
   - Миграции схемы

3. **Bot Handler тесты:**
   - Обработка команд
   - Генерация ответов
   - Клавиатуры и меню

### Интеграционные тесты

1. **Полный цикл оплаты:**
   - Создание счета → Webhook → Добавление в канал
   - Тестирование с mock Lava.top API

2. **Управление подписками:**
   - Истечение подписки → Удаление из канала
   - Продление подписки → Обновление доступа

### Тестовая конфигурация

```python
# test_config.py
TEST_CONFIG = {
    'TELEGRAM_TOKEN': 'test_token',
    'LAVA_API_KEY': 'test_lava_key',
    'CHANNEL_ID': '@test_channel',
    'DATABASE_URL': 'sqlite:///test.db',
    'WEBHOOK_URL': 'https://test.example.com/webhook'
}
```

## Конфигурация и развертывание

### Переменные окружения

```bash
# .env файл
TELEGRAM_BOT_TOKEN=your_bot_token
LAVA_API_KEY=your_lava_api_key
LAVA_SECRET_KEY=your_lava_secret_key
CHANNEL_ID=@your_channel
ADMIN_USER_IDS=123456789,987654321
DATABASE_URL=sqlite:///subscriptions.db
WEBHOOK_URL=https://yourdomain.com/webhook
FLASK_SECRET_KEY=your_flask_secret_key
```

### Структура проекта

```
telegram-payment-bot/
├── app.py                 # Главный Flask приложение
├── bot_handler.py         # Обработчик Telegram бота
├── payment_service.py     # Сервис платежей
├── database.py           # Работа с базой данных
├── channel_manager.py    # Управление каналом
├── scheduler.py          # Планировщик задач
├── admin_panel.py        # Веб-админ панель
├── config.py             # Конфигурация
├── requirements.txt      # Зависимости Python
├── .env                  # Переменные окружения
├── templates/           # HTML шаблоны для админ-панели
│   ├── base.html
│   ├── dashboard.html
│   ├── users.html
│   ├── subscriptions.html
│   ├── payments.html
│   ├── reports.html
│   ├── system.html
│   └── settings.html
├── static/              # Статические файлы (CSS, JS)
│   ├── css/
│   │   └── admin.css
│   └── js/
│       └── admin.js
├── tests/               # Тесты
│   ├── test_payment.py
│   ├── test_database.py
│   ├── test_bot.py
│   └── test_admin_panel.py
└── migrations/          # Миграции БД
    └── init.sql
```

### Зависимости

```txt
Flask==2.3.3
Flask-Login==0.6.3
Flask-WTF==1.2.1
WTForms==3.1.0
pyTelegramBotAPI==4.14.0
requests==2.31.0
sqlite3
APScheduler==3.10.4
python-dotenv==1.0.0
cryptography==41.0.7
Jinja2==3.1.2
MarkupSafe==2.1.3
```

## Безопасность

### Меры безопасности

1. **Webhook валидация:**
   - Проверка подписи Lava.top
   - Валидация IP адресов
   - Предотвращение replay атак

2. **Данные пользователей:**
   - Шифрование чувствительных данных
   - Минимизация хранимой информации
   - Регулярная очистка старых данных

3. **API безопасность:**
   - Rate limiting для webhook
   - Валидация входных данных
   - Защита от SQL инъекций

### Мониторинг

```python
# Метрики для мониторинга
METRICS = {
    'successful_payments': 0,
    'failed_payments': 0,
    'active_subscriptions': 0,
    'webhook_errors': 0,
    'api_errors': 0
}
```