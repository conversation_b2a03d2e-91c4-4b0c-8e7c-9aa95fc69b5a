#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Admin Panel for Rinadzhi Telegram Payment Bot
Minimal Flask app without circular imports
"""

import os
import sqlite3
from datetime import datetime
from flask import Flask, render_template_string, request, jsonify, redirect, url_for, session
from werkzeug.security import check_password_hash, generate_password_hash

app = Flask(__name__)
app.secret_key = 'rinadzhi_admin_secret_key_2025'

# Database path
DB_PATH = '/home/<USER>/app/payments.db'

# Admin credentials (in production, store in database)
ADMIN_USERNAME = 'admin'
ADMIN_PASSWORD_HASH = generate_password_hash('admin123')  # Change this!

def get_db_connection():
    """Get database connection"""
    return sqlite3.connect(DB_PATH)

def get_stats():
    """Get basic statistics"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Total users
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]
        
        # Active subscriptions
        cursor.execute("SELECT COUNT(*) FROM subscriptions WHERE is_active = 1 AND end_date > datetime('now')")
        active_subs = cursor.fetchone()[0]
        
        # Total payments
        cursor.execute("SELECT COUNT(*) FROM payments")
        total_payments = cursor.fetchone()[0]
        
        # Revenue
        cursor.execute("SELECT SUM(amount) FROM payments WHERE status = 'completed'")
        revenue = cursor.fetchone()[0] or 0
        
        conn.close()
        return {
            'total_users': total_users,
            'active_subscriptions': active_subs,
            'total_payments': total_payments,
            'revenue': revenue
        }
    except Exception as e:
        return {
            'total_users': 0,
            'active_subscriptions': 0,
            'total_payments': 0,
            'revenue': 0,
            'error': str(e)
        }

def get_recent_users(limit=10):
    """Get recent users"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT user_id, username, first_name, created_at 
            FROM users 
            ORDER BY created_at DESC 
            LIMIT ?
        """, (limit,))
        users = cursor.fetchall()
        conn.close()
        return users
    except Exception as e:
        return []

def get_recent_payments(limit=10):
    """Get recent payments"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT p.user_id, p.amount, p.plan_type, p.status, p.created_at, u.username
            FROM payments p
            LEFT JOIN users u ON p.user_id = u.user_id
            ORDER BY p.created_at DESC 
            LIMIT ?
        """, (limit,))
        payments = cursor.fetchall()
        conn.close()
        return payments
    except Exception as e:
        return []

# HTML Templates
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Rinadzhi Admin Login</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 50px; }
        .login-form { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #0056b3; }
        .error { color: red; margin-top: 10px; }
        h2 { text-align: center; color: #333; }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>🔐 Rinadzhi Admin Panel</h2>
        <form method="POST">
            <div class="form-group">
                <label>Username:</label>
                <input type="text" name="username" required>
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" name="password" required>
            </div>
            <button type="submit">Login</button>
            {% if error %}
                <div class="error">{{ error }}</div>
            {% endif %}
        </form>
    </div>
</body>
</html>
"""

DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Rinadzhi Admin Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .header { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        .section { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
        .logout { float: right; color: #dc3545; text-decoration: none; }
        .logout:hover { text-decoration: underline; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Rinadzhi Bot Admin Dashboard</h1>
        <a href="/logout" class="logout">Logout</a>
    </div>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_users }}</div>
            <div class="stat-label">Total Users</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.active_subscriptions }}</div>
            <div class="stat-label">Active Subscriptions</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_payments }}</div>
            <div class="stat-label">Total Payments</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.revenue }}₽</div>
            <div class="stat-label">Revenue</div>
        </div>
    </div>

    <div class="section">
        <h2>📊 Recent Users</h2>
        <table>
            <tr>
                <th>User ID</th>
                <th>Username</th>
                <th>Name</th>
                <th>Registered</th>
            </tr>
            {% for user in recent_users %}
            <tr>
                <td>{{ user[0] }}</td>
                <td>@{{ user[1] or 'N/A' }}</td>
                <td>{{ user[2] or 'N/A' }}</td>
                <td>{{ user[3] }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>

    <div class="section">
        <h2>💳 Recent Payments</h2>
        <table>
            <tr>
                <th>User ID</th>
                <th>Username</th>
                <th>Amount</th>
                <th>Plan</th>
                <th>Status</th>
                <th>Date</th>
            </tr>
            {% for payment in recent_payments %}
            <tr>
                <td>{{ payment[0] }}</td>
                <td>@{{ payment[5] or 'N/A' }}</td>
                <td>{{ payment[1] }}₽</td>
                <td>{{ payment[2] }}</td>
                <td>{{ payment[3] }}</td>
                <td>{{ payment[4] }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>

    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
"""

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username == ADMIN_USERNAME and check_password_hash(ADMIN_PASSWORD_HASH, password):
            session['logged_in'] = True
            return redirect(url_for('dashboard'))
        else:
            return render_template_string(LOGIN_TEMPLATE, error="Invalid credentials")
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.pop('logged_in', None)
    return redirect(url_for('login'))

@app.route('/')
@app.route('/admin')
@app.route('/admin/dashboard')
def dashboard():
    if not session.get('logged_in'):
        return redirect(url_for('login'))
    
    stats = get_stats()
    recent_users = get_recent_users()
    recent_payments = get_recent_payments()
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                stats=stats, 
                                recent_users=recent_users, 
                                recent_payments=recent_payments)

@app.route('/api/stats')
def api_stats():
    if not session.get('logged_in'):
        return jsonify({'error': 'Not authenticated'}), 401
    
    return jsonify(get_stats())

@app.route('/health')
def health():
    return jsonify({'status': 'ok', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
