#!/usr/bin/env python3
"""
Тест для проверки входа в админ-панель
"""

import requests
import sys

def test_admin_login():
    """Тестирует вход в админ-панель"""
    base_url = "https://195.49.212.172"
    
    # Создаем сессию
    session = requests.Session()
    session.verify = False  # Отключаем проверку SSL для тестирования
    
    try:
        # Получаем страницу входа
        print("Получаем страницу входа...")
        login_page = session.get(f"{base_url}/admin/login")
        print(f"Статус страницы входа: {login_page.status_code}")
        
        if login_page.status_code != 200:
            print(f"Ошибка получения страницы входа: {login_page.text}")
            return False
        
        # Ищем CSRF токен в HTML
        csrf_token = None
        if 'csrf_token' in login_page.text:
            import re
            csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
            if csrf_match:
                csrf_token = csrf_match.group(1)
                print(f"CSRF токен найден: {csrf_token[:20]}...")
        
        # Подготавливаем данные для входа
        login_data = {
            'password': 'admin123',
            'submit': 'Войти'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        # Отправляем POST запрос для входа
        print("Отправляем данные для входа...")
        login_response = session.post(
            f"{base_url}/admin/login",
            data=login_data,
            allow_redirects=False
        )
        
        print(f"Статус ответа: {login_response.status_code}")
        print(f"Заголовки ответа: {dict(login_response.headers)}")
        
        if login_response.status_code == 302:
            print("Успешное перенаправление - вход выполнен!")
            location = login_response.headers.get('Location', '')
            print(f"Перенаправление на: {location}")
            return True
        else:
            print(f"Ошибка входа. Ответ сервера:")
            print(login_response.text[:500])
            return False
            
    except Exception as e:
        print(f"Ошибка при тестировании входа: {e}")
        return False

if __name__ == "__main__":
    success = test_admin_login()
    sys.exit(0 if success else 1)