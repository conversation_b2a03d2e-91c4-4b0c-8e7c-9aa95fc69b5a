# PowerShell script to upload .env file with password
param(
    [string]$Password = ""
)

$serverIP = "**************"
$username = "root"
$localFile = "server_env_file"
$remotePath = "/root/.env"

if ([string]::IsNullOrEmpty($Password)) {
    $Password = Read-Host "Enter server password" -AsSecureString
    $Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password))
}

Write-Host "Uploading .env file to server..."

try {
    # Method 1: Try using pscp (PuTTY's scp)
    if (Get-Command pscp -ErrorAction SilentlyContinue) {
        Write-Host "Using pscp..."
        & pscp -pw $Password -o StrictHostKeyChecking=no $localFile "${username}@${serverIP}:${remotePath}"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "File uploaded successfully with pscp!"
            
            # Restart services
            Write-Host "Restarting services..."
            & plink -pw $Password "${username}@${serverIP}" "systemctl restart telegram-bot && systemctl restart nginx && echo 'Services restarted'"
            
            Write-Host "Update completed successfully!"
            exit 0
        }
    }
    
    # Method 2: Try using WinSCP if available
    if (Get-Command winscp -ErrorAction SilentlyContinue) {
        Write-Host "Using WinSCP..."
        $winscpScript = @"
open sftp://${username}:${Password}@${serverIP}
put $localFile $remotePath
exit
"@
        $winscpScript | winscp /script=-
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "File uploaded successfully with WinSCP!"
            exit 0
        }
    }
    
    # Method 3: Create expect-like script
    Write-Host "Using expect-like approach..."
    $expectScript = @"
spawn scp -o StrictHostKeyChecking=no $localFile ${username}@${serverIP}:${remotePath}
expect "password:"
send "${Password}\r"
expect eof
"@
    
    # Save expect script and run if expect is available
    $expectScript | Out-File -FilePath "temp_expect.exp" -Encoding ASCII
    
    if (Get-Command expect -ErrorAction SilentlyContinue) {
        expect temp_expect.exp
        Remove-Item temp_expect.exp -Force
    } else {
        Write-Host "Expect not available. Please install PuTTY tools or use manual upload."
        Write-Host "Manual command: scp -o StrictHostKeyChecking=no $localFile ${username}@${serverIP}:${remotePath}"
        Write-Host "Password: [Enter your server password when prompted]"
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Please try manual upload or install PuTTY tools."
}

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")