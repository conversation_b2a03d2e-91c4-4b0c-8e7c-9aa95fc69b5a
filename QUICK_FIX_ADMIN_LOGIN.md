# 🚨 БЫСТРОЕ РЕШЕНИЕ: Проблема входа в админ-панель

## Проблема
Не удается войти в админ-панель - форма не отправляется при вводе правильного пароля.

## Причина
Настройка `SESSION_COOKIE_SECURE=true` блокирует cookies для HTTP соединений.

## ⚡ Быстрое решение

### Вариант 1: Изменение config.py (рекомендуется)
```bash
sudo sed -i "s/SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'true')/SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'false')/" /home/<USER>/app/config.py
```

### Вариант 2: Через переменную окружения
```bash
echo "SESSION_COOKIE_SECURE=false" >> /home/<USER>/app/.env
```

### Перезапуск приложения
```bash
# Если используется systemd
sudo systemctl restart telegram-payment-bot

# Если используется gunicorn напрямую
sudo pkill -f gunicorn
sudo bash -c "cd /home/<USER>/app && nohup sudo -u telegrambot /home/<USER>/app/venv/bin/gunicorn --bind 0.0.0.0:5000 --workers 2 --pid /tmp/gunicorn.pid wsgi:app > /var/log/telegram-payment-bot/gunicorn.log 2>&1 &"
```

## ✅ Проверка решения
```bash
# Проверьте настройку
grep "SESSION_COOKIE_SECURE" /home/<USER>/app/config.py

# Тест входа
curl -I http://localhost:5000/admin/login
```

## 🔒 Для Production с HTTPS
Если у вас настроен SSL, верните `SESSION_COOKIE_SECURE=true` для безопасности:
```bash
echo "SESSION_COOKIE_SECURE=true" >> /home/<USER>/app/.env
```

## 📚 Подробная документация
- [TROUBLESHOOTING.md](TROUBLESHOOTING.md) - полное руководство по устранению неполадок
- [DEPLOYMENT_INSTRUCTIONS.md](DEPLOYMENT_INSTRUCTIONS.md) - инструкции по развертыванию
