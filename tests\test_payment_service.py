import pytest
import json
import time
import hmac
import hashlib
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal
from datetime import datetime, timedelta

from app.services.payment_service import PaymentService


class TestPaymentService:
    """Тесты для PaymentService"""
    
    @pytest.fixture
    def payment_service(self):
        """Фикстура для создания экземпляра PaymentService"""
        with patch('app.services.payment_service.Config') as mock_config:
            mock_config.LAVA_API_KEY = 'test_api_key'
            mock_config.LAVA_SECRET_KEY = 'test_secret_key'
            mock_config.LAVA_BASE_URL = 'https://api.test.lava.top'
            mock_config.WEBHOOK_URL = 'https://test.example.com'
            
            service = PaymentService()
            return service
    
    @pytest.fixture
    def mock_successful_api_response(self):
        """Фикстура для успешного ответа API"""
        return {
            'status': 'success',
            'data': {
                'id': 'lava_invoice_123',
                'url': 'https://pay.lava.top/invoice/123',
                'amount': 299.00,
                'currency': 'RUB',
                'status': 'pending'
            }
        }
    
    @pytest.fixture
    def mock_error_api_response(self):
        """Фикстура для ошибочного ответа API"""
        return {
            'status': 'error',
            'message': 'Invalid API key',
            'code': 'INVALID_API_KEY'
        }
    
    def test_init_payment_service(self, payment_service):
        """Тест инициализации PaymentService"""
        assert payment_service.api_key == 'test_api_key'
        assert payment_service.secret_key == 'test_secret_key'
        assert payment_service.base_url == 'https://api.test.lava.top'
        assert payment_service.webhook_url == 'https://test.example.com'
        assert 'Authorization' in payment_service.session.headers
        assert payment_service.session.headers['Authorization'] == 'Bearer test_api_key'
    
    def test_subscription_plans_configuration(self):
        """Тест конфигурации тарифных планов"""
        plans = PaymentService.SUBSCRIPTION_PLANS
        
        assert len(plans) == 4
        assert 1 in plans and plans[1]['months'] == 1 and plans[1]['price'] == Decimal('299.00')
        assert 3 in plans and plans[3]['months'] == 3 and plans[3]['price'] == Decimal('799.00')
        assert 6 in plans and plans[6]['months'] == 6 and plans[6]['price'] == Decimal('1499.00')
        assert 12 in plans and plans[12]['months'] == 12 and plans[12]['price'] == Decimal('2799.00')
    
    def test_payment_methods_configuration(self):
        """Тест конфигурации способов оплаты"""
        methods = PaymentService.PAYMENT_METHODS
        
        assert len(methods) == 3
        assert 'ru_card' in methods and methods['ru_card']['service'] == 'card_ru'
        assert 'foreign_card' in methods and methods['foreign_card']['service'] == 'card_intl'
        assert 'crypto' in methods and methods['crypto']['service'] == 'crypto'
    
    @patch('app.services.payment_service.PaymentService._make_api_request')
    def test_create_invoice_success(self, mock_api_request, payment_service, mock_successful_api_response):
        """Тест успешного создания счета"""
        mock_api_request.return_value = mock_successful_api_response
        
        result = payment_service.create_invoice(
            user_id=123456,
            plan_months=1,
            payment_method='ru_card'
        )
        
        assert result['success'] is True
        assert 'order_id' in result
        assert result['order_id'].startswith('tg_123456_')
        assert result['invoice_id'] == 'lava_invoice_123'
        assert result['payment_url'] == 'https://pay.lava.top/invoice/123'
        assert result['amount'] == Decimal('299.00')
        assert result['currency'] == 'RUB'
        assert result['payment_method'] == 'ru_card'
        assert result['plan_months'] == 1
        assert isinstance(result['expires_at'], datetime)
        
        # Проверяем, что API был вызван с правильными параметрами
        mock_api_request.assert_called_once()
        call_args = mock_api_request.call_args
        assert call_args[0][0] == 'POST'
        assert call_args[0][1] == '/invoice/create'
        
        invoice_data = call_args[0][2]
        assert invoice_data['amount'] == 299.0
        assert invoice_data['currency'] == 'RUB'
        assert invoice_data['service'] == 'card_ru'
        assert invoice_data['custom_fields']['user_id'] == 123456
        assert invoice_data['custom_fields']['plan_months'] == 1
        assert invoice_data['custom_fields']['payment_method'] == 'ru_card'
    
    @patch('app.services.payment_service.PaymentService._make_api_request')
    def test_create_invoice_different_plans(self, mock_api_request, payment_service, mock_successful_api_response):
        """Тест создания счетов для разных тарифных планов"""
        mock_api_request.return_value = mock_successful_api_response
        
        # Тест для 3-месячного плана
        result = payment_service.create_invoice(123456, 3, 'foreign_card')
        assert result['success'] is True
        assert result['plan_months'] == 3
        
        call_args = mock_api_request.call_args[0][2]
        assert call_args['amount'] == 799.0
        assert call_args['service'] == 'card_intl'
        
        # Тест для 12-месячного плана с криптовалютой
        result = payment_service.create_invoice(123456, 12, 'crypto')
        assert result['success'] is True
        assert result['plan_months'] == 12
        
        call_args = mock_api_request.call_args[0][2]
        assert call_args['amount'] == 2799.0
        assert call_args['service'] == 'crypto'
    
    def test_create_invoice_invalid_plan(self, payment_service):
        """Тест создания счета с неподдерживаемым планом"""
        result = payment_service.create_invoice(123456, 5, 'ru_card')
        
        assert result['success'] is False
        assert 'Неподдерживаемый план подписки' in result['error']
        assert result['error_code'] == 'VALIDATION_ERROR'
    
    def test_create_invoice_invalid_payment_method(self, payment_service):
        """Тест создания счета с неподдерживаемым способом оплаты"""
        result = payment_service.create_invoice(123456, 1, 'invalid_method')
        
        assert result['success'] is False
        assert 'Неподдерживаемый способ оплаты' in result['error']
        assert result['error_code'] == 'VALIDATION_ERROR'
    
    @patch('app.services.payment_service.PaymentService._make_api_request')
    def test_create_invoice_api_error(self, mock_api_request, payment_service, mock_error_api_response):
        """Тест обработки ошибки API при создании счета"""
        mock_api_request.return_value = mock_error_api_response
        
        result = payment_service.create_invoice(123456, 1, 'ru_card')
        
        assert result['success'] is False
        assert result['error'] == 'Invalid API key'
        assert result['error_code'] == 'INVALID_API_KEY'
    
    @patch('app.services.payment_service.PaymentService._make_api_request')
    def test_get_payment_status_success(self, mock_api_request, payment_service):
        """Тест успешного получения статуса платежа"""
        mock_response = {
            'status': 'success',
            'data': {
                'id': 'lava_invoice_123',
                'status': 'completed',
                'amount': 299.0,
                'currency': 'RUB',
                'paid_at': '2024-01-15T10:30:00Z'
            }
        }
        mock_api_request.return_value = mock_response
        
        result = payment_service.get_payment_status('tg_123456_1642234567')
        
        assert result['success'] is True
        assert result['order_id'] == 'tg_123456_1642234567'
        assert result['status'] == 'completed'
        assert result['amount'] == 299.0
        assert result['currency'] == 'RUB'
        assert result['paid_at'] == '2024-01-15T10:30:00Z'
        assert result['invoice_id'] == 'lava_invoice_123'
        
        mock_api_request.assert_called_once_with('GET', '/invoice/status/tg_123456_1642234567')
    
    @patch('app.services.payment_service.PaymentService._make_api_request')
    def test_get_payment_status_error(self, mock_api_request, payment_service):
        """Тест обработки ошибки при получении статуса платежа"""
        mock_api_request.return_value = {
            'status': 'error',
            'message': 'Invoice not found',
            'code': 'INVOICE_NOT_FOUND'
        }
        
        result = payment_service.get_payment_status('invalid_order_id')
        
        assert result['success'] is False
        assert result['error'] == 'Invoice not found'
        assert result['error_code'] == 'INVOICE_NOT_FOUND'
    
    def test_validate_webhook_signature_valid(self, payment_service):
        """Тест валидации правильной подписи webhook"""
        payload = '{"order_id": "test_order", "status": "completed"}'
        
        # Создаем правильную подпись
        expected_signature = hmac.new(
            payment_service.secret_key.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        result = payment_service.validate_webhook_signature(payload, expected_signature)
        assert result is True
    
    def test_validate_webhook_signature_invalid(self, payment_service):
        """Тест валидации неправильной подписи webhook"""
        payload = '{"order_id": "test_order", "status": "completed"}'
        invalid_signature = 'invalid_signature_hash'
        
        result = payment_service.validate_webhook_signature(payload, invalid_signature)
        assert result is False
    
    @patch('requests.Session.post')
    def test_make_api_request_success(self, mock_post, payment_service):
        """Тест успешного API запроса"""
        mock_response = Mock()
        mock_response.json.return_value = {'status': 'success', 'data': {}}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        result = payment_service._make_api_request('POST', '/test', {'test': 'data'})
        
        assert result == {'status': 'success', 'data': {}}
        mock_post.assert_called_once()
    
    @patch('requests.Session.get')
    def test_make_api_request_get_success(self, mock_get, payment_service):
        """Тест успешного GET API запроса"""
        mock_response = Mock()
        mock_response.json.return_value = {'status': 'success', 'data': {}}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = payment_service._make_api_request('GET', '/test')
        
        assert result == {'status': 'success', 'data': {}}
        mock_get.assert_called_once()
    
    @patch('requests.Session.post')
    @patch('time.sleep')
    def test_make_api_request_retry_on_timeout(self, mock_sleep, mock_post, payment_service):
        """Тест повторных попыток при таймауте"""
        import requests
        
        # Первые два вызова - таймаут, третий - успех
        mock_post.side_effect = [
            requests.exceptions.Timeout(),
            requests.exceptions.Timeout(),
            Mock(json=lambda: {'status': 'success'}, raise_for_status=lambda: None)
        ]
        
        result = payment_service._make_api_request('POST', '/test', {'test': 'data'}, max_retries=2)
        
        assert result == {'status': 'success'}
        assert mock_post.call_count == 3
        assert mock_sleep.call_count == 2
    
    @patch('requests.Session.post')
    def test_make_api_request_max_retries_exceeded(self, mock_post, payment_service):
        """Тест превышения максимального количества повторных попыток"""
        import requests
        
        mock_post.side_effect = requests.exceptions.Timeout()
        
        result = payment_service._make_api_request('POST', '/test', {'test': 'data'}, max_retries=1)
        
        assert result['status'] == 'error'
        assert result['message'] == 'Таймаут запроса к платежной системе'
        assert result['code'] == 'TIMEOUT_ERROR'
        assert mock_post.call_count == 2  # Исходный вызов + 1 повтор
    
    @patch('requests.Session.post')
    def test_make_api_request_http_error(self, mock_post, payment_service):
        """Тест обработки HTTP ошибок"""
        import requests
        
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {'message': 'Bad Request', 'code': 'BAD_REQUEST'}
        mock_response.text = 'Bad Request'
        
        http_error = requests.exceptions.HTTPError(response=mock_response)
        mock_post.side_effect = http_error
        
        result = payment_service._make_api_request('POST', '/test', {'test': 'data'})
        
        assert result['status'] == 'error'
        assert result['message'] == 'Bad Request'
        assert result['code'] == 'BAD_REQUEST'
    
    def test_get_available_payment_methods(self):
        """Тест получения доступных способов оплаты"""
        methods = PaymentService.get_available_payment_methods()
        
        assert len(methods) == 3
        assert 'ru_card' in methods
        assert 'foreign_card' in methods
        assert 'crypto' in methods
        
        # Проверяем, что возвращается копия, а не оригинальный словарь
        methods['test'] = 'test'
        assert 'test' not in PaymentService.PAYMENT_METHODS
    
    def test_get_subscription_plans(self):
        """Тест получения тарифных планов"""
        plans = PaymentService.get_subscription_plans()
        
        assert len(plans) == 4
        assert 1 in plans and 3 in plans and 6 in plans and 12 in plans
        
        # Проверяем, что возвращается копия
        plans[99] = {'test': 'test'}
        assert 99 not in PaymentService.SUBSCRIPTION_PLANS
    
    def test_calculate_plan_price(self):
        """Тест расчета цены тарифного плана"""
        assert PaymentService.calculate_plan_price(1) == Decimal('299.00')
        assert PaymentService.calculate_plan_price(3) == Decimal('799.00')
        assert PaymentService.calculate_plan_price(6) == Decimal('1499.00')
        assert PaymentService.calculate_plan_price(12) == Decimal('2799.00')
        assert PaymentService.calculate_plan_price(99) is None
    
    @patch('app.services.payment_service.PaymentService._make_api_request')
    def test_create_invoice_order_id_format(self, mock_api_request, payment_service, mock_successful_api_response):
        """Тест формата order_id"""
        mock_api_request.return_value = mock_successful_api_response
        
        with patch('time.time', return_value=1642234567):
            result = payment_service.create_invoice(123456, 1, 'ru_card')
            
            assert result['success'] is True
            assert result['order_id'] == 'tg_123456_1642234567'
    
    @patch('app.services.payment_service.PaymentService._make_api_request')
    def test_create_invoice_expiration_time(self, mock_api_request, payment_service, mock_successful_api_response):
        """Тест времени истечения счета (1 час)"""
        mock_api_request.return_value = mock_successful_api_response
        
        with patch('app.services.payment_service.datetime') as mock_datetime:
            mock_now = datetime(2024, 1, 15, 10, 0, 0)
            mock_datetime.now.return_value = mock_now
            mock_datetime.fromtimestamp.side_effect = datetime.fromtimestamp
            
            result = payment_service.create_invoice(123456, 1, 'ru_card')
            
            # Проверяем, что время истечения установлено на 1 час вперед
            call_args = mock_api_request.call_args[0][2]
            expected_expire_time = int((mock_now + timedelta(hours=1)).timestamp())
            assert call_args['expire'] == expected_expire_time

    # ==================== ТЕСТЫ ПРОВЕРКИ СТАТУСА ПЛАТЕЖЕЙ ====================
    
    @patch('app.services.payment_service.PaymentService.get_payment_status')
    @patch('time.sleep')
    def test_check_stuck_payments_success(self, mock_sleep, mock_get_status, payment_service):
        """Тест успешной проверки зависших платежей"""
        # Мокаем базу данных
        mock_db_service = Mock()
        
        # Создаем тестовые зависшие платежи
        stuck_payment = Mock()
        stuck_payment.lava_invoice_id = 'tg_123456_1642234567'
        stuck_payment.status = 'pending'
        stuck_payment.amount = 299.0
        stuck_payment.user_id = 1
        
        payment_service._get_stuck_payments = Mock(return_value=[stuck_payment])
        
        # Мокаем успешный ответ API
        mock_get_status.return_value = {
            'success': True,
            'status': 'completed',
            'paid_at': '2024-01-15T10:30:00Z'
        }
        
        # Мокаем успешное обновление в БД
        mock_db_service.update_payment_status.return_value = True
        mock_db_service.get_user_by_telegram_id.return_value = Mock(id=1)
        mock_db_service.create_or_extend_subscription.return_value = True
        
        # Мокаем определение плана
        payment_service._determine_plan_by_amount = Mock(return_value=1)
        
        result = payment_service.check_stuck_payments(mock_db_service)
        
        assert result['success'] is True
        assert result['checked_count'] == 1
        assert result['updated_count'] == 1
        assert len(result['errors']) == 0
        
        mock_get_status.assert_called_once_with('tg_123456_1642234567')
        mock_db_service.update_payment_status.assert_called_once()
        mock_sleep.assert_called_once_with(0.5)
    
    @patch('app.services.payment_service.PaymentService.get_payment_status')
    def test_check_stuck_payments_failed_status(self, mock_get_status, payment_service):
        """Тест обработки неудачного статуса платежа"""
        mock_db_service = Mock()
        
        stuck_payment = Mock()
        stuck_payment.lava_invoice_id = 'tg_123456_1642234567'
        stuck_payment.status = 'pending'
        
        payment_service._get_stuck_payments = Mock(return_value=[stuck_payment])
        
        # Мокаем статус 'failed'
        mock_get_status.return_value = {
            'success': True,
            'status': 'failed'
        }
        
        mock_db_service.update_payment_status.return_value = True
        
        result = payment_service.check_stuck_payments(mock_db_service)
        
        assert result['success'] is True
        assert result['updated_count'] == 1
        mock_db_service.update_payment_status.assert_called_once_with('tg_123456_1642234567', 'failed')
    
    @patch('app.services.payment_service.PaymentService.get_payment_status')
    def test_check_stuck_payments_api_error(self, mock_get_status, payment_service):
        """Тест обработки ошибки API при проверке статуса"""
        mock_db_service = Mock()
        
        stuck_payment = Mock()
        stuck_payment.lava_invoice_id = 'tg_123456_1642234567'
        stuck_payment.status = 'pending'
        
        payment_service._get_stuck_payments = Mock(return_value=[stuck_payment])
        
        # Мокаем ошибку API
        mock_get_status.return_value = {
            'success': False,
            'error': 'API Error'
        }
        
        result = payment_service.check_stuck_payments(mock_db_service)
        
        assert result['success'] is True
        assert result['updated_count'] == 0
        assert len(result['errors']) == 1
        assert 'API Error' in result['errors'][0]
    
    def test_check_stuck_payments_no_payments(self, payment_service):
        """Тест когда нет зависших платежей"""
        mock_db_service = Mock()
        payment_service._get_stuck_payments = Mock(return_value=[])
        
        result = payment_service.check_stuck_payments(mock_db_service)
        
        assert result['success'] is True
        assert result['checked_count'] == 0
        assert result['updated_count'] == 0
        assert len(result['errors']) == 0
    
    def test_get_stuck_payments_success(self, payment_service):
        """Тест получения зависших платежей из БД"""
        mock_db_service = Mock()
        mock_conn = Mock()
        mock_cursor = Mock()
        
        # Мокаем данные из БД
        mock_row = {
            'id': 1,
            'user_id': 1,
            'lava_invoice_id': 'tg_123456_1642234567',
            'amount': 299.0,
            'currency': 'RUB',
            'payment_method': 'ru_card',
            'status': 'pending',
            'created_at': '2024-01-15T09:00:00',
            'expires_at': '2024-01-15T11:00:00'
        }
        
        mock_cursor.fetchall.return_value = [mock_row]
        mock_conn.cursor.return_value = mock_cursor
        
        # Правильно мокаем context manager с MagicMock
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__.return_value = mock_conn
        mock_context_manager.__exit__.return_value = None
        mock_db_service.db_manager.get_connection.return_value = mock_context_manager
        
        payments = payment_service._get_stuck_payments(mock_db_service)
        
        assert len(payments) == 1
        assert payments[0].lava_invoice_id == 'tg_123456_1642234567'
        assert payments[0].status == 'pending'
        assert payments[0].amount == 299.0
        
        mock_cursor.execute.assert_called_once()
        sql_query = mock_cursor.execute.call_args[0][0]
        assert 'status = \'pending\'' in sql_query
        assert 'created_at < datetime(\'now\', \'-10 minutes\')' in sql_query
    
    def test_get_stuck_payments_database_error(self, payment_service):
        """Тест обработки ошибки БД при получении зависших платежей"""
        mock_db_service = Mock()
        mock_db_service.db_manager.get_connection.side_effect = Exception("Database error")
        
        payments = payment_service._get_stuck_payments(mock_db_service)
        
        assert payments == []
    
    def test_process_successful_payment_success(self, payment_service):
        """Тест успешной обработки платежа"""
        mock_db_service = Mock()
        mock_payment = Mock()
        mock_payment.lava_invoice_id = 'tg_123456_1642234567'
        mock_payment.amount = 299.0
        
        mock_user = Mock()
        mock_user.id = 1
        mock_db_service.get_user_by_telegram_id.return_value = mock_user
        mock_db_service.create_or_extend_subscription.return_value = True
        
        payment_service._determine_plan_by_amount = Mock(return_value=1)
        
        # Не должно вызывать исключений
        payment_service._process_successful_payment(mock_payment, mock_db_service)
        
        mock_db_service.get_user_by_telegram_id.assert_called_once_with(123456)
        mock_db_service.create_or_extend_subscription.assert_called_once_with(1, 1)
    
    def test_process_successful_payment_user_not_found(self, payment_service):
        """Тест обработки платежа когда пользователь не найден"""
        mock_db_service = Mock()
        mock_payment = Mock()
        mock_payment.lava_invoice_id = 'tg_123456_1642234567'
        mock_payment.amount = 299.0
        
        mock_db_service.get_user_by_telegram_id.return_value = None
        
        # Не должно вызывать исключений
        payment_service._process_successful_payment(mock_payment, mock_db_service)
        
        mock_db_service.get_user_by_telegram_id.assert_called_once_with(123456)
        mock_db_service.create_or_extend_subscription.assert_not_called()
    
    def test_process_successful_payment_invalid_order_id(self, payment_service):
        """Тест обработки платежа с неверным форматом order_id"""
        mock_db_service = Mock()
        mock_payment = Mock()
        mock_payment.lava_invoice_id = 'invalid_order_id'
        mock_payment.amount = 299.0
        
        # Не должно вызывать исключений
        payment_service._process_successful_payment(mock_payment, mock_db_service)
        
        mock_db_service.get_user_by_telegram_id.assert_not_called()
    
    def test_determine_plan_by_amount_success(self, payment_service):
        """Тест определения плана по сумме платежа"""
        assert payment_service._determine_plan_by_amount(299.0) == 1
        assert payment_service._determine_plan_by_amount(799.0) == 3
        assert payment_service._determine_plan_by_amount(1499.0) == 6
        assert payment_service._determine_plan_by_amount(2799.0) == 12
        assert payment_service._determine_plan_by_amount(999.0) is None
    
    def test_check_expired_payments_success(self, payment_service):
        """Тест успешной проверки истекших платежей"""
        mock_db_service = Mock()
        
        # Создаем тестовые истекшие платежи
        expired_payment = Mock()
        expired_payment.lava_invoice_id = 'tg_123456_1642234567'
        
        mock_db_service.get_expired_payments.return_value = [expired_payment]
        mock_db_service.update_payment_status.return_value = True
        
        result = payment_service.check_expired_payments(mock_db_service)
        
        assert result['success'] is True
        assert result['processed_count'] == 1
        
        mock_db_service.get_expired_payments.assert_called_once()
        mock_db_service.update_payment_status.assert_called_once_with('tg_123456_1642234567', 'expired')
    
    def test_check_expired_payments_no_payments(self, payment_service):
        """Тест когда нет истекших платежей"""
        mock_db_service = Mock()
        mock_db_service.get_expired_payments.return_value = []
        
        result = payment_service.check_expired_payments(mock_db_service)
        
        assert result['success'] is True
        assert result['processed_count'] == 0
    
    def test_check_expired_payments_update_error(self, payment_service):
        """Тест обработки ошибки при обновлении истекшего платежа"""
        mock_db_service = Mock()
        
        expired_payment = Mock()
        expired_payment.lava_invoice_id = 'tg_123456_1642234567'
        
        mock_db_service.get_expired_payments.return_value = [expired_payment]
        mock_db_service.update_payment_status.side_effect = Exception("Update error")
        
        result = payment_service.check_expired_payments(mock_db_service)
        
        assert result['success'] is True
        assert result['processed_count'] == 0
    
    def test_check_expired_payments_database_error(self, payment_service):
        """Тест обработки ошибки БД при проверке истекших платежей"""
        mock_db_service = Mock()
        mock_db_service.get_expired_payments.side_effect = Exception("Database error")
        
        result = payment_service.check_expired_payments(mock_db_service)
        
        assert result['success'] is False
        assert 'Database error' in result['error']
        assert result['processed_count'] == 0