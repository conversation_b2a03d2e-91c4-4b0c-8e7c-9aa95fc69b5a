@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Copying admin.py to server...
pscp -pw %PASSWORD% app\admin.py ubuntu@195.49.212.172:/home/<USER>/admin.py
echo Executing remote commands...
plink -ssh -pw %PASSWORD% -batch -v ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S mv /home/<USER>/admin.py /home/<USER>/app/admin.py && echo '%PASSWORD%' | sudo -S chown telegrambot:telegrambot /home/<USER>/app/admin.py && echo '%PASSWORD%' | sudo -S chmod 644 /home/<USER>/app/admin.py && echo '%PASSWORD%' | sudo -S systemctl restart telegram-payment-bot"
echo Done.