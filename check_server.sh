#!/bin/bash

# Server details
SERVER_IP="**************"
USERNAME="ubuntu"
PASSWORD="dkomqgTaijxro7in^bxd"

# Function to run SSH command
run_ssh() {
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_IP} "$1"
}

echo "Checking home directory..."
run_ssh "ls -la ~/"

echo -e "\nChecking common web directories..."
run_ssh "ls -la /var/www/ 2>/dev/null || echo '/var/www/ not found'"

echo -e "\nChecking for running Python processes..."
run_ssh "ps aux | grep -i python | grep -v grep"

echo -e "\nChecking for Node.js processes..."
run_ssh "ps aux | grep -i node | grep -v grep"

echo -e "\nChecking for any .env files..."
run_ssh "sudo find / -name '.env' -type f 2>/dev/null | head -n 10"

echo -e "\nChecking for systemd services..."
run_ssh "sudo systemctl list-units --type=service | grep -i bot\|telegram\|node\|python"

echo -e "\nChecking nginx configuration..."
run_ssh "ls -la /etc/nginx/sites-enabled/ 2>/dev/null || echo 'Nginx config not found'"
run_ssh "[ -f /etc/nginx/sites-enabled/default ] && cat /etc/nginx/sites-enabled/default || echo 'No default nginx config found'""
