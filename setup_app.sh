#!/bin/bash

# Скрипт настройки приложения Telegram Payment Bot
# Запускать после загрузки кода проекта

set -e

echo "=== Настройка приложения Telegram Payment Bot ==="

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Переменные
APP_DIR="/home/<USER>/app"
VENV_DIR="/home/<USER>/venv"
SERVICE_NAME="telegram-payment-bot"

# Проверка прав root
if [[ $EUID -ne 0 ]]; then
   log_error "Этот скрипт должен быть запущен с правами root (sudo)"
   exit 1
fi

# Проверка наличия кода приложения
if [ ! -f "$APP_DIR/app.py" ]; then
    log_error "Код приложения не найден в $APP_DIR"
    log_error "Загрузите код проекта в эту директорию"
    exit 1
fi

# 1. Создание виртуального окружения
log_info "Создание виртуального окружения Python..."
sudo -u telegrambot python3 -m venv $VENV_DIR

# 2. Установка зависимостей
log_info "Установка зависимостей Python..."
sudo -u telegrambot $VENV_DIR/bin/pip install --upgrade pip
sudo -u telegrambot $VENV_DIR/bin/pip install -r $APP_DIR/requirements.txt

# 3. Создание systemd сервиса
log_info "Создание systemd сервиса..."
cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Telegram Payment Bot
After=network.target

[Service]
Type=exec
User=telegrambot
Group=telegrambot
WorkingDirectory=$APP_DIR
Environment=PATH=$VENV_DIR/bin
Environment=FLASK_ENV=production
ExecStart=$VENV_DIR/bin/gunicorn --config gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF

# 4. Настройка Nginx
log_info "Настройка Nginx..."
cat > /etc/nginx/sites-available/$SERVICE_NAME << EOF
server {
    listen 80;
    server_name _;

    # Основное приложение
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Webhook endpoint
    location /webhook {
        proxy_pass http://127.0.0.1:5000/webhook;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Health check
    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        access_log off;
    }

    # Логирование
    access_log /var/log/nginx/$SERVICE_NAME.access.log;
    error_log /var/log/nginx/$SERVICE_NAME.error.log;
}
EOF

# Активация сайта Nginx
ln -sf /etc/nginx/sites-available/$SERVICE_NAME /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 5. Настройка логротации
log_info "Настройка логротации..."
cat > /etc/logrotate.d/$SERVICE_NAME << EOF
/var/log/telegram-payment-bot/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 telegrambot telegrambot
    postrotate
        systemctl reload $SERVICE_NAME
    endscript
}
EOF

# 6. Настройка файрвола (если ufw установлен)
if command -v ufw &> /dev/null; then
    log_info "Настройка файрвола..."
    ufw allow 22/tcp
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw --force enable
fi

# 7. Перезагрузка сервисов
log_info "Перезагрузка сервисов..."
systemctl daemon-reload
systemctl enable $SERVICE_NAME
systemctl restart nginx
systemctl enable nginx

# 8. Проверка конфигурации Nginx
nginx -t

log_info "Настройка приложения завершена!"
log_info "Для запуска сервиса выполните:"
log_info "  sudo systemctl start $SERVICE_NAME"
log_info "Для проверки статуса:"
log_info "  sudo systemctl status $SERVICE_NAME"
log_info "Для просмотра логов:"
log_info "  sudo journalctl -u $SERVICE_NAME -f"

echo "=== Настройка приложения завершена ==="