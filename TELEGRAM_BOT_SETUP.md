# Настройка Telegram бота и системы уведомлений

## 🎯 Обзор

Telegram бот полностью интегрирован с Lava.top API и включает:
- Интерактивный интерфейс для покупки подписок
- Автоматические уведомления о статусе платежей
- Систему управления подписками
- Автоматические напоминания об истечении подписки

## ✅ Что готово и протестировано

### **Основные компоненты:**
- ✅ **PaymentService** - создание счетов через Lava.top API
- ✅ **TelegramBotHandler** - обработка команд и взаимодействие с пользователями
- ✅ **NotificationService** - централизованная система уведомлений
- ✅ **WebhookHandler** - обработка уведомлений от Lava.top
- ✅ **SchedulerService** - автоматические задачи и уведомления
- ✅ **DatabaseService** - управление пользователями, платежами и подписками

### **Функциональность бота:**
- ✅ Команды: `/start`, `/купить_подписку`, `/статус_подписки`, `/о_канале`, `/задать_вопрос`
- ✅ Интерактивные клавиатуры для выбора планов и способов оплаты
- ✅ Поддержка способов оплаты: карты РФ, международные карты, криптовалюта
- ✅ Тарифные планы: 1, 3, 6, 12 месяцев с автоматическим расчетом скидок

### **Система уведомлений:**
- ✅ Уведомления об успешной оплате
- ✅ Уведомления о неудачной оплате
- ✅ Автоматическая отправка пригласительных ссылок
- ✅ Предупреждения об истечении подписки (1, 3, 7 дней)
- ✅ Уведомления об истечении подписки
- ✅ Массовые уведомления через планировщик

## 🚀 Запуск системы

### 1. Проверка готовности

Запустите тест интеграции для проверки всех компонентов:

```bash
python test_bot_integration.py
```

Ожидаемый результат:
```
🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!
💡 Telegram бот готов к использованию
Пройдено тестов: 5/5
```

### 2. Запуск Flask приложения

```bash
python app.py
```

Приложение запустится на `http://0.0.0.0:5000` и будет готово принимать webhook от Lava.top.

### 3. Настройка webhook в Lava.top

В личном кабинете Lava.top настройте webhook:
- **URL:** `http://**************/webhook`
- **Метод:** POST
- **События:** Все события платежей

## 📱 Использование бота

### Основные команды

| Команда | Описание |
|---------|----------|
| `/start` | Главное меню бота |
| `/купить_подписку` | Выбор тарифного плана |
| `/статус_подписки` | Проверка статуса подписки |
| `/о_канале` | Информация о канале |
| `/задать_вопрос` | Контакты поддержки |

### Процесс покупки подписки

1. **Пользователь:** `/start` или `/купить_подписку`
2. **Бот:** Показывает тарифные планы с ценами и скидками
3. **Пользователь:** Выбирает план (1, 3, 6 или 12 месяцев)
4. **Бот:** Показывает способы оплаты
5. **Пользователь:** Выбирает способ оплаты
6. **Бот:** Создает счет через Lava.top API и отправляет ссылку
7. **Пользователь:** Переходит по ссылке и оплачивает
8. **Система:** Получает webhook, обрабатывает платеж, отправляет уведомления

### Автоматические уведомления

#### После успешной оплаты:
```
✅ Платеж успешно обработан!

💳 Детали платежа:
• Номер заказа: tg_123456789_1753479676
• Сумма: 299 ₽
• План: 1 месяц

🎉 Ваша подписка активирована!
```

#### При неудачной оплате:
```
❌ Платеж не удался

📋 Детали:
• Номер заказа: tg_123456789_1753479676
• Причина: Недостаточно средств

💡 Что делать:
• Попробуйте создать новый счет
• Проверьте данные карты
```

#### Пригласительная ссылка:
```
🎉 Добро пожаловать в приватный канал!

📅 Подписка действует до: 25.08.2025 в 21:41

👇 Нажмите кнопку ниже, чтобы присоединиться:
[🔗 Присоединиться к каналу]
```

#### Предупреждение об истечении:
```
⚠️ Уведомление о подписке

Ваша подписка истекает завтра. Рекомендуем продлить её заранее.

📊 Статус: истекает завтра
```

## 🔧 Конфигурация

### Переменные окружения (.env)

```env
# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Lava.top API
LAVA_API_KEY=your_api_key_here
LAVA_SECRET_KEY=your_secret_key_here
LAVA_BASE_URL=https://gate.lava.top

# Webhook
WEBHOOK_URL=http://**************

# Channel
CHANNEL_ID=-1001002786126628

# Admin
ADMIN_USER_IDS=389794370
ADMIN_PASSWORD=SecureAdmin2024!

# Subscription Plans
PLAN_1_MONTHS=1
PLAN_1_PRICE=299
PLAN_3_MONTHS=3
PLAN_3_PRICE=799
PLAN_6_MONTHS=6
PLAN_6_PRICE=1499
PLAN_12_MONTHS=12
PLAN_12_PRICE=2799
```

### Способы оплаты

Поддерживаемые способы оплаты в системе:

| Ключ | Название | Описание |
|------|----------|----------|
| `card_ru` | Карта РФ | Российские банковские карты |
| `card_foreign` | Карта иностранного банка | Международные карты |
| `crypto` | Криптовалюта | Оплата криптовалютой |

### Тарифные планы

| План | Месяцев | Цена | Скидка |
|------|---------|------|--------|
| 1 месяц | 1 | 299 ₽ | - |
| 3 месяца | 3 | 799 ₽ | 11% |
| 6 месяцев | 6 | 1499 ₽ | 16% |
| 12 месяцев | 12 | 2799 ₽ | 22% |

## 🔄 Автоматические задачи

### Планировщик задач (SchedulerService)

Автоматически выполняет следующие задачи:

#### Каждый час:
- **Проверка истекающих подписок** - обновление статусов
- **Удаление пользователей** из канала при истечении подписки
- **Отправка предупреждений** об истечении (1, 3, 7 дней)

#### Ежедневно:
- **Массовые уведомления** об истечении подписок
- **Очистка истекших платежей** старше 30 дней
- **Резервное копирование** базы данных
- **Очистка логов** старше 30 дней

### Мониторинг задач

Статистика выполнения задач доступна через:
- Админ-панель: `/admin/dashboard`
- API endpoint: `/metrics`
- Логи: `bot.log`

## 📊 Мониторинг и метрики

### Health Check

```bash
curl http://localhost:5000/health
```

Ответ:
```json
{
  "status": "healthy",
  "database": "connected",
  "services": {
    "payment_service": "initialized",
    "bot_handler": "initialized",
    "scheduler_service": "running"
  }
}
```

### Метрики системы

```bash
curl http://localhost:5000/metrics
```

Включает:
- Количество пользователей и подписок
- Статистику платежей
- Производительность системы
- Статус планировщика

### Webhook статистика

```bash
curl http://localhost:5000/webhook/test
```

Показывает:
- Количество обработанных webhook
- Статистику безопасности
- Последние обработанные платежи

## 🛠️ Разработка и тестирование

### Структура тестов

```
tests/
├── test_bot_integration.py          # Основные тесты интеграции
├── test_full_integration.py         # Полный тест с webhook
├── test_notifications_integration.py # Тесты уведомлений
├── test_payment_service.py          # Тесты PaymentService
└── test_lava_api.py                 # Тесты Lava.top API
```

### Запуск тестов

```bash
# Основные тесты
python test_bot_integration.py

# Полная интеграция
python test_full_integration.py

# Тесты API
python test_lava_api.py

# Тесты уведомлений
python test_notifications_integration.py
```

### Mock тестирование

Для тестирования без реальных API вызовов используются mock объекты:

```python
class MockBotHandler:
    def send_payment_success_notification(self, user_id, order_id, amount, plan_months):
        print(f"Mock: Уведомление об успешной оплате для {user_id}")
        return True
```

## 🔒 Безопасность

### Webhook безопасность

- **IP фильтрация** - только разрешенные IP адреса
- **Rate limiting** - ограничение количества запросов
- **Валидация подписи** - проверка HMAC подписи от Lava.top
- **Валидация данных** - проверка структуры webhook

### Защита от дублирования

- **Уникальные order_id** - предотвращение повторной обработки
- **Кэширование webhook** - защита от дублированных уведомлений
- **Транзакции БД** - атомарность операций

## 📝 Логирование

### Уровни логирования

- **INFO** - обычные операции
- **WARNING** - предупреждения
- **ERROR** - ошибки обработки
- **DEBUG** - детальная отладка (только в development)

### Файлы логов

- `bot.log` - основные логи приложения
- `error.log` - только ошибки
- `payment_checker.log` - логи проверки платежей

### Ротация логов

- Максимальный размер файла: 10MB
- Количество backup файлов: 5
- Автоматическая очистка старых логов

## 🚨 Устранение неполадок

### Частые проблемы

#### 1. Бот не отвечает на команды
```bash
# Проверьте токен бота
echo $TELEGRAM_BOT_TOKEN

# Проверьте логи
tail -f bot.log
```

#### 2. Webhook не обрабатывается
```bash
# Проверьте URL webhook
curl -X POST http://localhost:5000/webhook/test

# Проверьте настройки в Lava.top
```

#### 3. Платежи не создаются
```bash
# Тест API Lava.top
python test_lava_api.py

# Проверьте API ключи
python test_payment_service.py
```

#### 4. Уведомления не отправляются
```bash
# Тест системы уведомлений
python test_notifications_integration.py

# Проверьте планировщик
curl http://localhost:5000/metrics
```

### Диагностические команды

```bash
# Проверка всех компонентов
python test_bot_integration.py

# Проверка базы данных
sqlite3 payments.db ".tables"

# Проверка процессов
ps aux | grep python

# Проверка портов
netstat -tlnp | grep 5000
```

## 📈 Масштабирование

### Производительность

- **Асинхронная обработка** webhook
- **Пулы соединений** к базе данных
- **Кэширование** часто используемых данных
- **Rate limiting** для защиты от перегрузки

### Мониторинг производительности

- CPU и память через `/metrics`
- Время ответа API
- Количество активных соединений
- Статистика обработки webhook

## 🔄 Обновления и миграции

### Обновление кода

```bash
# Остановка сервисов
pkill -f "python app.py"

# Обновление кода
git pull origin main

# Миграция БД (если нужно)
python -c "from app.models.database import migrate_database; migrate_database()"

# Запуск
python app.py
```

### Резервное копирование

Автоматическое резервное копирование:
- **Ежедневно** в 02:00
- Хранение 30 дней
- Папка: `./backups/`

Ручное резервное копирование:
```bash
python -c "
from app.models.database import DatabaseService
db = DatabaseService()
db.create_backup('./backup_manual.db')
"
```

## 📞 Поддержка

### Контакты

- **Email:** <EMAIL>
- **Telegram:** @support_bot
- **Время работы:** 9:00-18:00 (МСК)

### Документация

- [Lava.top API](https://gate.lava.top/docs)
- [Telegram Bot API](https://core.telegram.org/bots/api)
- [Flask Documentation](https://flask.palletsprojects.com/)

---

**Telegram бот с системой уведомлений полностью настроен и готов к использованию!** 🎯

Для запуска выполните:
```bash
python app.py
```

И протестируйте бота командой `/start` в Telegram.