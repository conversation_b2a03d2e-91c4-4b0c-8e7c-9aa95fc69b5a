#!/usr/bin/env python3
"""
Тест веб-админ панели
"""

import os
import sys
from unittest.mock import patch

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_admin_panel():
    """Тест веб-админ панели"""
    
    # Мокаем переменные окружения для тестирования
    test_env = {
        'TELEGRAM_BOT_TOKEN': 'test_bot_token',
        'LAVA_API_KEY': 'test_lava_key',
        'LAVA_SECRET_KEY': 'test_lava_secret',
        'WEBHOOK_URL': 'https://test.example.com/webhook',
        'CHANNEL_ID': '@test_channel',
        'ADMIN_USER_IDS': '123456789,987654321',
        'ADMIN_PASSWORD': 'test_admin_password',
        'SECRET_KEY': 'test_secret_key_for_flask'
    }
    
    with patch.dict(os.environ, test_env):
        try:
            # Импортируем после установки переменных окружения
            import importlib.util
            spec = importlib.util.spec_from_file_location("app_module", "app.py")
            app_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(app_module)
            
            print("✅ Импорт Flask приложения успешен")
            
            # Создаем Flask приложение
            app = app_module.create_app()
            print("✅ Flask приложение создано")
            
            # Проверяем, что админ панель зарегистрирована
            admin_routes = [rule.rule for rule in app.url_map.iter_rules() if rule.rule.startswith('/admin')]
            print(f"✅ Найдено админ маршрутов: {len(admin_routes)}")
            
            expected_routes = [
                '/admin/',
                '/admin/dashboard',
                '/admin/login',
                '/admin/logout',
                '/admin/users',
                '/admin/subscriptions',
                '/admin/payments',
                '/admin/logs',
                '/admin/settings',
                '/admin/cleanup',
                '/admin/api/stats'
            ]
            
            for route in expected_routes:
                if any(route in admin_route for admin_route in admin_routes):
                    print(f"✅ Маршрут найден: {route}")
                else:
                    print(f"❌ Маршрут не найден: {route}")
                    return False
            
            # Тестируем Flask приложение
            with app.test_client() as client:
                # Тест главной страницы админки (должен редиректить на логин)
                response = client.get('/admin/')
                print(f"✅ GET /admin/ - статус: {response.status_code}")
                
                # Тест страницы логина
                response = client.get('/admin/login')
                print(f"✅ GET /admin/login - статус: {response.status_code}")
                
                # Тест API статистики (без авторизации - должен редиректить)
                response = client.get('/admin/api/stats')
                print(f"✅ GET /admin/api/stats - статус: {response.status_code}")
                
                # Тест POST логина с неверным паролем
                response = client.post('/admin/login', data={
                    'password': 'wrong_password',
                    'csrf_token': 'test'  # В реальном тесте нужен настоящий CSRF токен
                }, follow_redirects=True)
                print(f"✅ POST /admin/login (неверный пароль) - статус: {response.status_code}")
            
            print("\n🎉 Тест веб-админ панели прошел успешно!")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка теста админ панели: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🔄 Запуск теста веб-админ панели...")
    success = test_admin_panel()
    sys.exit(0 if success else 1)