#!/bin/bash

# Rinad<PERSON> Bot Installation Script
# Автоматическая установка и настройка Telegram бота

echo "🚀 Установка Rinadzhi Telegram Payment Bot..."

# Проверка прав root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Пожалуйста, запустите скрипт с правами root (sudo)"
    exit 1
fi

# Создание пользователя telegrambot
echo "👤 Создание пользователя telegrambot..."
if ! id "telegrambot" &>/dev/null; then
    useradd -m -s /bin/bash telegrambot
    echo "✅ Пользователь telegrambot создан"
else
    echo "ℹ️ Пользователь telegrambot уже существует"
fi

# Создание директорий
echo "📁 Создание директорий..."
mkdir -p /home/<USER>/app
mkdir -p /var/log/telegram-payment-bot
chown telegrambot:telegrambot /home/<USER>/app
chown telegrambot:telegrambot /var/log/telegram-payment-bot
echo "✅ Директории созданы"

# Установка Python и pip
echo "🐍 Проверка Python..."
if ! command -v python3 &> /dev/null; then
    echo "📦 Установка Python3..."
    apt update
    apt install -y python3 python3-pip python3-venv
fi
echo "✅ Python готов"

# Создание виртуального окружения
echo "🔧 Создание виртуального окружения..."
cd /home/<USER>/app
sudo -u telegrambot python3 -m venv venv
sudo -u telegrambot /home/<USER>/app/venv/bin/pip install --upgrade pip
echo "✅ Виртуальное окружение создано"

# Установка зависимостей
echo "📦 Установка зависимостей..."
sudo -u telegrambot /home/<USER>/app/venv/bin/pip install pyTelegramBotAPI requests
echo "✅ Зависимости установлены"

# Копирование файлов (если они есть в текущей директории)
echo "📄 Копирование файлов бота..."

# Проверяем наличие файлов в текущей директории
CURRENT_DIR=$(pwd)

if [ -f "$CURRENT_DIR/rinadzhi_bot_final.py" ]; then
    cp "$CURRENT_DIR/rinadzhi_bot_final.py" /home/<USER>/app/rinadzhi_bot.py
    echo "✅ rinadzhi_bot.py скопирован"
else
    echo "⚠️ rinadzhi_bot_final.py не найден в текущей директории"
fi

if [ -f "$CURRENT_DIR/config_rinadzhi.py" ]; then
    cp "$CURRENT_DIR/config_rinadzhi.py" /home/<USER>/app/config.py
    echo "✅ config.py скопирован"
else
    echo "⚠️ config_rinadzhi.py не найден в текущей директории"
fi

if [ -f "$CURRENT_DIR/init_database.py" ]; then
    cp "$CURRENT_DIR/init_database.py" /home/<USER>/app/
    echo "✅ init_database.py скопирован"
else
    echo "⚠️ init_database.py не найден в текущей директории"
fi

# Установка прав доступа
echo "🔐 Установка прав доступа..."
chown -R telegrambot:telegrambot /home/<USER>/app/
chmod +x /home/<USER>/app/rinadzhi_bot.py
echo "✅ Права доступа установлены"

# Инициализация базы данных
echo "🗄️ Инициализация базы данных..."
if [ -f "/home/<USER>/app/init_database.py" ]; then
    sudo -u telegrambot /home/<USER>/app/venv/bin/python /home/<USER>/app/init_database.py
    echo "✅ База данных инициализирована"
else
    echo "⚠️ Файл init_database.py не найден, пропускаем инициализацию БД"
fi

# Создание systemd сервиса
echo "⚙️ Создание systemd сервиса..."
cat > /etc/systemd/system/rinadzhi-bot.service << EOF
[Unit]
Description=Rinadzhi Telegram Payment Bot
After=network.target

[Service]
Type=simple
User=telegrambot
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/venv/bin/python rinadzhi_bot.py
Restart=always
RestartSec=10
StandardOutput=append:/var/log/telegram-payment-bot/rinadzhi_bot.log
StandardError=append:/var/log/telegram-payment-bot/rinadzhi_bot.log

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
echo "✅ Systemd сервис создан"

echo ""
echo "🎉 Установка завершена!"
echo ""
echo "📋 Следующие шаги:"
echo "1. Отредактируйте /home/<USER>/app/config.py"
echo "   - Добавьте токен Telegram бота"
echo "   - Добавьте API ключи Lava.top"
echo "   - Укажите ID канала"
echo ""
echo "2. Запустите бота:"
echo "   sudo systemctl start rinadzhi-bot"
echo "   sudo systemctl enable rinadzhi-bot"
echo ""
echo "3. Проверьте логи:"
echo "   sudo journalctl -u rinadzhi-bot -f"
echo "   sudo tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log"
echo ""
echo "4. Управление сервисом:"
echo "   sudo systemctl start rinadzhi-bot    # Запуск"
echo "   sudo systemctl stop rinadzhi-bot     # Остановка"
echo "   sudo systemctl restart rinadzhi-bot  # Перезапуск"
echo "   sudo systemctl status rinadzhi-bot   # Статус"
echo ""
echo "✅ Готово к использованию!"
