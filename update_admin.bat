@echo off
chcp 65001 >nul
echo ========================================
echo   ОБНОВЛЕНИЕ АДМИН-ПАНЕЛИ НА СЕРВЕРЕ
echo ========================================
echo.

echo 1. Создание архива...
powershell -Command "if (Test-Path 'admin_panel_fix.zip') { Remove-Item 'admin_panel_fix.zip' -Force }; Compress-Archive -Path 'app\admin.py', 'templates\*' -DestinationPath 'admin_panel_fix.zip' -Force"
if %errorlevel% neq 0 (
    echo Ошибка создания архива
    pause
    exit /b 1
)
echo Архив создан успешно

echo.
echo 2. Загрузка архива на сервер...
echo Введите пароль сервера когда будет запрошен
pscp admin_panel_fix.zip ubuntu@**************:/home/<USER>/
if %errorlevel% neq 0 (
    echo Ошибка загрузки архива
    pause
    exit /b 1
)
echo Архив загружен успешно

echo.
echo 3. Выполнение обновления на сервере...
echo Введите пароль сервера когда будет запрошен

echo cd /home/<USER>/telegram_bot > update_commands.txt
echo echo 'Stopping service...' >> update_commands.txt
echo sudo systemctl stop telegram-bot >> update_commands.txt
echo echo 'Creating backup...' >> update_commands.txt
echo sudo cp -r app templates backup_$(date +%%Y%%m%%d_%%H%%M%%S) >> update_commands.txt
echo echo 'Extracting updates...' >> update_commands.txt
echo unzip -o /home/<USER>/admin_panel_fix.zip >> update_commands.txt
echo echo 'Setting permissions...' >> update_commands.txt
echo sudo chown -R ubuntu:ubuntu app templates >> update_commands.txt
echo sudo chmod -R 755 app templates >> update_commands.txt
echo echo 'Starting service...' >> update_commands.txt
echo sudo systemctl start telegram-bot >> update_commands.txt
echo echo 'Checking status...' >> update_commands.txt
echo sudo systemctl status telegram-bot --no-pager -l >> update_commands.txt
echo echo 'Verifying updates...' >> update_commands.txt
echo if grep -q 'safe_url_for' app/admin.py; then echo 'admin.py: OK'; else echo 'admin.py: FAILED'; fi >> update_commands.txt
echo if grep -q 'safe_url_for' templates/base.html; then echo 'templates: OK'; else echo 'templates: FAILED'; fi >> update_commands.txt
echo echo 'Update completed!' >> update_commands.txt

plink -ssh ubuntu@************** -m update_commands.txt

del update_commands.txt

echo.
echo ========================================
echo   ОБНОВЛЕНИЕ ЗАВЕРШЕНО!
echo ========================================
echo.
echo ТЕСТИРОВАНИЕ:
echo 1. Откройте: https://**************/admin/login
echo 2. Введите пароль: admin123
echo 3. Проверьте работу всех разделов
echo.
echo При проблемах запустите:
echo restart_services.ps1
echo.
pause