"""
Тесты для Scheduler Service
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

from app.services.scheduler_service import SchedulerService
from app.models.database import DatabaseService, Subscription, User
from app.services.notification_service import NotificationService
from app.services.channel_manager import ChannelManager


class TestSchedulerService:
    """Тесты для SchedulerService"""
    
    @pytest.fixture
    def mock_db_service(self):
        """Мок сервиса базы данных"""
        return Mock(spec=DatabaseService)
    
    @pytest.fixture
    def mock_notification_service(self):
        """Мок сервиса уведомлений"""
        return Mock(spec=NotificationService)
    
    @pytest.fixture
    def mock_channel_manager(self):
        """Мок менеджера канала"""
        return Mock(spec=ChannelManager)
    
    @pytest.fixture
    def scheduler_service(self, mock_db_service, mock_notification_service, mock_channel_manager):
        """Экземпляр SchedulerService для тестов"""
        return SchedulerService(mock_db_service, mock_notification_service, mock_channel_manager)
    
    @pytest.fixture
    def sample_user(self):
        """Пример пользователя"""
        return User(
            id=1,
            telegram_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.fixture
    def sample_active_subscription(self):
        """Пример активной подписки"""
        return Subscription(
            id=1,
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now() - timedelta(days=20),
            end_date=datetime.now() + timedelta(days=10),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.fixture
    def sample_expired_subscription(self):
        """Пример истекшей подписки"""
        return Subscription(
            id=2,
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now() - timedelta(days=40),
            end_date=datetime.now() - timedelta(days=5),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    def test_scheduler_initialization(self, scheduler_service):
        """Тест инициализации планировщика"""
        assert scheduler_service.db_service is not None
        assert scheduler_service.notification_service is not None
        assert scheduler_service.channel_manager is not None
        assert scheduler_service.scheduler is not None
        assert not scheduler_service.scheduler.running
        assert scheduler_service.job_stats['executed'] == 0
        assert scheduler_service.job_stats['errors'] == 0
    
    def test_start_scheduler(self, scheduler_service):
        """Тест запуска планировщика"""
        with patch.object(scheduler_service.scheduler, 'start') as mock_start:
            scheduler_service.start()
            mock_start.assert_called_once()
            
            # Проверяем, что задачи добавлены
            jobs = scheduler_service.scheduler.get_jobs()
            job_ids = [job.id for job in jobs]
            
            assert 'check_expiring_subscriptions' in job_ids
            assert 'send_expiration_warnings' in job_ids
            assert 'cleanup_expired_subscriptions' in job_ids
    
    def test_stop_scheduler(self, scheduler_service):
        """Тест остановки планировщика"""
        with patch.object(scheduler_service.scheduler, 'shutdown') as mock_shutdown, \
             patch.object(scheduler_service.scheduler, 'running', True):
            scheduler_service.stop()
            mock_shutdown.assert_called_once_with(wait=True)
    
    def test_check_expiring_subscriptions_with_active_subscriptions(
        self, scheduler_service, sample_active_subscription, sample_user
    ):
        """Тест проверки истекающих подписок с активными подписками"""
        # Настройка моков
        scheduler_service.db_service.get_all_active_subscriptions.return_value = [sample_active_subscription]
        scheduler_service.db_service.get_user_by_id.return_value = sample_user
        scheduler_service.channel_manager.remove_user_from_channel.return_value = True
        
        # Выполнение
        result = scheduler_service.check_expiring_subscriptions()
        
        # Проверки
        assert result['checked'] == 1
        assert result['expired'] == 0
        assert result['expiring_soon'] == 0  # Подписка истекает через 10 дней, но логика не считает это "expiring_soon"
        assert result['errors'] == 0
        
        scheduler_service.db_service.get_all_active_subscriptions.assert_called_once()
    
    def test_check_expiring_subscriptions_with_expired_subscriptions(
        self, scheduler_service, sample_expired_subscription, sample_user
    ):
        """Тест проверки истекающих подписок с истекшими подписками"""
        # Настройка моков
        scheduler_service.db_service.get_all_active_subscriptions.return_value = [sample_expired_subscription]
        scheduler_service.db_service.get_user_by_id.return_value = sample_user
        scheduler_service.db_service.update_subscription_status.return_value = True
        scheduler_service.channel_manager.remove_user_from_channel.return_value = True
        
        # Выполнение
        result = scheduler_service.check_expiring_subscriptions()
        
        # Проверки
        assert result['checked'] == 1
        assert result['expired'] == 1
        assert result['removed_from_channel'] == 1
        assert result['errors'] == 0
        
        scheduler_service.db_service.update_subscription_status.assert_called_once_with(
            sample_expired_subscription.id, 'expired'
        )
        scheduler_service.channel_manager.remove_user_from_channel.assert_called_once_with(
            sample_user.telegram_id
        )
    
    def test_check_expiring_subscriptions_with_channel_removal_failure(
        self, scheduler_service, sample_expired_subscription, sample_user
    ):
        """Тест проверки истекающих подписок с ошибкой удаления из канала"""
        # Настройка моков
        scheduler_service.db_service.get_all_active_subscriptions.return_value = [sample_expired_subscription]
        scheduler_service.db_service.get_user_by_id.return_value = sample_user
        scheduler_service.db_service.update_subscription_status.return_value = True
        scheduler_service.channel_manager.remove_user_from_channel.return_value = False
        
        # Выполнение
        result = scheduler_service.check_expiring_subscriptions()
        
        # Проверки
        assert result['checked'] == 1
        assert result['expired'] == 1
        assert result['removed_from_channel'] == 0
        assert result['errors'] == 0
    
    def test_check_expiring_subscriptions_with_database_error(self, scheduler_service):
        """Тест проверки истекающих подписок с ошибкой базы данных"""
        # Настройка мока для генерации исключения
        scheduler_service.db_service.get_all_active_subscriptions.side_effect = Exception("Database error")
        
        # Выполнение
        result = scheduler_service.check_expiring_subscriptions()
        
        # Проверки
        assert result['checked'] == 0
        assert result['errors'] == 1
    
    def test_send_expiration_warnings(self, scheduler_service):
        """Тест отправки предупреждений об истечении"""
        # Настройка мока
        expected_result = {
            'warnings_7_days': 2,
            'warnings_1_day': 1,
            'errors': 0
        }
        scheduler_service.notification_service.send_expiration_warnings.return_value = expected_result
        
        # Выполнение
        result = scheduler_service.send_expiration_warnings()
        
        # Проверки
        assert result == expected_result
        scheduler_service.notification_service.send_expiration_warnings.assert_called_once()
    
    def test_send_expiration_warnings_with_error(self, scheduler_service):
        """Тест отправки предупреждений с ошибкой"""
        # Настройка мока для генерации исключения
        scheduler_service.notification_service.send_expiration_warnings.side_effect = Exception("Notification error")
        
        # Выполнение
        result = scheduler_service.send_expiration_warnings()
        
        # Проверки
        assert result['warnings_7_days'] == 0
        assert result['warnings_1_day'] == 0
        assert result['errors'] == 1
    
    def test_cleanup_expired_subscriptions(self, scheduler_service, sample_expired_subscription, sample_user):
        """Тест очистки истекших подписок"""
        # Настройка моков
        scheduler_service.db_service.get_expired_subscriptions.return_value = [sample_expired_subscription]
        scheduler_service.db_service.get_user_by_id.return_value = sample_user
        scheduler_service.channel_manager.check_user_in_channel.return_value = True
        scheduler_service.channel_manager.remove_user_from_channel.return_value = True
        scheduler_service.db_service.update_subscription_status.return_value = True
        
        # Выполнение
        result = scheduler_service.cleanup_expired_subscriptions()
        
        # Проверки
        assert result['expired_found'] == 1
        assert result['users_removed'] == 1
        assert result['subscriptions_updated'] == 1
        assert result['errors'] == 0
        
        scheduler_service.db_service.get_expired_subscriptions.assert_called_once()
        scheduler_service.channel_manager.check_user_in_channel.assert_called_once_with(sample_user.telegram_id)
        scheduler_service.channel_manager.remove_user_from_channel.assert_called_once_with(sample_user.telegram_id)
        scheduler_service.db_service.update_subscription_status.assert_called_once_with(
            sample_expired_subscription.id, 'expired'
        )
    
    def test_cleanup_expired_subscriptions_user_not_in_channel(
        self, scheduler_service, sample_expired_subscription, sample_user
    ):
        """Тест очистки истекших подписок когда пользователь не в канале"""
        # Настройка моков
        scheduler_service.db_service.get_expired_subscriptions.return_value = [sample_expired_subscription]
        scheduler_service.db_service.get_user_by_id.return_value = sample_user
        scheduler_service.channel_manager.check_user_in_channel.return_value = False
        scheduler_service.db_service.update_subscription_status.return_value = True
        
        # Выполнение
        result = scheduler_service.cleanup_expired_subscriptions()
        
        # Проверки
        assert result['expired_found'] == 1
        assert result['users_removed'] == 0
        assert result['subscriptions_updated'] == 1
        assert result['errors'] == 0
        
        scheduler_service.channel_manager.remove_user_from_channel.assert_not_called()
    
    def test_cleanup_expired_subscriptions_with_error(self, scheduler_service):
        """Тест очистки истекших подписок с ошибкой"""
        # Настройка мока для генерации исключения
        scheduler_service.db_service.get_expired_subscriptions.side_effect = Exception("Database error")
        
        # Выполнение
        result = scheduler_service.cleanup_expired_subscriptions()
        
        # Проверки
        assert result['expired_found'] == 0
        assert result['users_removed'] == 0
        assert result['subscriptions_updated'] == 0
        assert result['errors'] == 1
    
    def test_run_manual_check(self, scheduler_service):
        """Тест ручной проверки всех задач"""
        # Настройка моков
        subscription_result = {'checked': 5, 'expired': 1, 'errors': 0}
        warning_result = {'warnings_7_days': 2, 'warnings_1_day': 1, 'errors': 0}
        cleanup_result = {'expired_found': 1, 'users_removed': 1, 'errors': 0}
        
        with patch.object(scheduler_service, 'check_expiring_subscriptions', return_value=subscription_result), \
             patch.object(scheduler_service, 'send_expiration_warnings', return_value=warning_result), \
             patch.object(scheduler_service, 'cleanup_expired_subscriptions', return_value=cleanup_result):
            
            # Выполнение
            result = scheduler_service.run_manual_check()
            
            # Проверки
            assert 'timestamp' in result
            assert result['subscription_check'] == subscription_result
            assert result['expiration_warnings'] == warning_result
            assert result['expired_cleanup'] == cleanup_result
    
    def test_get_scheduler_status_running(self, scheduler_service):
        """Тест получения статуса работающего планировщика"""
        # Настройка мока
        mock_job = Mock()
        mock_job.id = 'test_job'
        mock_job.name = 'Test Job'
        mock_job.next_run_time = datetime.now()
        mock_job.trigger = IntervalTrigger(hours=1)
        
        scheduler_service.scheduler.running = True
        scheduler_service.scheduler.get_jobs = Mock(return_value=[mock_job])
        scheduler_service.job_stats['executed'] = 10
        scheduler_service.job_stats['errors'] = 1
        
        # Выполнение
        status = scheduler_service.get_scheduler_status()
        
        # Проверки
        assert status['running'] is True
        assert status['jobs_count'] == 1
        assert len(status['jobs']) == 1
        assert status['jobs'][0]['id'] == 'test_job'
        assert status['jobs'][0]['name'] == 'Test Job'
        assert status['stats']['executed'] == 10
        assert status['stats']['errors'] == 1
    
    def test_get_scheduler_status_not_running(self, scheduler_service):
        """Тест получения статуса остановленного планировщика"""
        scheduler_service.scheduler.running = False
        
        # Выполнение
        status = scheduler_service.get_scheduler_status()
        
        # Проверки
        assert status['running'] is False
        assert status['jobs_count'] == 0
        assert status['jobs'] == []
    
    def test_add_custom_job(self, scheduler_service):
        """Тест добавления пользовательской задачи"""
        # Настройка мока
        mock_func = Mock()
        trigger = IntervalTrigger(minutes=30)
        
        with patch.object(scheduler_service.scheduler, 'add_job') as mock_add_job:
            # Выполнение
            result = scheduler_service.add_custom_job(
                func=mock_func,
                trigger=trigger,
                job_id='custom_job',
                name='Custom Job'
            )
            
            # Проверки
            assert result is True
            mock_add_job.assert_called_once_with(
                func=mock_func,
                trigger=trigger,
                id='custom_job',
                name='Custom Job',
                replace_existing=True,
                max_instances=1
            )
    
    def test_add_custom_job_with_error(self, scheduler_service):
        """Тест добавления пользовательской задачи с ошибкой"""
        # Настройка мока для генерации исключения
        mock_func = Mock()
        trigger = IntervalTrigger(minutes=30)
        
        with patch.object(scheduler_service.scheduler, 'add_job', side_effect=Exception("Scheduler error")):
            # Выполнение
            result = scheduler_service.add_custom_job(
                func=mock_func,
                trigger=trigger,
                job_id='custom_job',
                name='Custom Job'
            )
            
            # Проверки
            assert result is False
    
    def test_remove_job(self, scheduler_service):
        """Тест удаления задачи"""
        with patch.object(scheduler_service.scheduler, 'remove_job') as mock_remove_job:
            # Выполнение
            result = scheduler_service.remove_job('test_job')
            
            # Проверки
            assert result is True
            mock_remove_job.assert_called_once_with('test_job')
    
    def test_remove_job_with_error(self, scheduler_service):
        """Тест удаления задачи с ошибкой"""
        with patch.object(scheduler_service.scheduler, 'remove_job', side_effect=Exception("Job not found")):
            # Выполнение
            result = scheduler_service.remove_job('test_job')
            
            # Проверки
            assert result is False
    
    def test_pause_job(self, scheduler_service):
        """Тест приостановки задачи"""
        with patch.object(scheduler_service.scheduler, 'pause_job') as mock_pause_job:
            # Выполнение
            result = scheduler_service.pause_job('test_job')
            
            # Проверки
            assert result is True
            mock_pause_job.assert_called_once_with('test_job')
    
    def test_resume_job(self, scheduler_service):
        """Тест возобновления задачи"""
        with patch.object(scheduler_service.scheduler, 'resume_job') as mock_resume_job:
            # Выполнение
            result = scheduler_service.resume_job('test_job')
            
            # Проверки
            assert result is True
            mock_resume_job.assert_called_once_with('test_job')
    
    def test_get_job_stats(self, scheduler_service):
        """Тест получения статистики задач"""
        # Настройка данных
        test_time = datetime.now()
        scheduler_service.job_stats['executed'] = 15
        scheduler_service.job_stats['errors'] = 2
        scheduler_service.job_stats['last_execution'] = test_time
        scheduler_service.job_stats['last_error'] = test_time
        
        # Выполнение
        stats = scheduler_service.get_job_stats()
        
        # Проверки
        assert stats['executed'] == 15
        assert stats['errors'] == 2
        assert stats['last_execution'] == test_time.isoformat()
        assert stats['last_error'] == test_time.isoformat()
    
    def test_job_listener_success(self, scheduler_service):
        """Тест слушателя событий при успешном выполнении задачи"""
        # Создание мок события
        mock_event = Mock()
        mock_event.exception = None
        mock_event.job_id = 'test_job'
        
        initial_executed = scheduler_service.job_stats['executed']
        
        # Выполнение
        scheduler_service._job_listener(mock_event)
        
        # Проверки
        assert scheduler_service.job_stats['executed'] == initial_executed + 1
        assert scheduler_service.job_stats['last_execution'] is not None
    
    def test_job_listener_error(self, scheduler_service):
        """Тест слушателя событий при ошибке выполнения задачи"""
        # Создание мок события
        mock_event = Mock()
        mock_event.exception = Exception("Test error")
        mock_event.job_id = 'test_job'
        
        initial_errors = scheduler_service.job_stats['errors']
        
        # Выполнение
        scheduler_service._job_listener(mock_event)
        
        # Проверки
        assert scheduler_service.job_stats['errors'] == initial_errors + 1
        assert scheduler_service.job_stats['last_error'] is not None
    
    def test_job_triggers_configuration(self, scheduler_service):
        """Тест конфигурации триггеров задач"""
        scheduler_service.start()
        
        jobs = scheduler_service.scheduler.get_jobs()
        job_dict = {job.id: job for job in jobs}
        
        # Проверяем триггер для проверки подписок (каждый час)
        subscription_job = job_dict.get('check_expiring_subscriptions')
        assert subscription_job is not None
        assert isinstance(subscription_job.trigger, IntervalTrigger)
        
        # Проверяем триггер для предупреждений (ежедневно в 10:00)
        warning_job = job_dict.get('send_expiration_warnings')
        assert warning_job is not None
        assert isinstance(warning_job.trigger, CronTrigger)
        
        # Проверяем триггер для очистки (каждый час)
        cleanup_job = job_dict.get('cleanup_expired_subscriptions')
        assert cleanup_job is not None
        assert isinstance(cleanup_job.trigger, IntervalTrigger)
        
        scheduler_service.stop()


if __name__ == '__main__':
    pytest.main([__file__])