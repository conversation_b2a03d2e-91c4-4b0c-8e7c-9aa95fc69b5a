@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Checking bot location on server...
echo.
echo Checking /home/<USER>/app/:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "ls -la /home/<USER>/app/ 2>/dev/null || echo 'Directory not found'"
echo.
echo Checking /opt/telegram-payment-bot/app/:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "ls -la /opt/telegram-payment-bot/app/ 2>/dev/null || echo 'Directory not found'"
echo.
echo Checking systemd service file:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "echo '%PASSWORD%' | sudo -S cat /etc/systemd/system/telegram-payment-bot.service | grep WorkingDirectory"
echo Done.