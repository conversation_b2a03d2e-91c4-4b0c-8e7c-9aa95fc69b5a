"""
Система мониторинга и метрик для production окружения
"""

import time
import psutil
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from threading import Lock
from config import Config

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """Системные метрики"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    active_connections: int
    uptime_seconds: int

@dataclass
class ApplicationMetrics:
    """Метрики приложения"""
    timestamp: datetime
    total_users: int
    active_subscriptions: int
    pending_payments: int
    completed_payments: int
    failed_payments: int
    webhook_requests: int
    api_errors: int
    bot_messages_sent: int
    bot_messages_received: int

class MetricsCollector:
    """Сборщик метрик для мониторинга системы"""
    
    def __init__(self):
        self.start_time = time.time()
        self.metrics_lock = Lock()
        self.app_metrics = {
            'total_users': 0,
            'active_subscriptions': 0,
            'pending_payments': 0,
            'completed_payments': 0,
            'failed_payments': 0,
            'webhook_requests': 0,
            'api_errors': 0,
            'bot_messages_sent': 0,
            'bot_messages_received': 0
        }
        
    def get_system_metrics(self) -> SystemMetrics:
        """Получает системные метрики"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Количество активных соединений (приблизительно)
            connections = len(psutil.net_connections())
            
            uptime = int(time.time() - self.start_time)
            
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk.percent,
                active_connections=connections,
                uptime_seconds=uptime
            )
        except Exception as e:
            logger.error(f"Ошибка получения системных метрик: {e}")
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_percent=0.0,
                active_connections=0,
                uptime_seconds=0
            )
    
    def get_application_metrics(self) -> ApplicationMetrics:
        """Получает метрики приложения"""
        with self.metrics_lock:
            return ApplicationMetrics(
                timestamp=datetime.now(),
                **self.app_metrics.copy()
            )
    
    def update_database_metrics(self):
        """Обновляет метрики из базы данных"""
        try:
            from app.models.database import DatabaseService
            db = DatabaseService()
            
            with self.metrics_lock:
                # Получаем статистику из базы данных
                system_stats = db.get_system_statistics()
                payment_stats = db.get_payment_statistics()
                
                self.app_metrics['total_users'] = system_stats.get('total_users', 0)
                self.app_metrics['active_subscriptions'] = system_stats.get('active_subscriptions', 0)
                self.app_metrics['pending_payments'] = payment_stats.get('pending_payments', 0)
                self.app_metrics['completed_payments'] = payment_stats.get('completed_payments', 0)
                self.app_metrics['failed_payments'] = payment_stats.get('failed_payments', 0)
                
        except Exception as e:
            logger.error(f"Ошибка обновления метрик из БД: {e}")
    
    def increment_counter(self, metric_name: str, value: int = 1):
        """Увеличивает счетчик метрики"""
        with self.metrics_lock:
            if metric_name in self.app_metrics:
                self.app_metrics[metric_name] += value
    
    def set_gauge(self, metric_name: str, value: int):
        """Устанавливает значение метрики"""
        with self.metrics_lock:
            if metric_name in self.app_metrics:
                self.app_metrics[metric_name] = value
    
    def get_health_status(self) -> Dict[str, Any]:
        """Возвращает статус здоровья системы"""
        system_metrics = self.get_system_metrics()
        app_metrics = self.get_application_metrics()
        
        # Определяем статус здоровья
        health_status = "healthy"
        issues = []
        
        # Проверяем системные ресурсы
        if system_metrics.cpu_percent > 80:
            health_status = "warning"
            issues.append(f"Высокая загрузка CPU: {system_metrics.cpu_percent}%")
        
        if system_metrics.memory_percent > 85:
            health_status = "warning"
            issues.append(f"Высокое использование памяти: {system_metrics.memory_percent}%")
        
        if system_metrics.disk_percent > 90:
            health_status = "critical"
            issues.append(f"Критически мало места на диске: {system_metrics.disk_percent}%")
        
        # Проверяем ошибки приложения
        if app_metrics.api_errors > 10:  # За последний период
            health_status = "warning"
            issues.append(f"Много ошибок API: {app_metrics.api_errors}")
        
        return {
            "status": health_status,
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": system_metrics.uptime_seconds,
            "issues": issues,
            "system": asdict(system_metrics),
            "application": asdict(app_metrics)
        }

class HealthChecker:
    """Проверка здоровья системы"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.last_check = datetime.now()
    
    def check_database_connection(self) -> bool:
        """Проверяет подключение к базе данных"""
        try:
            from app.models.database import DatabaseService
            db = DatabaseService()
            # Простой запрос для проверки соединения
            stats = db.get_system_statistics()
            return True
        except Exception as e:
            logger.error(f"Ошибка подключения к БД: {e}")
            return False
    
    def check_telegram_api(self) -> bool:
        """Проверяет доступность Telegram API"""
        try:
            import telebot
            bot = telebot.TeleBot(Config.TELEGRAM_BOT_TOKEN)
            bot.get_me()
            return True
        except Exception as e:
            logger.error(f"Ошибка подключения к Telegram API: {e}")
            return False
    
    def check_lava_api(self) -> bool:
        """Проверяет доступность Lava.top API"""
        try:
            import requests
            headers = {
                'Authorization': f'Bearer {Config.LAVA_API_KEY}',
                'Content-Type': 'application/json'
            }
            response = requests.get(
                f'{Config.LAVA_BASE_URL}/business/invoice/status',
                headers=headers,
                timeout=10
            )
            return response.status_code in [200, 401]  # 401 означает что API доступно
        except Exception as e:
            logger.error(f"Ошибка подключения к Lava API: {e}")
            return False
    
    def perform_health_check(self) -> Dict[str, Any]:
        """Выполняет полную проверку здоровья"""
        checks = {
            "database": self.check_database_connection(),
            "telegram_api": self.check_telegram_api(),
            "lava_api": self.check_lava_api()
        }
        
        # Получаем общий статус системы
        system_status = self.metrics.get_health_status()
        
        # Определяем общий статус
        all_checks_passed = all(checks.values())
        overall_status = system_status["status"]
        
        if not all_checks_passed:
            overall_status = "critical"
        
        self.last_check = datetime.now()
        
        return {
            "status": overall_status,
            "timestamp": self.last_check.isoformat(),
            "checks": checks,
            "system": system_status
        }

class AlertManager:
    """Менеджер уведомлений о проблемах"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.last_alerts = {}
        self.alert_cooldown = timedelta(minutes=15)  # Не чаще раза в 15 минут
    
    def should_send_alert(self, alert_type: str) -> bool:
        """Проверяет, нужно ли отправлять уведомление"""
        now = datetime.now()
        last_alert = self.last_alerts.get(alert_type)
        
        if last_alert is None:
            return True
        
        return now - last_alert > self.alert_cooldown
    
    def send_alert(self, alert_type: str, message: str, level: str = "warning"):
        """Отправляет уведомление администратору"""
        if not self.should_send_alert(alert_type):
            return
        
        try:
            # Отправляем уведомление в Telegram
            if Config.NOTIFICATION_CHAT_ID:
                self._send_telegram_alert(message, level)
            
            # Отправляем email (если настроен)
            if Config.ADMIN_EMAIL and Config.SMTP_SERVER:
                self._send_email_alert(message, level)
            
            # Логируем уведомление
            logger.warning(f"Alert sent [{alert_type}]: {message}")
            
            self.last_alerts[alert_type] = datetime.now()
            
        except Exception as e:
            logger.error(f"Ошибка отправки уведомления: {e}")
    
    def _send_telegram_alert(self, message: str, level: str):
        """Отправляет уведомление в Telegram"""
        try:
            import telebot
            bot = telebot.TeleBot(Config.TELEGRAM_BOT_TOKEN)
            
            emoji = {
                "info": "ℹ️",
                "warning": "⚠️",
                "critical": "🚨"
            }.get(level, "⚠️")
            
            alert_message = f"{emoji} **Системное уведомление**\n\n{message}\n\nВремя: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            bot.send_message(
                Config.NOTIFICATION_CHAT_ID,
                alert_message,
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Ошибка отправки Telegram уведомления: {e}")
    
    def _send_email_alert(self, message: str, level: str):
        """Отправляет email уведомление"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            msg = MIMEMultipart()
            msg['From'] = Config.SMTP_USERNAME
            msg['To'] = Config.ADMIN_EMAIL
            msg['Subject'] = f"[{level.upper()}] Telegram Payment Bot Alert"
            
            body = f"""
            Системное уведомление от Telegram Payment Bot
            
            Уровень: {level.upper()}
            Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            
            Сообщение:
            {message}
            
            ---
            Это автоматическое уведомление от системы мониторинга.
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(Config.SMTP_SERVER, Config.SMTP_PORT)
            if Config.SMTP_USE_TLS:
                server.starttls()
            server.login(Config.SMTP_USERNAME, Config.SMTP_PASSWORD)
            server.send_message(msg)
            server.quit()
            
        except Exception as e:
            logger.error(f"Ошибка отправки email уведомления: {e}")
    
    def check_and_alert(self):
        """Проверяет метрики и отправляет уведомления при необходимости"""
        health_status = self.metrics.get_health_status()
        
        # Проверяем критические проблемы
        if health_status["status"] == "critical":
            for issue in health_status["issues"]:
                self.send_alert("critical_issue", issue, "critical")
        
        # Проверяем предупреждения
        elif health_status["status"] == "warning":
            for issue in health_status["issues"]:
                self.send_alert("warning_issue", issue, "warning")
        
        # Проверяем ошибки API
        app_metrics = self.metrics.get_application_metrics()
        if app_metrics.api_errors > 20:
            self.send_alert(
                "high_api_errors",
                f"Высокое количество ошибок API: {app_metrics.api_errors}",
                "warning"
            )

# Глобальный экземпляр сборщика метрик
metrics_collector = MetricsCollector()
health_checker = HealthChecker(metrics_collector)
alert_manager = AlertManager(metrics_collector)

def get_metrics_collector() -> MetricsCollector:
    """Возвращает глобальный экземпляр сборщика метрик"""
    return metrics_collector

def get_health_checker() -> HealthChecker:
    """Возвращает глобальный экземпляр проверки здоровья"""
    return health_checker

def get_alert_manager() -> AlertManager:
    """Возвращает глобальный экземпляр менеджера уведомлений"""
    return alert_manager