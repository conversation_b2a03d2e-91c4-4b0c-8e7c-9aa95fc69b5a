@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Creating proper start.sh script...
echo.
echo Creating start.sh script with proper format:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S bash -c 'cat > /home/<USER>/app/start.sh << \"EOF\"
#!/bin/bash
cd /home/<USER>/app
source venv/bin/activate
python main.py
EOF'"
echo.
echo Making start.sh executable:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S chmod +x /home/<USER>/app/start.sh"
echo.
echo Setting proper ownership:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S chown telegrambot:telegrambot /home/<USER>/app/start.sh"
echo.
echo Checking file structure:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "ls -la /home/<USER>/app/"
echo Done.