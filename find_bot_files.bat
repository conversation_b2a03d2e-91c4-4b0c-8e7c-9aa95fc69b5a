@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Searching for bot files on server...
echo.
echo Looking for main.py:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "find /home /opt -name 'main.py' 2>/dev/null"
echo.
echo Looking for app.py:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "find /home /opt -name 'app.py' 2>/dev/null"
echo.
echo Looking for config.py:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "find /home /opt -name 'config.py' 2>/dev/null"
echo.
echo Looking for .env files:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "find /home /opt -name '.env' 2>/dev/null"
echo.
echo Checking if telegrambot user home exists:
plink -ssh -pw %PASSWORD% -batch ubuntu@195.49.212.172 "ls -la /home/<USER>/ 2>/dev/null || echo 'Directory not found'"
echo Done.