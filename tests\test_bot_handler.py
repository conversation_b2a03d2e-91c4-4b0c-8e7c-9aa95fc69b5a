"""
Тесты для TelegramBotHandler
"""

import pytest
from unittest.mock import Mock, MagicMock, patch, call
from datetime import datetime, timedelta
from decimal import Decimal

from app.services.bot_handler import TelegramBotHandler
from app.models.models import User, Subscription, Payment
from app.models.database import DatabaseService
from app.services.payment_service import PaymentService


class TestTelegramBotHandler:
    """Тесты для класса TelegramBotHandler"""
    
    @pytest.fixture
    def mock_db_service(self):
        """Мок сервиса базы данных"""
        mock_service = Mock(spec=DatabaseService)
        # Добавляем методы, которые используются в коде
        mock_service.get_user_by_telegram_id = Mock()
        mock_service.create_user = Mock()
        mock_service.get_user_active_subscription = Mock()
        mock_service.create_payment = Mock()
        return mock_service
    
    @pytest.fixture
    def mock_payment_service(self):
        """Мок сервиса платежей"""
        mock_service = Mock(spec=PaymentService)
        mock_service.get_subscription_plans.return_value = {
            1: {'months': 1, 'price': Decimal('299.00'), 'name': '1 месяц'},
            3: {'months': 3, 'price': Decimal('799.00'), 'name': '3 месяца'},
            6: {'months': 6, 'price': Decimal('1499.00'), 'name': '6 месяцев'},
            12: {'months': 12, 'price': Decimal('2799.00'), 'name': '12 месяцев'}
        }
        mock_service.get_available_payment_methods.return_value = {
            'ru_card': {'service': 'card_ru', 'name': 'Карта РФ'},
            'foreign_card': {'service': 'card_intl', 'name': 'Карта иностранного банка'},
            'crypto': {'service': 'crypto', 'name': 'Криптовалюта'}
        }
        return mock_service
    
    @pytest.fixture
    def mock_bot(self):
        """Мок Telegram бота"""
        return Mock()
    
    @pytest.fixture
    def bot_handler(self, mock_db_service, mock_payment_service):
        """Экземпляр TelegramBotHandler с моками"""
        with patch('app.services.bot_handler.telebot.TeleBot') as mock_telebot:
            mock_bot = Mock()
            mock_telebot.return_value = mock_bot
            
            handler = TelegramBotHandler(mock_db_service, mock_payment_service)
            handler.bot = mock_bot
            return handler
    
    @pytest.fixture
    def mock_message(self):
        """Мок сообщения от Telegram"""
        message = Mock()
        message.from_user.id = 123456789
        message.from_user.username = 'testuser'
        message.from_user.first_name = 'Test'
        message.from_user.last_name = 'User'
        message.text = '/start'
        return message
    
    @pytest.fixture
    def mock_callback_query(self):
        """Мок callback query от inline клавиатуры"""
        call = Mock()
        call.from_user.id = 123456789
        call.from_user.username = 'testuser'
        call.from_user.first_name = 'Test'
        call.data = 'plan_1'
        call.message.message_id = 12345
        call.id = 'callback_123'
        return call


class TestStartCommand(TestTelegramBotHandler):
    """Тесты команды /start"""
    
    def test_handle_start_command_new_user(self, bot_handler, mock_message, mock_db_service):
        """Тест команды /start для нового пользователя"""
        # Настройка моков
        mock_db_service.get_user_by_telegram_id.return_value = None
        mock_db_service.create_user.return_value = True
        
        # Выполнение
        bot_handler.handle_start_command(mock_message)
        
        # Проверки
        mock_db_service.get_user_by_telegram_id.assert_called_once_with(123456789)
        mock_db_service.create_user.assert_called_once()
        
        # Проверяем, что отправлено приветственное сообщение
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert args[0] == 123456789  # user_id
        assert 'Добро пожаловать' in args[1]  # текст содержит приветствие
        assert kwargs['parse_mode'] == 'HTML'
        assert kwargs['reply_markup'] is not None  # есть клавиатура
    
    def test_handle_start_command_existing_user(self, bot_handler, mock_message, mock_db_service):
        """Тест команды /start для существующего пользователя"""
        # Настройка моков
        existing_user = User(
            id=1,
            telegram_id=123456789,
            username='testuser',
            first_name='Test'
        )
        mock_db_service.get_user_by_telegram_id.return_value = existing_user
        
        # Выполнение
        bot_handler.handle_start_command(mock_message)
        
        # Проверки
        mock_db_service.get_user_by_telegram_id.assert_called_once_with(123456789)
        mock_db_service.create_user.assert_not_called()  # не создаем пользователя
        
        # Проверяем отправку сообщения
        bot_handler.bot.send_message.assert_called_once()
    
    def test_handle_start_command_error(self, bot_handler, mock_message, mock_db_service):
        """Тест обработки ошибки в команде /start"""
        # Настройка моков для ошибки
        mock_db_service.get_user_by_telegram_id.side_effect = Exception("Database error")
        
        # Выполнение
        bot_handler.handle_start_command(mock_message)
        
        # Проверяем, что отправлено сообщение об ошибке
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        assert 'произошла ошибка' in args[1].lower()


class TestBuySubscription(TestTelegramBotHandler):
    """Тесты команды покупки подписки"""
    
    def test_handle_buy_subscription_no_active_subscription(self, bot_handler, mock_message, mock_db_service):
        """Тест покупки подписки без активной подписки"""
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        mock_db_service.get_user_by_telegram_id.return_value = user
        mock_db_service.get_user_active_subscription.return_value = None
        
        # Выполнение
        bot_handler.handle_buy_subscription(mock_message)
        
        # Проверки
        mock_db_service.get_user_active_subscription.assert_called_once_with(1)
        
        # Проверяем отправку сообщения с тарифами
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert args[0] == 123456789
        assert 'тарифный план' in args[1].lower()
        assert kwargs['reply_markup'] is not None
    
    def test_handle_buy_subscription_with_active_subscription(self, bot_handler, mock_message, mock_db_service):
        """Тест покупки подписки с активной подпиской"""
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        active_subscription = Subscription(
            id=1,
            user_id=1,
            plan_type='monthly',
            status='active',
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=15)
        )
        
        mock_db_service.get_user_by_telegram_id.return_value = user
        mock_db_service.get_user_active_subscription.return_value = active_subscription
        
        # Выполнение
        bot_handler.handle_buy_subscription(mock_message)
        
        # Проверки
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert 'уже есть активная подписка' in args[1]
        assert 'продлить' in args[1].lower()


class TestSubscriptionStatus(TestTelegramBotHandler):
    """Тесты команды статуса подписки"""
    
    def test_handle_subscription_status_active(self, bot_handler, mock_message, mock_db_service):
        """Тест статуса активной подписки"""
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        active_subscription = Subscription(
            id=1,
            user_id=1,
            plan_type='monthly',
            status='active',
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=15)
        )
        
        mock_db_service.get_user_by_telegram_id.return_value = user
        mock_db_service.get_user_active_subscription.return_value = active_subscription
        
        # Выполнение
        bot_handler.handle_subscription_status(mock_message)
        
        # Проверки
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert 'Статус вашей подписки' in args[1]
        assert 'Активна' in args[1] or 'Скоро истечет' in args[1]
        assert '15' in args[1]  # количество дней
    
    def test_handle_subscription_status_no_subscription(self, bot_handler, mock_message, mock_db_service):
        """Тест статуса без подписки"""
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        mock_db_service.get_user_by_telegram_id.return_value = user
        mock_db_service.get_user_active_subscription.return_value = None
        
        # Выполнение
        bot_handler.handle_subscription_status(mock_message)
        
        # Проверки
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert 'нет активной подписки' in args[1]
        assert 'приобрести подписку' in args[1].lower()
    
    def test_handle_subscription_status_expiring_soon(self, bot_handler, mock_message, mock_db_service):
        """Тест статуса подписки, которая скоро истекает"""
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        expiring_subscription = Subscription(
            id=1,
            user_id=1,
            plan_type='monthly',
            status='active',
            start_date=datetime.now() - timedelta(days=25),
            end_date=datetime.now() + timedelta(days=3)  # истекает через 3 дня
        )
        
        mock_db_service.get_user_by_telegram_id.return_value = user
        mock_db_service.get_user_active_subscription.return_value = expiring_subscription
        
        # Выполнение
        bot_handler.handle_subscription_status(mock_message)
        
        # Проверки
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert 'Скоро истечет' in args[1] or 'Истекает сегодня' in args[1]
        # Проверяем, что количество дней отображается (может быть 2 или 3 из-за времени выполнения)
        assert ('2' in args[1] or '3' in args[1]), f"Expected 2 or 3 days in message: {args[1]}"


class TestPlanSelection(TestTelegramBotHandler):
    """Тесты выбора тарифного плана"""
    
    def test_handle_plan_selection_valid_plan(self, bot_handler, mock_callback_query):
        """Тест выбора валидного тарифного плана"""
        # Настройка callback данных
        mock_callback_query.data = 'plan_3'
        
        # Выполнение
        bot_handler.handle_plan_selection(mock_callback_query)
        
        # Проверки
        bot_handler.bot.edit_message_text.assert_called_once()
        args, kwargs = bot_handler.bot.edit_message_text.call_args
        
        assert '3 месяца' in args[0]  # название плана
        assert '799' in args[0]  # цена плана
        assert kwargs['reply_markup'] is not None  # есть клавиатура способов оплаты
        
        bot_handler.bot.answer_callback_query.assert_called_once_with(mock_callback_query.id)
    
    def test_handle_plan_selection_invalid_plan(self, bot_handler, mock_callback_query):
        """Тест выбора невалидного тарифного плана"""
        # Настройка callback данных
        mock_callback_query.data = 'plan_99'  # несуществующий план
        
        # Выполнение
        bot_handler.handle_plan_selection(mock_callback_query)
        
        # Проверки
        bot_handler.bot.answer_callback_query.assert_called_once_with(
            mock_callback_query.id, 
            "Ошибка: неверный тарифный план"
        )
        bot_handler.bot.edit_message_text.assert_not_called()


class TestPaymentMethodSelection(TestTelegramBotHandler):
    """Тесты выбора способа оплаты"""
    
    def test_handle_payment_method_selection_success(self, bot_handler, mock_callback_query, 
                                                   mock_db_service, mock_payment_service):
        """Тест успешного выбора способа оплаты"""
        # Настройка callback данных
        mock_callback_query.data = 'payment_ru_card_1'
        
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        mock_db_service.get_user_by_telegram_id.return_value = user
        mock_db_service.create_payment.return_value = True
        
        invoice_result = {
            'success': True,
            'order_id': 'tg_123456789_1234567890',
            'invoice_id': 'lava_invoice_123',
            'payment_url': 'https://pay.lava.top/invoice/123',
            'amount': Decimal('299.00'),
            'currency': 'RUB',
            'expires_at': datetime.now() + timedelta(hours=1),
            'payment_method': 'ru_card',
            'plan_months': 1
        }
        mock_payment_service.create_invoice.return_value = invoice_result
        
        # Выполнение
        bot_handler.handle_payment_method_selection(mock_callback_query)
        
        # Проверки
        mock_payment_service.create_invoice.assert_called_once_with(123456789, 1, 'ru_card')
        mock_db_service.create_payment.assert_called_once()
        
        # Проверяем, что отправлена ссылка для оплаты
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert 'Счет для оплаты создан' in args[1]
        assert kwargs['reply_markup'] is not None
        
        # Проверяем, что в клавиатуре есть кнопка с URL для оплаты
        keyboard = kwargs['reply_markup']
        found_payment_button = False
        for row in keyboard.keyboard:
            for button in row:
                if hasattr(button, 'url') and button.url == 'https://pay.lava.top/invoice/123':
                    found_payment_button = True
                    break
        assert found_payment_button, "Кнопка с URL для оплаты не найдена"
    
    def test_handle_payment_method_selection_failure(self, bot_handler, mock_callback_query, 
                                                   mock_db_service, mock_payment_service):
        """Тест неудачного создания счета для оплаты"""
        # Настройка callback данных
        mock_callback_query.data = 'payment_ru_card_1'
        
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        mock_db_service.get_user_by_telegram_id.return_value = user
        
        invoice_result = {
            'success': False,
            'error': 'API Error',
            'error_code': 'API_ERROR'
        }
        mock_payment_service.create_invoice.return_value = invoice_result
        
        # Выполнение
        bot_handler.handle_payment_method_selection(mock_callback_query)
        
        # Проверки
        mock_payment_service.create_invoice.assert_called_once_with(123456789, 1, 'ru_card')
        mock_db_service.create_payment.assert_not_called()
        
        # Проверяем, что отправлено сообщение об ошибке
        bot_handler.bot.edit_message_text.assert_called_once()
        args, kwargs = bot_handler.bot.edit_message_text.call_args
        
        assert 'Ошибка создания счета' in args[0]
        assert 'API Error' in args[0]


class TestInformationalCommands(TestTelegramBotHandler):
    """Тесты информационных команд"""
    
    def test_handle_channel_info(self, bot_handler, mock_message):
        """Тест команды информации о канале"""
        # Выполнение
        bot_handler.handle_channel_info(mock_message)
        
        # Проверки
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert args[0] == 123456789
        assert 'О нашем приватном канале' in args[1]
        assert 'Эксклюзивный контент' in args[1]
        assert 'Преимущества подписки' in args[1]
        assert 'Статистика канала' in args[1]
        assert kwargs['reply_markup'] is not None
        
        # Проверяем наличие кнопок в клавиатуре
        keyboard = kwargs['reply_markup']
        button_texts = []
        for row in keyboard.keyboard:
            for button in row:
                button_texts.append(button.text)
        
        assert any('Купить подписку' in text for text in button_texts)
        assert any('Задать вопрос' in text for text in button_texts)
    
    def test_handle_channel_info_error(self, bot_handler, mock_message):
        """Тест обработки ошибки в команде информации о канале"""
        # Настройка мока для ошибки
        bot_handler.bot.send_message.side_effect = Exception("Send error")
        
        # Выполнение
        bot_handler.handle_channel_info(mock_message)
        
        # Проверяем, что ошибка была залогирована (метод _send_error_message вызовется)
        # Сбрасываем side_effect для последующих вызовов
        bot_handler.bot.send_message.side_effect = None
    
    def test_handle_ask_question(self, bot_handler, mock_message):
        """Тест команды задать вопрос"""
        # Выполнение
        bot_handler.handle_ask_question(mock_message)
        
        # Проверки
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert args[0] == 123456789
        assert 'Поддержка и контакты' in args[1]
        assert '<EMAIL>' in args[1]
        assert '@support_bot' in args[1]
        assert '+7 (999) 123-45-67' in args[1]
        assert 'Время работы поддержки' in args[1]
        assert 'Часто задаваемые вопросы' in args[1]
        assert kwargs['reply_markup'] is not None
        
        # Проверяем наличие кнопок в клавиатуре
        keyboard = kwargs['reply_markup']
        button_texts = []
        button_urls = []
        for row in keyboard.keyboard:
            for button in row:
                button_texts.append(button.text)
                if hasattr(button, 'url'):
                    button_urls.append(button.url)
        
        assert any('Написать в поддержку' in text for text in button_texts)
        assert any('Главное меню' in text for text in button_texts)
        assert 'https://t.me/support_bot' in button_urls
    
    def test_handle_ask_question_error(self, bot_handler, mock_message):
        """Тест обработки ошибки в команде задать вопрос"""
        # Настройка мока для ошибки
        bot_handler.bot.send_message.side_effect = Exception("Send error")
        
        # Выполнение
        bot_handler.handle_ask_question(mock_message)
        
        # Сбрасываем side_effect
        bot_handler.bot.send_message.side_effect = None
    
    def test_handle_unknown_command(self, bot_handler, mock_message):
        """Тест обработки неизвестной команды"""
        # Настройка сообщения
        mock_message.text = '/unknown_command'
        mock_message.content_type = 'text'
        
        # Выполнение
        bot_handler.handle_unknown_command(mock_message)
        
        # Проверки
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert args[0] == 123456789
        assert 'Команда не распознана' in args[1]
        assert 'доступных команд' in args[1]
        assert '/start' in args[1]
        assert '/купить_подписку' in args[1]
        assert '/статус_подписки' in args[1]
        assert '/о_канале' in args[1]
        assert '/задать_вопрос' in args[1]
        assert kwargs['reply_markup'] is not None
    
    def test_handle_unknown_command_non_text(self, bot_handler, mock_message):
        """Тест игнорирования неизвестных команд для не-текстовых сообщений"""
        # Настройка сообщения
        mock_message.content_type = 'photo'
        
        # Выполнение
        bot_handler.handle_unknown_command(mock_message)
        
        # Проверки - сообщение не должно быть отправлено
        bot_handler.bot.send_message.assert_not_called()
    
    def test_handle_unknown_command_error(self, bot_handler, mock_message):
        """Тест обработки ошибки в неизвестной команде"""
        # Настройка сообщения
        mock_message.text = '/unknown_command'
        mock_message.content_type = 'text'
        
        # Настройка мока для ошибки
        bot_handler.bot.send_message.side_effect = Exception("Send error")
        
        # Выполнение
        bot_handler.handle_unknown_command(mock_message)
        
        # Сбрасываем side_effect
        bot_handler.bot.send_message.side_effect = None


class TestCallbackHandlers(TestTelegramBotHandler):
    """Тесты callback обработчиков"""
    
    def test_handle_channel_info_callback(self, bot_handler, mock_callback_query):
        """Тест callback обработчика информации о канале"""
        # Настройка callback данных
        mock_callback_query.data = 'channel_info'
        
        # Выполнение
        bot_handler.handle_channel_info_callback(mock_callback_query)
        
        # Проверки
        bot_handler.bot.edit_message_text.assert_called_once()
        args, kwargs = bot_handler.bot.edit_message_text.call_args
        
        assert 'О нашем приватном канале' in args[0]
        assert 'Эксклюзивный контент' in args[0]
        assert kwargs['reply_markup'] is not None
        
        bot_handler.bot.answer_callback_query.assert_called_once_with(mock_callback_query.id)
    
    def test_handle_ask_question_callback(self, bot_handler, mock_callback_query):
        """Тест callback обработчика задать вопрос"""
        # Настройка callback данных
        mock_callback_query.data = 'ask_question'
        
        # Выполнение
        bot_handler.handle_ask_question_callback(mock_callback_query)
        
        # Проверки
        bot_handler.bot.edit_message_text.assert_called_once()
        args, kwargs = bot_handler.bot.edit_message_text.call_args
        
        assert 'Поддержка и контакты' in args[0]
        assert '<EMAIL>' in args[0]
        assert kwargs['reply_markup'] is not None
        
        bot_handler.bot.answer_callback_query.assert_called_once_with(mock_callback_query.id)
    
    def test_handle_main_menu_callback(self, bot_handler, mock_callback_query):
        """Тест callback обработчика главного меню"""
        # Настройка callback данных
        mock_callback_query.data = 'main_menu'
        
        # Выполнение
        bot_handler.handle_main_menu_callback(mock_callback_query)
        
        # Проверки
        bot_handler.bot.edit_message_text.assert_called_once()
        args, kwargs = bot_handler.bot.edit_message_text.call_args
        
        assert 'Главное меню' in args[0]
        assert 'Купить подписку на канал' in args[0]
        assert kwargs['reply_markup'] is not None
        
        bot_handler.bot.answer_callback_query.assert_called_once_with(mock_callback_query.id)
    
    def test_handle_buy_subscription_callback(self, bot_handler, mock_callback_query, mock_db_service):
        """Тест callback обработчика покупки подписки"""
        # Настройка callback данных
        mock_callback_query.data = 'buy_subscription'
        
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        mock_db_service.get_user_by_telegram_id.return_value = user
        mock_db_service.get_user_active_subscription.return_value = None
        
        # Выполнение
        bot_handler.handle_buy_subscription_callback(mock_callback_query)
        
        # Проверки
        bot_handler.bot.edit_message_text.assert_called_once()
        args, kwargs = bot_handler.bot.edit_message_text.call_args
        
        assert 'тарифный план' in args[0].lower()
        assert kwargs['reply_markup'] is not None
        
        bot_handler.bot.answer_callback_query.assert_called_once_with(mock_callback_query.id)
    
    def test_handle_subscription_status_callback(self, bot_handler, mock_callback_query, mock_db_service):
        """Тест callback обработчика статуса подписки"""
        # Настройка callback данных
        mock_callback_query.data = 'subscription_status'
        
        # Настройка моков
        user = User(id=1, telegram_id=123456789, username='testuser')
        mock_db_service.get_user_by_telegram_id.return_value = user
        mock_db_service.get_user_active_subscription.return_value = None
        
        # Выполнение
        bot_handler.handle_subscription_status_callback(mock_callback_query)
        
        # Проверки
        bot_handler.bot.edit_message_text.assert_called_once()
        args, kwargs = bot_handler.bot.edit_message_text.call_args
        
        assert 'нет активной подписки' in args[0]
        assert kwargs['reply_markup'] is not None
        
        bot_handler.bot.answer_callback_query.assert_called_once_with(mock_callback_query.id)
    
    def test_handle_back_to_plans_callback(self, bot_handler, mock_callback_query):
        """Тест callback обработчика возврата к тарифам"""
        # Настройка callback данных
        mock_callback_query.data = 'back_to_plans'
        
        # Выполнение
        bot_handler.handle_back_to_plans_callback(mock_callback_query)
        
        # Проверки
        bot_handler.bot.edit_message_text.assert_called_once()
        args, kwargs = bot_handler.bot.edit_message_text.call_args
        
        assert 'тарифный план' in args[0].lower()
        assert kwargs['reply_markup'] is not None
        
        bot_handler.bot.answer_callback_query.assert_called_once_with(mock_callback_query.id)
    
    def test_callback_error_handling(self, bot_handler, mock_callback_query):
        """Тест обработки ошибок в callback обработчиках"""
        # Настройка callback данных
        mock_callback_query.data = 'channel_info'
        
        # Настройка мока для ошибки
        bot_handler.bot.edit_message_text.side_effect = Exception("Edit error")
        
        # Выполнение
        bot_handler.handle_channel_info_callback(mock_callback_query)
        
        # Проверяем, что callback query был отвечен с ошибкой
        bot_handler.bot.answer_callback_query.assert_called_once()
        
        # Сбрасываем side_effect
        bot_handler.bot.edit_message_text.side_effect = None


class TestKeyboardCreation(TestTelegramBotHandler):
    """Тесты создания клавиатур"""
    
    def test_create_main_menu_keyboard(self, bot_handler):
        """Тест создания главного меню"""
        keyboard = bot_handler._create_main_menu_keyboard()
        
        # Проверяем, что клавиатура создана
        assert keyboard is not None
        assert hasattr(keyboard, 'keyboard')
        assert len(keyboard.keyboard) > 0
    
    def test_create_subscription_plans_keyboard(self, bot_handler):
        """Тест создания клавиатуры тарифных планов"""
        keyboard = bot_handler._create_subscription_plans_keyboard()
        
        # Проверяем, что клавиатура создана
        assert keyboard is not None
        assert hasattr(keyboard, 'keyboard')
        
        # Проверяем, что есть кнопки для всех планов
        button_texts = []
        for row in keyboard.keyboard:
            for button in row:
                button_texts.append(button.text)
        
        # Должны быть кнопки для всех 4 планов + кнопка "Главное меню"
        assert len([text for text in button_texts if 'месяц' in text]) == 4
        assert any('Главное меню' in text for text in button_texts)
    
    def test_create_payment_methods_keyboard(self, bot_handler):
        """Тест создания клавиатуры способов оплаты"""
        keyboard = bot_handler._create_payment_methods_keyboard(1)
        
        # Проверяем, что клавиатура создана
        assert keyboard is not None
        assert hasattr(keyboard, 'keyboard')
        
        # Проверяем, что есть кнопки для всех способов оплаты
        button_texts = []
        for row in keyboard.keyboard:
            for button in row:
                button_texts.append(button.text)
        
        # Должны быть кнопки для всех 3 способов оплаты + кнопка "Назад"
        assert len([text for text in button_texts if 'Карта' in text or 'Криптовалюта' in text]) == 3
        assert any('Назад' in text for text in button_texts)


class TestUtilityMethods(TestTelegramBotHandler):
    """Тесты вспомогательных методов"""
    
    def test_ensure_user_exists_new_user(self, bot_handler, mock_db_service):
        """Тест создания нового пользователя"""
        # Настройка моков
        telegram_user = Mock()
        telegram_user.id = 123456789
        telegram_user.username = 'testuser'
        telegram_user.first_name = 'Test'
        telegram_user.last_name = 'User'
        
        mock_db_service.get_user_by_telegram_id.return_value = None
        mock_db_service.create_user.return_value = True
        
        # Выполнение
        bot_handler._ensure_user_exists(telegram_user)
        
        # Проверки
        mock_db_service.get_user_by_telegram_id.assert_called_once_with(123456789)
        mock_db_service.create_user.assert_called_once()
    
    def test_ensure_user_exists_existing_user(self, bot_handler, mock_db_service):
        """Тест для существующего пользователя"""
        # Настройка моков
        telegram_user = Mock()
        telegram_user.id = 123456789
        
        existing_user = User(id=1, telegram_id=123456789, username='testuser')
        mock_db_service.get_user_by_telegram_id.return_value = existing_user
        
        # Выполнение
        bot_handler._ensure_user_exists(telegram_user)
        
        # Проверки
        mock_db_service.get_user_by_telegram_id.assert_called_once_with(123456789)
        mock_db_service.create_user.assert_not_called()
    
    def test_get_plan_display_name(self, bot_handler):
        """Тест получения отображаемого названия плана"""
        assert bot_handler._get_plan_display_name('monthly') == '1 месяц'
        assert bot_handler._get_plan_display_name('quarterly') == '3 месяца'
        assert bot_handler._get_plan_display_name('semi_annual') == '6 месяцев'
        assert bot_handler._get_plan_display_name('yearly') == '12 месяцев'
        assert bot_handler._get_plan_display_name('unknown') == 'unknown'
    
    def test_get_status_advice(self, bot_handler):
        """Тест получения совета по статусу подписки"""
        # Тест для разных количеств дней
        advice_long = bot_handler._get_status_advice(45)
        assert 'активна' in advice_long.lower()
        
        advice_medium = bot_handler._get_status_advice(15)
        assert 'продлить' in advice_medium.lower()
        
        advice_short = bot_handler._get_status_advice(3)
        assert 'скоро истечет' in advice_short.lower()
        
        advice_today = bot_handler._get_status_advice(1)
        assert 'истекает сегодня' in advice_today.lower()
    
    def test_send_error_message(self, bot_handler):
        """Тест отправки сообщения об ошибке"""
        # Выполнение
        bot_handler._send_error_message(123456789, "Тестовая ошибка")
        
        # Проверки
        bot_handler.bot.send_message.assert_called_once()
        args, kwargs = bot_handler.bot.send_message.call_args
        
        assert args[0] == 123456789
        assert 'Произошла ошибка' in args[1]
        assert 'Тестовая ошибка' in args[1]
        assert kwargs['reply_markup'] is not None


class TestBotLifecycle(TestTelegramBotHandler):
    """Тесты жизненного цикла бота"""
    
    def test_send_message_success(self, bot_handler):
        """Тест успешной отправки сообщения"""
        # Настройка мока
        bot_handler.bot.send_message.return_value = True
        
        # Выполнение
        result = bot_handler.send_message(123456789, "Тестовое сообщение")
        
        # Проверки
        assert result is True
        bot_handler.bot.send_message.assert_called_once_with(
            123456789,
            "Тестовое сообщение",
            parse_mode='HTML',
            reply_markup=None
        )
    
    def test_send_message_failure(self, bot_handler):
        """Тест неудачной отправки сообщения"""
        # Настройка мока для ошибки
        bot_handler.bot.send_message.side_effect = Exception("Send error")
        
        # Выполнение
        result = bot_handler.send_message(123456789, "Тестовое сообщение")
        
        # Проверки
        assert result is False
        bot_handler.bot.send_message.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__])