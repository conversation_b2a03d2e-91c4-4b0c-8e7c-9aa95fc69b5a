{% extends "base.html" %}

{% block title %}Настройки системы{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-gear"></i> Настройки системы
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button class="btn btn-outline-primary btn-sm" onclick="initializeDefaults()">
            <i class="bi bi-arrow-clockwise"></i> Инициализировать по умолчанию
        </button>
    </div>
</div>

<!-- Навигационные вкладки -->
<ul class="nav nav-tabs" id="settingsTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
            <i class="bi bi-gear"></i> Системные настройки
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="plans-tab" data-bs-toggle="tab" data-bs-target="#plans" type="button" role="tab">
            <i class="bi bi-credit-card"></i> Тарифные планы
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="texts-tab" data-bs-toggle="tab" data-bs-target="#texts" type="button" role="tab">
            <i class="bi bi-chat-text"></i> Тексты бота
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
            <i class="bi bi-shield-check"></i> Резервное копирование
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="scheduler-tab" data-bs-toggle="tab" data-bs-target="#scheduler" type="button" role="tab">
            <i class="bi bi-clock"></i> Планировщик
        </button>
    </li>
</ul>

<div class="tab-content" id="settingsTabContent">
    <!-- Системные настройки -->
    <div class="tab-pane fade show active" id="system" role="tabpanel">
        <div class="row mt-3">
            {% for category, settings in all_settings.items() %}
            <div class="col-md-6 mb-4">
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-{{ 'api' if category == 'api' else 'robot' if category == 'bot' else 'person-check' if category == 'subscription' else 'gear' }}"></i>
                            {{ 'API настройки' if category == 'api' else 'Настройки бота' if category == 'bot' else 'Настройки подписок' if category == 'subscription' else 'Системные настройки' if category == 'system' else category.title() }}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% for setting in settings %}
                        <div class="mb-3">
                            <label class="form-label">
                                <strong>{{ setting.key.replace('_', ' ').title() }}</strong>
                                {% if setting.description %}
                                <small class="text-muted d-block">{{ setting.description }}</small>
                                {% endif %}
                            </label>
                            {% if setting.data_type == 'boolean' %}
                            <div class="form-check">
                                <input class="form-check-input setting-input" type="checkbox" 
                                       data-category="{{ category }}" data-key="{{ setting.key }}" data-type="{{ setting.data_type }}"
                                       {{ 'checked' if setting.value.lower() == 'true' else '' }}>
                            </div>
                            {% elif setting.data_type == 'integer' %}
                            <input type="number" class="form-control setting-input" 
                                   data-category="{{ category }}" data-key="{{ setting.key }}" data-type="{{ setting.data_type }}"
                                   value="{{ setting.value }}">
                            {% elif setting.data_type == 'float' %}
                            <input type="number" step="0.01" class="form-control setting-input" 
                                   data-category="{{ category }}" data-key="{{ setting.key }}" data-type="{{ setting.data_type }}"
                                   value="{{ setting.value }}">
                            {% else %}
                            <input type="text" class="form-control setting-input" 
                                   data-category="{{ category }}" data-key="{{ setting.key }}" data-type="{{ setting.data_type }}"
                                   value="{{ setting.value }}">
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="row">
            <div class="col-12">
                <button class="btn btn-primary" onclick="saveSystemSettings()">
                    <i class="bi bi-save"></i> Сохранить настройки
                </button>
            </div>
        </div>
    </div>

    <!-- Тарифные планы -->
    <div class="tab-pane fade" id="plans" role="tabpanel">
        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Управление тарифными планами</h5>
                    <button class="btn btn-success" onclick="showCreatePlanModal()">
                        <i class="bi bi-plus"></i> Добавить план
                    </button>
                </div>
                
                <div class="card shadow">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="plansTable">
                                <thead>
                                    <tr>
                                        <th>Название</th>
                                        <th>Продолжительность</th>
                                        <th>Цена</th>
                                        <th>Статус</th>
                                        <th>Порядок</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for plan in subscription_plans %}
                                    <tr data-plan-id="{{ plan.id }}">
                                        <td>{{ plan.name }}</td>
                                        <td>{{ plan.duration_months }} мес.</td>
                                        <td>{{ plan.price }} {{ plan.currency }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if plan.is_active else 'secondary' }}">
                                                {{ 'Активен' if plan.is_active else 'Неактивен' }}
                                            </span>
                                        </td>
                                        <td>{{ plan.sort_order }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editPlan({{ plan.id }})">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deletePlan({{ plan.id }})">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Тексты бота -->
    <div class="tab-pane fade" id="texts" role="tabpanel">
        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Управление текстами бота</h5>
                    <button class="btn btn-success" onclick="showCreateTextModal()">
                        <i class="bi bi-plus"></i> Добавить текст
                    </button>
                </div>
                
                <div class="row">
                    {% for text in bot_texts %}
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="m-0">{{ text.key }}</h6>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editText('{{ text.key }}')">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteText('{{ text.key }}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                {% if text.description %}
                                <small class="text-muted">{{ text.description }}</small>
                                {% endif %}
                                <div class="mt-2">
                                    <textarea class="form-control" rows="3" readonly>{{ text.text }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Резервное копирование -->
    <div class="tab-pane fade" id="backup" role="tabpanel">
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-download"></i> Создание резервной копии
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>Создайте резервную копию базы данных для обеспечения безопасности ваших данных.</p>
                        <button class="btn btn-primary" onclick="createBackup()">
                            <i class="bi bi-download"></i> Создать резервную копию
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-upload"></i> Восстановление
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>Восстановите базу данных из ранее созданной резервной копии.</p>
                        <button class="btn btn-warning" onclick="loadBackupsList()">
                            <i class="bi bi-list"></i> Показать резервные копии
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-archive"></i> Список резервных копий
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="backupsList">
                            <p class="text-muted">Нажмите "Показать резервные копии" для загрузки списка</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Планировщик -->
    <div class="tab-pane fade" id="scheduler" role="tabpanel">
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-clock"></i> Статус планировщика
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if scheduler_status %}
                        <div class="mb-3">
                            <strong>Статус:</strong>
                            <span class="badge bg-{{ 'success' if scheduler_status.running else 'danger' }}">
                                {{ 'Работает' if scheduler_status.running else 'Остановлен' }}
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Количество задач:</strong> {{ scheduler_status.jobs_count or 0 }}
                        </div>
                        
                        {% if scheduler_status.stats %}
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 text-success">{{ scheduler_status.stats.executed or 0 }}</div>
                                    <small class="text-muted">Выполнено</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 text-danger">{{ scheduler_status.stats.errors or 0 }}</div>
                                    <small class="text-muted">Ошибок</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% else %}
                        <p class="text-muted">Информация о планировщике недоступна</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-tools"></i> Системные операции
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="runCleanupTasks()">
                                <i class="bi bi-trash"></i> Запустить очистку данных
                            </button>
                            
                            <button class="btn btn-warning" onclick="refreshStats()">
                                <i class="bi bi-arrow-clockwise"></i> Обновить статистику
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {% if scheduler_status and scheduler_status.jobs %}
        <div class="row mt-3">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-list-task"></i> Активные задачи
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Название</th>
                                        <th>ID</th>
                                        <th>Следующий запуск</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for job in scheduler_status.jobs %}
                                    <tr>
                                        <td>{{ job.name }}</td>
                                        <td><code>{{ job.id }}</code></td>
                                        <td>
                                            {% if job.next_run_time %}
                                            {{ job.next_run_time }}
                                            {% else %}
                                            <span class="text-muted">Не запланирован</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Модальные окна -->
<!-- Модальное окно создания/редактирования тарифного плана -->
<div class="modal fade" id="planModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="planModalTitle">Создать тарифный план</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="planForm">
                    <input type="hidden" id="planId">
                    <div class="mb-3">
                        <label class="form-label">Название</label>
                        <input type="text" class="form-control" id="planName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Продолжительность (месяцы)</label>
                        <input type="number" class="form-control" id="planDuration" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Цена</label>
                        <input type="number" class="form-control" id="planPrice" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Валюта</label>
                        <select class="form-control" id="planCurrency">
                            <option value="RUB">RUB</option>
                            <option value="USD">USD</option>
                            <option value="EUR">EUR</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Описание</label>
                        <textarea class="form-control" id="planDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Порядок сортировки</label>
                        <input type="number" class="form-control" id="planSortOrder" value="0">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="planIsActive" checked>
                        <label class="form-check-label">Активен</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" onclick="savePlan()">Сохранить</button>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно создания/редактирования текста бота -->
<div class="modal fade" id="textModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="textModalTitle">Создать текст бота</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="textForm">
                    <div class="mb-3">
                        <label class="form-label">Ключ</label>
                        <input type="text" class="form-control" id="textKey" required>
                        <small class="form-text text-muted">Уникальный идентификатор текста (например: welcome, payment_success)</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Описание</label>
                        <input type="text" class="form-control" id="textDescription">
                        <small class="form-text text-muted">Краткое описание назначения текста</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Текст</label>
                        <textarea class="form-control" id="textContent" rows="5" required></textarea>
                        <small class="form-text text-muted">Поддерживаются эмодзи и форматирование Telegram</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" onclick="saveText()">Сохранить</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Глобальные переменные
let currentPlanId = null;
let currentTextKey = null;

// Системные настройки
function saveSystemSettings() {
    const settings = {};
    
    document.querySelectorAll('.setting-input').forEach(input => {
        const category = input.dataset.category;
        const key = input.dataset.key;
        const type = input.dataset.type;
        
        if (!settings[category]) {
            settings[category] = {};
        }
        
        let value;
        if (type === 'boolean') {
            value = input.checked ? 'true' : 'false';
        } else {
            value = input.value;
        }
        
        settings[category][key] = {
            value: value,
            data_type: type
        };
    });
    
    fetch('/admin/api/settings/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({settings: settings})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Настройки сохранены успешно!', 'success');
        } else {
            showAlert('Ошибка: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Ошибка: ' + error, 'danger');
    });
}

// Тарифные планы
function showCreatePlanModal() {
    currentPlanId = null;
    document.getElementById('planModalTitle').textContent = 'Создать тарифный план';
    document.getElementById('planForm').reset();
    document.getElementById('planId').value = '';
    new bootstrap.Modal(document.getElementById('planModal')).show();
}

function editPlan(planId) {
    currentPlanId = planId;
    document.getElementById('planModalTitle').textContent = 'Редактировать тарифный план';
    
    // Получаем данные плана из таблицы
    const row = document.querySelector(`tr[data-plan-id="${planId}"]`);
    if (row) {
        const cells = row.cells;
        document.getElementById('planId').value = planId;
        document.getElementById('planName').value = cells[0].textContent;
        document.getElementById('planDuration').value = parseInt(cells[1].textContent);
        
        // Парсим цену и валюту
        const priceText = cells[2].textContent.split(' ');
        document.getElementById('planPrice').value = parseFloat(priceText[0]);
        document.getElementById('planCurrency').value = priceText[1] || 'RUB';
        
        document.getElementById('planIsActive').checked = cells[3].textContent.includes('Активен');
        document.getElementById('planSortOrder').value = parseInt(cells[4].textContent);
    }
    
    new bootstrap.Modal(document.getElementById('planModal')).show();
}

function savePlan() {
    const formData = {
        name: document.getElementById('planName').value,
        duration_months: parseInt(document.getElementById('planDuration').value),
        price: parseFloat(document.getElementById('planPrice').value),
        currency: document.getElementById('planCurrency').value,
        description: document.getElementById('planDescription').value,
        is_active: document.getElementById('planIsActive').checked,
        sort_order: parseInt(document.getElementById('planSortOrder').value)
    };
    
    const url = currentPlanId ? 
        `/admin/api/subscription-plans/${currentPlanId}` : 
        '/admin/api/subscription-plans';
    const method = currentPlanId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('planModal')).hide();
            location.reload(); // Перезагружаем страницу для обновления таблицы
        } else {
            showAlert('Ошибка: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Ошибка: ' + error, 'danger');
    });
}

function deletePlan(planId) {
    if (confirm('Вы уверены, что хотите удалить этот тарифный план?')) {
        fetch(`/admin/api/subscription-plans/${planId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert('Ошибка: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showAlert('Ошибка: ' + error, 'danger');
        });
    }
}

// Тексты бота
function showCreateTextModal() {
    currentTextKey = null;
    document.getElementById('textModalTitle').textContent = 'Создать текст бота';
    document.getElementById('textForm').reset();
    document.getElementById('textKey').readOnly = false;
    new bootstrap.Modal(document.getElementById('textModal')).show();
}

function editText(key) {
    currentTextKey = key;
    document.getElementById('textModalTitle').textContent = 'Редактировать текст бота';
    document.getElementById('textKey').value = key;
    document.getElementById('textKey').readOnly = true;
    
    // Находим карточку с текстом
    const cards = document.querySelectorAll('.card');
    for (let card of cards) {
        const header = card.querySelector('.card-header h6');
        if (header && header.textContent === key) {
            const description = card.querySelector('.text-muted');
            const textarea = card.querySelector('textarea');
            
            if (description) {
                document.getElementById('textDescription').value = description.textContent;
            }
            if (textarea) {
                document.getElementById('textContent').value = textarea.value;
            }
            break;
        }
    }
    
    new bootstrap.Modal(document.getElementById('textModal')).show();
}

function saveText() {
    const formData = {
        key: document.getElementById('textKey').value,
        text: document.getElementById('textContent').value,
        description: document.getElementById('textDescription').value
    };
    
    fetch('/admin/api/bot-texts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('textModal')).hide();
            location.reload();
        } else {
            showAlert('Ошибка: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Ошибка: ' + error, 'danger');
    });
}

function deleteText(key) {
    if (confirm('Вы уверены, что хотите удалить этот текст?')) {
        fetch(`/admin/api/bot-texts/${key}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert('Ошибка: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showAlert('Ошибка: ' + error, 'danger');
        });
    }
}

// Резервное копирование
function createBackup() {
    if (confirm('Создать резервную копию базы данных?')) {
        fetch('/admin/api/backup/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Резервная копия создана: ' + data.backup_file, 'success');
                loadBackupsList(); // Обновляем список
            } else {
                showAlert('Ошибка: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showAlert('Ошибка: ' + error, 'danger');
        });
    }
}

function loadBackupsList() {
    fetch('/admin/api/backup/list')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const backupsDiv = document.getElementById('backupsList');
            if (data.backups.length === 0) {
                backupsDiv.innerHTML = '<p class="text-muted">Резервные копии не найдены</p>';
            } else {
                let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr><th>Файл</th><th>Размер</th><th>Дата создания</th><th>Действия</th></tr></thead><tbody>';
                
                data.backups.forEach(backup => {
                    html += `<tr>
                        <td>${backup.filename}</td>
                        <td>${backup.size_mb} МБ</td>
                        <td>${new Date(backup.created_at).toLocaleString()}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="restoreBackup('${backup.filename}')">
                                <i class="bi bi-upload"></i> Восстановить
                            </button>
                        </td>
                    </tr>`;
                });
                
                html += '</tbody></table></div>';
                backupsDiv.innerHTML = html;
            }
        } else {
            showAlert('Ошибка: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Ошибка: ' + error, 'danger');
    });
}

function restoreBackup(filename) {
    if (confirm(`Восстановить базу данных из резервной копии "${filename}"? Это действие нельзя отменить!`)) {
        fetch('/admin/api/backup/restore', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({backup_filename: filename})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
            } else {
                showAlert('Ошибка: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showAlert('Ошибка: ' + error, 'danger');
        });
    }
}

// Системные операции
function runCleanupTasks() {
    if (confirm('Запустить задачи очистки данных?')) {
        fetch('/admin/cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Задачи очистки выполнены успешно!', 'success');
            } else {
                showAlert('Ошибка: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showAlert('Ошибка: ' + error, 'danger');
        });
    }
}

function refreshStats() {
    location.reload();
}

function initializeDefaults() {
    if (confirm('Инициализировать настройки по умолчанию? Это добавит отсутствующие настройки, но не изменит существующие.')) {
        fetch('/admin/api/initialize-defaults', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert('Ошибка: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showAlert('Ошибка: ' + error, 'danger');
        });
    }
}

// Утилиты
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Вставляем alert в начало контента
    const content = document.querySelector('.tab-content');
    content.insertBefore(alertDiv, content.firstChild);
    
    // Автоматически скрываем через 5 секунд
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}