$password = "dkomqgTaijxro7in^bxd"
$server = "**************"
$user = "ubuntu"

# Создаем временный файл с паролем
$passwordFile = "temp_password.txt"
$password | Out-File -FilePath $passwordFile -Encoding ASCII

# Загружаем архив на сервер
Write-Host "Загружаем файлы на сервер..."
pscp -pw $password -batch telegram_bot_deploy.tar.gz ubuntu@**************:/home/<USER>/

# Подключаемся к серверу и выполняем команды
Write-Host "Подключаемся к серверу..."
$commands = @"
cd /home/<USER>
tar -xzf telegram_bot_deploy.tar.gz
sudo apt update
sudo apt install -y python3 python3-pip python3-venv nginx
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl enable nginx
sudo systemctl start nginx
"@

$commands | plink -pw $password ubuntu@**************

# Remove temporary file
Remove-Item $passwordFile -Force