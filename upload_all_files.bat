@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Uploading all project files to /home/<USER>/app/...
echo.
echo Uploading main files:
pscp -pw %PASSWORD% app.py ubuntu@**************:/home/<USER>/app.py
pscp -pw %PASSWORD% main.py ubuntu@**************:/home/<USER>/main.py
pscp -pw %PASSWORD% config.py ubuntu@**************:/home/<USER>/config.py
pscp -pw %PASSWORD% requirements.txt ubuntu@**************:/home/<USER>/requirements.txt
pscp -pw %PASSWORD% .env ubuntu@**************:/home/<USER>/.env
echo.
echo Uploading app directory:
pscp -pw %PASSWORD% -r app ubuntu@**************:/home/<USER>/app_folder
echo.
echo Uploading templates:
pscp -pw %PASSWORD% -r templates ubuntu@**************:/home/<USER>/templates
echo.
echo Moving files to telegrambot directory:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S mv /home/<USER>/app.py /home/<USER>/app/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/main.py /home/<USER>/app/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/config.py /home/<USER>/app/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/requirements.txt /home/<USER>/app/ && echo '%PASSWORD%' | sudo -S mv /home/<USER>/.env /home/<USER>/app/"
echo.
echo Moving directories:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S mv /home/<USER>/app_folder /home/<USER>/app/app && echo '%PASSWORD%' | sudo -S mv /home/<USER>/templates /home/<USER>/app/"
echo.
echo Setting ownership:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S chown -R telegrambot:telegrambot /home/<USER>/"
echo Done.