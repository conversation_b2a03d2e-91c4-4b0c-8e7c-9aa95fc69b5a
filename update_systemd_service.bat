@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Updating systemd service configuration...
echo.
echo Updating telegram-payment-bot.service:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S tee /etc/systemd/system/telegram-payment-bot.service > /dev/null << 'EOF'
[Unit]
Description=Telegram Payment Bot
After=network.target

[Service]
Type=simple
User=telegrambot
Group=telegrambot
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/start.sh
Restart=always
RestartSec=10
Environment=PYTHONPATH=/home/<USER>/app

[Install]
WantedBy=multi-user.target
EOF"
echo.
echo Reloading systemd daemon:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S systemctl daemon-reload"
echo.
echo Enabling service:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S systemctl enable telegram-payment-bot"
echo.
echo Starting service:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S systemctl start telegram-payment-bot"
echo.
echo Checking service status:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S systemctl status telegram-payment-bot --no-pager"
echo Done.