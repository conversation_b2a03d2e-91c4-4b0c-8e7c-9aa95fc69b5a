{% extends "base.html" %}

{% block title %}Подписки{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-calendar-check"></i> Управление подписками
    </h1>
</div>

<div class="card shadow">
    <div class="card-body">
        {% if subscriptions %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Пользователь</th>
                        <th>Тип</th>
                        <th>Статус</th>
                        <th>Начало</th>
                        <th>Окончание</th>
                        <th>Действия</th>
                    </tr>
                </thead>
                <tbody>
                    {% for subscription in subscriptions %}
                    <tr>
                        <td>{{ subscription.id }}</td>
                        <td>{{ subscription.user_id }}</td>
                        <td>{{ subscription.plan_type }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if subscription.status == 'active' else 'danger' }}">
                                {{ subscription.status }}
                            </span>
                        </td>
                        <td>{{ subscription.start_date.strftime('%d.%m.%Y') if subscription.start_date else '-' }}</td>
                        <td>{{ subscription.end_date.strftime('%d.%m.%Y') if subscription.end_date else '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-warning" title="Продлить">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="Отменить">
                                    <i class="bi bi-x-circle"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-calendar-x text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">Подписки не найдены</h4>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}