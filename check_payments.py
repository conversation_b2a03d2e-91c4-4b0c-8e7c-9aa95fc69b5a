#!/usr/bin/env python3
"""
Скрипт для проверки статуса зависших и истекших платежей
Может быть запущен как cron job или scheduled task
"""

import sys
import os
import logging
from datetime import datetime

# Добавляем корневую директорию в путь для импорта модулей
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.payment_service import PaymentService
from app.models.database import DatabaseService
from config import Config

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('payment_checker.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Основная функция для проверки платежей"""
    try:
        logger.info("Запуск проверки платежей...")
        
        # Инициализируем сервисы
        payment_service = PaymentService()
        db_service = DatabaseService()
        
        # Проверяем соединение с базой данных
        if not db_service.check_connection():
            logger.error("Не удалось подключиться к базе данных")
            return 1
        
        # Проверяем зависшие платежи
        logger.info("Проверка зависших платежей...")
        stuck_result = payment_service.check_stuck_payments(db_service)
        
        if stuck_result['success']:
            logger.info(f"Проверено зависших платежей: {stuck_result['checked_count']}")
            logger.info(f"Обновлено платежей: {stuck_result['updated_count']}")
            
            if stuck_result['errors']:
                logger.warning(f"Ошибки при проверке: {len(stuck_result['errors'])}")
                for error in stuck_result['errors']:
                    logger.warning(f"  - {error}")
        else:
            logger.error(f"Ошибка проверки зависших платежей: {stuck_result.get('error', 'Unknown error')}")
        
        # Проверяем истекшие платежи
        logger.info("Проверка истекших платежей...")
        expired_result = payment_service.check_expired_payments(db_service)
        
        if expired_result['success']:
            logger.info(f"Обработано истекших платежей: {expired_result['processed_count']}")
        else:
            logger.error(f"Ошибка проверки истекших платежей: {expired_result.get('error', 'Unknown error')}")
        
        logger.info("Проверка платежей завершена")
        return 0
        
    except Exception as e:
        logger.error(f"Критическая ошибка при проверке платежей: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)