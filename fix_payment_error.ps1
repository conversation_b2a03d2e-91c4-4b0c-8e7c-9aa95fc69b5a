# PowerShell скрипт для исправления ошибки "неверные параметры" в платежах
# Использование: .\fix_payment_error.ps1

param(
    [string]$Server = "**************",
    [string]$User = "ubuntu",
    [string]$ProjectDir = "/home/<USER>/telegram_bot",
    [string]$ServiceName = "telegram-bot"
)

Write-Host "🔧 Исправление ошибки платежей на сервере..." -ForegroundColor Green

# Функция для выполнения SSH команд
function Invoke-SSH {
    param([string]$Command)
    
    $sshArgs = @(
        "-o", "StrictHostKeyChecking=no",
        "$User@$Server",
        $Command
    )
    
    & ssh $sshArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Ошибка выполнения команды: $Command" -ForegroundColor Red
        throw "SSH команда завершилась с ошибкой"
    }
}

# Функция для загрузки файлов
function Copy-ToServer {
    param([string]$LocalPath, [string]$RemotePath)
    
    $scpArgs = @(
        "-o", "StrictHostKeyChecking=no",
        $LocalPath,
        "$User@${Server}:$RemotePath"
    )
    
    & scp $scpArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Ошибка загрузки файла: $LocalPath" -ForegroundColor Red
        throw "SCP команда завершилась с ошибкой"
    }
}

try {
    Write-Host "📦 Проверка архива с исправлениями..." -ForegroundColor Yellow
    
    if (-not (Test-Path "payment_fix.zip")) {
        Write-Host "❌ Архив payment_fix.zip не найден!" -ForegroundColor Red
        Write-Host "Создание архива..." -ForegroundColor Yellow
        Compress-Archive -Path "app/services/payment_service.py", "app/services/bot_handler.py", "app/admin.py", "templates/" -DestinationPath "payment_fix.zip" -Force
    }
    
    Write-Host "✅ Архив готов" -ForegroundColor Green

    Write-Host "📤 Загрузка исправлений на сервер..." -ForegroundColor Yellow
    Copy-ToServer "payment_fix.zip" "/home/<USER>/"
    Write-Host "✅ Исправления загружены" -ForegroundColor Green

    Write-Host "🛑 Остановка сервиса..." -ForegroundColor Yellow
    Invoke-SSH "sudo systemctl stop $ServiceName"
    Write-Host "✅ Сервис остановлен" -ForegroundColor Green

    Write-Host "💾 Создание резервной копии..." -ForegroundColor Yellow
    $backupCmd = "cd $ProjectDir; sudo cp -r app templates backup_payment_fix_`$(date +%Y%m%d_%H%M%S)"
    Invoke-SSH $backupCmd
    Write-Host "✅ Резервная копия создана" -ForegroundColor Green

    Write-Host "📦 Распаковка исправлений..." -ForegroundColor Yellow
    Invoke-SSH "cd $ProjectDir; unzip -o /home/<USER>/payment_fix.zip"
    Write-Host "✅ Исправления распакованы" -ForegroundColor Green

    Write-Host "🔐 Установка прав доступа..." -ForegroundColor Yellow
    Invoke-SSH "cd $ProjectDir; sudo chown -R ubuntu:ubuntu app templates; sudo chmod -R 755 app templates"
    Write-Host "✅ Права доступа установлены" -ForegroundColor Green

    Write-Host "🔍 Проверка исправлений..." -ForegroundColor Yellow
    try {
        Invoke-SSH "cd $ProjectDir; grep -q 'customFields' app/services/payment_service.py"
        Write-Host "✅ payment_service.py исправлен" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ payment_service.py может быть не обновлен" -ForegroundColor Yellow
    }
    
    try {
        Invoke-SSH "cd $ProjectDir; grep -q 'callback_data\[2\]' app/services/bot_handler.py"
        Write-Host "✅ bot_handler.py исправлен" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ bot_handler.py может быть не обновлен" -ForegroundColor Yellow
    }

    Write-Host "🚀 Запуск сервиса..." -ForegroundColor Yellow
    Invoke-SSH "sudo systemctl start $ServiceName"
    Write-Host "✅ Сервис запущен" -ForegroundColor Green

    Write-Host "⏳ Ожидание запуска сервиса..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10

    Write-Host "📊 Проверка статуса сервиса..." -ForegroundColor Yellow
    Invoke-SSH "sudo systemctl status $ServiceName --no-pager -l"

    Write-Host "📝 Последние логи сервиса:" -ForegroundColor Yellow
    Invoke-SSH "sudo journalctl -u $ServiceName -n 15 --no-pager"

    Write-Host ""
    Write-Host "🎉 Исправление ошибки платежей завершено!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Что было исправлено:" -ForegroundColor Cyan
    Write-Host "• Исправлены параметры API для создания счетов Lava.top" -ForegroundColor White
    Write-Host "• Улучшена обработка способов оплаты в боте" -ForegroundColor White
    Write-Host "• Обновлена структура данных для платежей" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 Тестирование:" -ForegroundColor Cyan
    Write-Host "1. Откройте бот в Telegram" -ForegroundColor White
    Write-Host "2. Попробуйте создать платеж" -ForegroundColor White
    Write-Host "3. Проверьте, что ошибка 'неверные параметры' исчезла" -ForegroundColor White
    Write-Host ""
    Write-Host "🔍 Для просмотра логов в реальном времени:" -ForegroundColor Cyan
    Write-Host "ssh $User@$Server 'sudo journalctl -u $ServiceName -f'" -ForegroundColor White

} catch {
    Write-Host ""
    Write-Host "💥 Ошибка при исправлении: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Попробуйте:" -ForegroundColor Yellow
    Write-Host "1. Проверьте подключение к серверу: ssh $User@$Server" -ForegroundColor White
    Write-Host "2. Убедитесь, что сервис запущен: ssh $User@$Server 'sudo systemctl status $ServiceName'" -ForegroundColor White
    Write-Host "3. Проверьте логи: ssh $User@$Server 'sudo journalctl -u $ServiceName -n 20'" -ForegroundColor White
    
    exit 1
}