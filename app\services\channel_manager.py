"""
Channel Manager для управления доступом к Telegram каналу
"""

import logging
import telebot
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from config import Config

logger = logging.getLogger('channel_manager')

class ChannelManager:
    """Менеджер для управления доступом к Telegram каналу"""
    
    def __init__(self, bot_token: Optional[str] = None, channel_id: Optional[str] = None):
        """
        Инициализация менеджера канала
        
        Args:
            bot_token: Токен Telegram бота (по умолчанию из конфига)
            channel_id: ID канала (по умолчанию из конфига)
        """
        self.bot = telebot.TeleBot(bot_token or Config.TELEGRAM_BOT_TOKEN)
        self.channel_id = channel_id or getattr(Config, 'CHANNEL_ID', None)
        
        if not self.channel_id:
            logger.warning("CHANNEL_ID не настроен в конфигурации")
        
        logger.info(f"ChannelManager инициализирован для канала {self.channel_id}")
    
    def create_invite_link(self, user_id: int, expire_hours: int = 24, member_limit: int = 1) -> Optional[str]:
        """
        Создает временную пригласительную ссылку для пользователя
        
        Args:
            user_id: ID пользователя Telegram
            expire_hours: Время действия ссылки в часах (по умолчанию 24)
            member_limit: Лимит использований ссылки (по умолчанию 1)
            
        Returns:
            Пригласительная ссылка или None в случае ошибки
        """
        try:
            if not self.channel_id:
                logger.error("Не настроен CHANNEL_ID для создания пригласительной ссылки")
                return None
            
            # Вычисляем время истечения ссылки
            expire_date = datetime.now() + timedelta(hours=expire_hours)
            expire_timestamp = int(expire_date.timestamp())
            
            # Создаем пригласительную ссылку
            invite_link = self.bot.create_chat_invite_link(
                chat_id=self.channel_id,
                name=f"Invite for user {user_id}",
                expire_date=expire_timestamp,
                member_limit=member_limit,
                creates_join_request=False
            )
            
            logger.info(f"Создана пригласительная ссылка для пользователя {user_id}: {invite_link.invite_link}")
            
            return invite_link.invite_link
            
        except telebot.apihelper.ApiTelegramException as e:
            logger.error(f"Ошибка Telegram API при создании пригласительной ссылки для пользователя {user_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Неожиданная ошибка при создании пригласительной ссылки для пользователя {user_id}: {e}")
            return None
    
    def remove_user_from_channel(self, user_id: int) -> bool:
        """
        Удаляет пользователя из канала
        
        Args:
            user_id: ID пользователя Telegram
            
        Returns:
            True если пользователь успешно удален, False в случае ошибки
        """
        try:
            if not self.channel_id:
                logger.error("Не настроен CHANNEL_ID для удаления пользователя")
                return False
            
            # Удаляем пользователя из канала
            self.bot.ban_chat_member(
                chat_id=self.channel_id,
                user_id=user_id,
                until_date=int((datetime.now() + timedelta(seconds=30)).timestamp())  # Бан на 30 секунд
            )
            
            # Сразу разбаниваем, чтобы пользователь мог вернуться при новой подписке
            self.bot.unban_chat_member(
                chat_id=self.channel_id,
                user_id=user_id,
                only_if_banned=True
            )
            
            logger.info(f"Пользователь {user_id} удален из канала {self.channel_id}")
            return True
            
        except telebot.apihelper.ApiTelegramException as e:
            if "user not found" in str(e).lower() or "user_not_participant" in str(e).lower():
                logger.info(f"Пользователь {user_id} не найден в канале {self.channel_id}")
                return True  # Считаем успехом, если пользователя уже нет в канале
            else:
                logger.error(f"Ошибка Telegram API при удалении пользователя {user_id} из канала: {e}")
                return False
        except Exception as e:
            logger.error(f"Неожиданная ошибка при удалении пользователя {user_id} из канала: {e}")
            return False
    
    def check_user_in_channel(self, user_id: int) -> bool:
        """
        Проверяет, находится ли пользователь в канале
        
        Args:
            user_id: ID пользователя Telegram
            
        Returns:
            True если пользователь в канале, False если нет или ошибка
        """
        try:
            if not self.channel_id:
                logger.error("Не настроен CHANNEL_ID для проверки пользователя")
                return False
            
            # Получаем информацию о пользователе в канале
            member = self.bot.get_chat_member(
                chat_id=self.channel_id,
                user_id=user_id
            )
            
            # Проверяем статус пользователя
            active_statuses = ['member', 'administrator', 'creator']
            is_member = member.status in active_statuses
            
            logger.info(f"Пользователь {user_id} {'найден' if is_member else 'не найден'} в канале {self.channel_id} (статус: {member.status})")
            
            return is_member
            
        except telebot.apihelper.ApiTelegramException as e:
            if "user not found" in str(e).lower() or "user_not_participant" in str(e).lower():
                logger.info(f"Пользователь {user_id} не найден в канале {self.channel_id}")
                return False
            else:
                logger.error(f"Ошибка Telegram API при проверке пользователя {user_id} в канале: {e}")
                return False
        except Exception as e:
            logger.error(f"Неожиданная ошибка при проверке пользователя {user_id} в канале: {e}")
            return False
    
    def get_channel_info(self) -> Optional[Dict[str, Any]]:
        """
        Получает информацию о канале
        
        Returns:
            Словарь с информацией о канале или None в случае ошибки
        """
        try:
            if not self.channel_id:
                logger.error("Не настроен CHANNEL_ID для получения информации о канале")
                return None
            
            # Получаем информацию о канале
            chat = self.bot.get_chat(self.channel_id)
            
            channel_info = {
                'id': chat.id,
                'title': chat.title,
                'username': chat.username,
                'description': chat.description,
                'type': chat.type,
                'member_count': None  # Будет получено отдельно
            }
            
            # Пытаемся получить количество участников
            try:
                member_count = self.bot.get_chat_member_count(self.channel_id)
                channel_info['member_count'] = member_count
            except Exception as e:
                logger.warning(f"Не удалось получить количество участников канала: {e}")
            
            logger.info(f"Получена информация о канале {self.channel_id}: {chat.title}")
            
            return channel_info
            
        except telebot.apihelper.ApiTelegramException as e:
            logger.error(f"Ошибка Telegram API при получении информации о канале {self.channel_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Неожиданная ошибка при получении информации о канале {self.channel_id}: {e}")
            return None
    
    def revoke_invite_link(self, invite_link: str) -> bool:
        """
        Отзывает пригласительную ссылку
        
        Args:
            invite_link: Пригласительная ссылка для отзыва
            
        Returns:
            True если ссылка успешно отозвана, False в случае ошибки
        """
        try:
            if not self.channel_id:
                logger.error("Не настроен CHANNEL_ID для отзыва пригласительной ссылки")
                return False
            
            # Отзываем пригласительную ссылку
            self.bot.revoke_chat_invite_link(
                chat_id=self.channel_id,
                invite_link=invite_link
            )
            
            logger.info(f"Пригласительная ссылка отозвана: {invite_link}")
            return True
            
        except telebot.apihelper.ApiTelegramException as e:
            logger.error(f"Ошибка Telegram API при отзыве пригласительной ссылки {invite_link}: {e}")
            return False
        except Exception as e:
            logger.error(f"Неожиданная ошибка при отзыве пригласительной ссылки {invite_link}: {e}")
            return False
    
    def get_invite_links(self) -> Optional[list]:
        """
        Получает список всех активных пригласительных ссылок канала
        
        Returns:
            Список пригласительных ссылок или None в случае ошибки
        """
        try:
            if not self.channel_id:
                logger.error("Не настроен CHANNEL_ID для получения пригласительных ссылок")
                return None
            
            # Получаем список пригласительных ссылок
            invite_links = self.bot.get_chat_invite_links(self.channel_id)
            
            links_info = []
            for link in invite_links:
                links_info.append({
                    'invite_link': link.invite_link,
                    'name': link.name,
                    'creator': link.creator.id if link.creator else None,
                    'creates_join_request': link.creates_join_request,
                    'is_primary': link.is_primary,
                    'is_revoked': link.is_revoked,
                    'expire_date': link.expire_date,
                    'member_limit': link.member_limit,
                    'pending_join_request_count': link.pending_join_request_count
                })
            
            logger.info(f"Получено {len(links_info)} пригласительных ссылок для канала {self.channel_id}")
            
            return links_info
            
        except telebot.apihelper.ApiTelegramException as e:
            logger.error(f"Ошибка Telegram API при получении пригласительных ссылок канала {self.channel_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Неожиданная ошибка при получении пригласительных ссылок канала {self.channel_id}: {e}")
            return None
    
    def validate_bot_permissions(self) -> Dict[str, bool]:
        """
        Проверяет права бота в канале
        
        Returns:
            Словарь с информацией о правах бота
        """
        try:
            if not self.channel_id:
                logger.error("Не настроен CHANNEL_ID для проверки прав бота")
                return {'error': 'Channel ID not configured'}
            
            # Получаем информацию о боте в канале
            bot_info = self.bot.get_me()
            member = self.bot.get_chat_member(self.channel_id, bot_info.id)
            
            permissions = {
                'is_admin': member.status in ['administrator', 'creator'],
                'can_invite_users': getattr(member, 'can_invite_users', False),
                'can_restrict_members': getattr(member, 'can_restrict_members', False),
                'can_delete_messages': getattr(member, 'can_delete_messages', False),
                'can_manage_chat': getattr(member, 'can_manage_chat', False),
                'status': member.status
            }
            
            logger.info(f"Права бота в канале {self.channel_id}: {permissions}")
            
            return permissions
            
        except telebot.apihelper.ApiTelegramException as e:
            logger.error(f"Ошибка Telegram API при проверке прав бота в канале {self.channel_id}: {e}")
            return {'error': str(e)}
        except Exception as e:
            logger.error(f"Неожиданная ошибка при проверке прав бота в канале {self.channel_id}: {e}")
            return {'error': str(e)}