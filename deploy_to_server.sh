#!/bin/bash

# Скрипт развертывания Telegram Payment Bot на Ubuntu сервере
# Автор: AI Assistant
# Дата: $(date)

set -e  # Остановка при ошибке

echo "=== Начало развертывания Telegram Payment Bot ==="

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Функция для вывода сообщений
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Проверка прав root
if [[ $EUID -ne 0 ]]; then
   log_error "Этот скрипт должен быть запущен с правами root (sudo)"
   exit 1
fi

# 1. Обновление системы
log_info "Обновление системы..."
apt update && apt upgrade -y

# 2. Установка необходимых пакетов
log_info "Установка необходимых пакетов..."
apt install -y python3 python3-pip python3-venv nginx supervisor git curl wget unzip

# 3. Создание пользователя для приложения
log_info "Создание пользователя telegrambot..."
if ! id "telegrambot" &>/dev/null; then
    useradd -m -s /bin/bash telegrambot
    log_info "Пользователь telegrambot создан"
else
    log_warn "Пользователь telegrambot уже существует"
fi

# 4. Создание директорий
log_info "Создание директорий..."
mkdir -p /home/<USER>/app
mkdir -p /home/<USER>/backups
mkdir -p /var/log/telegram-payment-bot
mkdir -p /etc/telegram-payment-bot

# 5. Установка прав доступа
chown -R telegrambot:telegrambot /home/<USER>
chown -R telegrambot:telegrambot /var/log/telegram-payment-bot
chown -R telegrambot:telegrambot /etc/telegram-payment-bot

log_info "Базовая настройка сервера завершена"
log_info "Теперь загрузите код проекта в /home/<USER>/app"
log_info "И запустите скрипт настройки приложения"

echo "=== Базовая настройка сервера завершена ==="