#!/usr/bin/env python3
"""
Скрипт проверки готовности системы к production
"""

import os
import sys
import requests
import subprocess
from datetime import datetime

def check_environment_variables():
    """Проверяет наличие всех необходимых переменных окружения"""
    print("🔍 Проверка переменных окружения...")
    
    # Загружаем переменные из .env файла
    try:
        from config import Config
        
        required_vars = {
            'TELEGRAM_BOT_TOKEN': Config.TELEGRAM_BOT_TOKEN,
            'LAVA_API_KEY': Config.LAVA_API_KEY,
            'LAVA_SECRET_KEY': Config.LAVA_SECRET_KEY,
            'WEBHOOK_URL': Config.WEBHOOK_URL,
            'CHANNEL_ID': Config.CHANNEL_ID,
            'ADMIN_USER_IDS': Config.ADMIN_USER_IDS
        }
        
        missing_vars = []
        for var_name, var_value in required_vars.items():
            if not var_value or str(var_value) in ['your_token_here', 'your_key_here', 'None', '[]']:
                missing_vars.append(var_name)
                print(f"   ❌ {var_name}: НЕ УСТАНОВЛЕН")
            else:
                print(f"   ✅ {var_name}: УСТАНОВЛЕН")
        
        if missing_vars:
            print(f"\n❌ Отсутствуют переменные: {', '.join(missing_vars)}")
            return False
        
        print("✅ Все переменные окружения настроены")
        return True
        
    except Exception as e:
        print(f"   ❌ Ошибка загрузки конфигурации: {e}")
        return False

def check_database():
    """Проверяет доступность и состояние базы данных"""
    print("\n🗄️ Проверка базы данных...")
    
    try:
        from app.models.database import DatabaseService
        db_service = DatabaseService()
        
        # Проверяем подключение
        if db_service.check_connection():
            print("   ✅ Подключение к БД: OK")
        else:
            print("   ❌ Подключение к БД: ОШИБКА")
            return False
        
        # Проверяем таблицы
        with db_service.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['users', 'subscriptions', 'payments', 'admin_logs']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                print(f"   ❌ Отсутствуют таблицы: {', '.join(missing_tables)}")
                return False
            else:
                print("   ✅ Все таблицы созданы")
        
        print("✅ База данных готова")
        return True
        
    except Exception as e:
        print(f"   ❌ Ошибка БД: {e}")
        return False

def check_lava_api():
    """Проверяет интеграцию с Lava.top API"""
    print("\n💳 Проверка Lava.top API...")
    
    try:
        from app.services.payment_service import PaymentService
        payment_service = PaymentService()
        
        # Тест создания счета
        result = payment_service.create_invoice(123456789, 1, 'card_ru')
        
        if result['success']:
            print("   ✅ Создание счетов: OK")
            print(f"   📋 Тестовый счет: {result['order_id']}")
            return True
        else:
            print(f"   ❌ Ошибка создания счета: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"   ❌ Ошибка API: {e}")
        return False

def check_telegram_bot():
    """Проверяет Telegram бота"""
    print("\n🤖 Проверка Telegram бота...")
    
    try:
        from config import Config
        import telebot
        
        bot = telebot.TeleBot(Config.TELEGRAM_BOT_TOKEN)
        
        # Проверяем токен
        bot_info = bot.get_me()
        print(f"   ✅ Бот подключен: @{bot_info.username}")
        print(f"   📋 ID бота: {bot_info.id}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Ошибка бота: {e}")
        return False

def check_webhook_url():
    """Проверяет доступность webhook URL"""
    print("\n🌐 Проверка webhook URL...")
    
    try:
        from config import Config
        webhook_url = f"{Config.WEBHOOK_URL}/test"
        
        print(f"   🔄 Проверяем: {webhook_url}")
        
        # Попытка подключения с таймаутом
        response = requests.get(webhook_url, timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Webhook URL доступен")
            return True
        else:
            print(f"   ❌ Webhook URL недоступен (статус: {response.status_code})")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Webhook URL недоступен (нет соединения)")
        print("   💡 Убедитесь, что Flask приложение запущено")
        return False
    except Exception as e:
        print(f"   ❌ Ошибка проверки webhook: {e}")
        return False

def check_services():
    """Проверяет инициализацию всех сервисов"""
    print("\n🔧 Проверка сервисов...")
    
    try:
        from app.services.payment_service import PaymentService
        from app.services.notification_service import NotificationService
        from app.models.database import DatabaseService
        
        # Инициализация сервисов
        db_service = DatabaseService()
        payment_service = PaymentService()
        
        print("   ✅ DatabaseService: OK")
        print("   ✅ PaymentService: OK")
        
        # Проверяем доступные методы оплаты
        methods = payment_service.get_available_payment_methods()
        print(f"   ✅ Способы оплаты: {list(methods.keys())}")
        
        # Проверяем тарифные планы
        plans = payment_service.get_subscription_plans()
        print(f"   ✅ Тарифные планы: {list(plans.keys())}")
        
        print("✅ Все сервисы готовы")
        return True
        
    except Exception as e:
        print(f"   ❌ Ошибка сервисов: {e}")
        return False

def check_security():
    """Проверяет настройки безопасности"""
    print("\n🔒 Проверка безопасности...")
    
    security_score = 0
    total_checks = 5
    
    # Проверка 1: Секретные ключи не в коде
    try:
        with open('.env', 'r') as f:
            env_content = f.read()
            if 'your_token_here' not in env_content and 'your_key_here' not in env_content:
                print("   ✅ Секретные ключи настроены")
                security_score += 1
            else:
                print("   ❌ Найдены заглушки в .env файле")
    except:
        print("   ⚠️  .env файл не найден")
    
    # Проверка 2: HTTPS URL
    webhook_url = os.getenv('WEBHOOK_URL', '')
    if webhook_url.startswith('https://'):
        print("   ✅ HTTPS webhook URL")
        security_score += 1
    else:
        print("   ⚠️  HTTP webhook URL (рекомендуется HTTPS)")
    
    # Проверка 3: Админские права
    admin_ids = os.getenv('ADMIN_USER_IDS', '')
    if admin_ids and admin_ids != 'your_admin_id':
        print("   ✅ Админские ID настроены")
        security_score += 1
    else:
        print("   ❌ Админские ID не настроены")
    
    # Проверка 4: Пароль админки
    admin_password = os.getenv('ADMIN_PASSWORD', '')
    if admin_password and admin_password not in ['admin', 'password', '123']:
        print("   ✅ Пароль админки настроен")
        security_score += 1
    else:
        print("   ❌ Слабый пароль админки")
    
    # Проверка 5: SECRET_KEY
    secret_key = os.getenv('SECRET_KEY', '')
    if secret_key and len(secret_key) > 20:
        print("   ✅ SECRET_KEY настроен")
        security_score += 1
    else:
        print("   ❌ Слабый SECRET_KEY")
    
    print(f"   📊 Оценка безопасности: {security_score}/{total_checks}")
    
    return security_score >= 3

def run_integration_tests():
    """Запускает интеграционные тесты"""
    print("\n🧪 Запуск интеграционных тестов...")
    
    try:
        result = subprocess.run([
            sys.executable, 'test_bot_integration.py'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and "🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!" in result.stdout:
            print("   ✅ Интеграционные тесты: ПРОЙДЕНЫ")
            return True
        else:
            print("   ❌ Интеграционные тесты: НЕ ПРОЙДЕНЫ")
            print(f"   📋 Вывод: {result.stdout[-200:]}")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ❌ Тесты превысили время ожидания")
        return False
    except Exception as e:
        print(f"   ❌ Ошибка запуска тестов: {e}")
        return False

def main():
    """Главная функция проверки"""
    print("🚀 ПРОВЕРКА ГОТОВНОСТИ К PRODUCTION")
    print("=" * 50)
    print(f"Время проверки: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    checks = [
        ("Переменные окружения", check_environment_variables),
        ("База данных", check_database),
        ("Lava.top API", check_lava_api),
        ("Telegram бот", check_telegram_bot),
        ("Webhook URL", check_webhook_url),
        ("Сервисы", check_services),
        ("Безопасность", check_security),
        ("Интеграционные тесты", run_integration_tests)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Критическая ошибка в проверке '{check_name}': {e}")
            results.append((check_name, False))
    
    # Итоговые результаты
    print("\n" + "=" * 50)
    print("📊 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"{check_name:.<30} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Пройдено проверок: {passed}/{total}")
    
    # Оценка готовности
    readiness_percent = (passed / total) * 100
    
    if readiness_percent >= 90:
        print("🎉 СИСТЕМА ГОТОВА К PRODUCTION!")
        print("💡 Можно запускать в боевом режиме")
        
        print(f"\n🚀 Для запуска выполните:")
        print(f"   python app.py")
        
    elif readiness_percent >= 70:
        print("⚠️  СИСТЕМА ПОЧТИ ГОТОВА К PRODUCTION")
        print("💡 Исправьте критические проблемы перед запуском")
        
        print(f"\n🔧 Что нужно исправить:")
        for check_name, result in results:
            if not result:
                print(f"   • {check_name}")
                
    else:
        print("❌ СИСТЕМА НЕ ГОТОВА К PRODUCTION")
        print("💡 Необходимо исправить множественные проблемы")
    
    print(f"\n📈 Готовность к production: {readiness_percent:.1f}%")
    print("=" * 50)
    
    return readiness_percent >= 90

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)