#!/bin/bash

# Telegram Payment Bot Deployment Script
# This script will deploy the bot to the production server

set -e  # Exit on any error

# Configuration
SERVER_IP="**************"
SERVER_USER="ubuntu"
SERVER_PASSWORD="dkomqgTaijxro7in^bxd"
APP_USER="telegrambot"
APP_DIR="/home/<USER>/app"
SERVICE_NAME="telegram-payment-bot"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute commands on remote server
execute_remote() {
    local command="$1"
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "$command"
}

# Function to copy files to remote server
copy_to_remote() {
    local local_path="$1"
    local remote_path="$2"
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no -r "$local_path" "$SERVER_USER@$SERVER_IP:$remote_path"
}

echo "=== TELEGRAM PAYMENT BOT DEPLOYMENT SCRIPT ==="
echo "Server: $SERVER_IP"
echo "User: $SERVER_USER"
echo "App Directory: $APP_DIR"
echo ""

# Step 1: Test connection
print_status "Testing connection to server..."
if execute_remote "echo 'Connection successful'"; then
    print_success "Connection established"
else
    print_error "Failed to connect to server"
    exit 1
fi

# Step 2: Stop existing service
print_status "Stopping existing service..."
execute_remote "sudo systemctl stop $SERVICE_NAME || true"
execute_remote "sudo systemctl disable $SERVICE_NAME || true"

# Step 3: Create user and directories
print_status "Creating application user and directories..."
execute_remote "sudo useradd -m -s /bin/bash $APP_USER || true"
execute_remote "sudo mkdir -p $APP_DIR"
execute_remote "sudo mkdir -p $APP_DIR/logs"
execute_remote "sudo mkdir -p $APP_DIR/backups"
execute_remote "sudo chown -R $APP_USER:$APP_USER /home/<USER>"

# Step 4: Install system dependencies
print_status "Installing system dependencies..."
execute_remote "sudo apt update"
execute_remote "sudo apt install -y python3 python3-pip python3-venv nginx supervisor"

# Step 5: Create and activate virtual environment
print_status "Creating Python virtual environment..."
execute_remote "sudo -u $APP_USER python3 -m venv $APP_DIR/venv"
execute_remote "sudo -u $APP_USER $APP_DIR/venv/bin/pip install --upgrade pip"

# Step 6: Copy application files
print_status "Copying application files..."
# Create temporary archive
tar -czf telegram_bot_app.tar.gz \
    app.py \
    config.py \
    admin_commands.py \
    check_payments.py \
    requirements.txt \
    app/ \
    --exclude='*.pyc' \
    --exclude='__pycache__' \
    --exclude='*.log' \
    --exclude='*.db'

copy_to_remote "telegram_bot_app.tar.gz" "/tmp/"
execute_remote "cd $APP_DIR && sudo -u $APP_USER tar -xzf /tmp/telegram_bot_app.tar.gz"
execute_remote "sudo chown -R $APP_USER:$APP_USER $APP_DIR"
rm telegram_bot_app.tar.gz

# Step 7: Install Python dependencies
print_status "Installing Python dependencies..."
execute_remote "sudo -u $APP_USER $APP_DIR/venv/bin/pip install -r $APP_DIR/requirements.txt"

# Step 8: Create production environment file
print_status "Creating production environment configuration..."
execute_remote "sudo -u $APP_USER tee $APP_DIR/.env.production > /dev/null << 'EOF'
# Production Environment Configuration
FLASK_ENV=production
FLASK_DEBUG=false

# Telegram Bot Token (REQUIRED - set your actual token)
TELEGRAM_BOT_TOKEN=YOUR_BOT_TOKEN_HERE

# Lava.top API Configuration (REQUIRED - set your actual keys)
LAVA_API_KEY=YOUR_LAVA_API_KEY_HERE
LAVA_SECRET_KEY=YOUR_LAVA_SECRET_KEY_HERE
LAVA_BASE_URL=https://api.lava.top

# Webhook Configuration (REQUIRED - set your actual domain)
WEBHOOK_URL=https://your-domain.com/webhook

# Admin Configuration
ADMIN_PASSWORD=admin123
ADMIN_USER_IDS=123456789
NOTIFICATION_CHAT_ID=123456789

# Channel Configuration (REQUIRED - set your actual channel ID)
CHANNEL_ID=-1001234567890

# Database Configuration
DATABASE_URL=sqlite:///payments.db

# Subscription Plans (prices in rubles)
PLAN_1_MONTHS=1
PLAN_1_PRICE=299
PLAN_3_MONTHS=3
PLAN_3_PRICE=799
PLAN_6_MONTHS=6
PLAN_6_PRICE=1499
PLAN_12_MONTHS=12
PLAN_12_PRICE=2799

# Payment Methods
PAYMENT_METHODS=ru_card,foreign_card,crypto

# Security Settings
SECRET_KEY=production-secret-key-change-this
RATE_LIMIT_ENABLED=true
SESSION_COOKIE_SECURE=true

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Scheduler
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=Europe/Moscow

# Logging
LOG_LEVEL=INFO
LOG_FILE=/home/<USER>/app/logs/bot.log
ERROR_LOG_FILE=/home/<USER>/app/logs/error.log

# Backup
BACKUP_ENABLED=true
BACKUP_PATH=/home/<USER>/app/backups
EOF"

# Step 9: Create systemd service file
print_status "Creating systemd service configuration..."
execute_remote "sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null << 'EOF'
[Unit]
Description=Telegram Payment Bot (Production)
After=network.target
Wants=network.target

[Service]
Type=simple
User=telegrambot
Group=telegrambot
WorkingDirectory=/home/<USER>/app
Environment=FLASK_ENV=production
ExecStart=/home/<USER>/app/venv/bin/python app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=telegram-payment-bot

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/app

[Install]
WantedBy=multi-user.target
EOF"

# Step 10: Update nginx configuration
print_status "Updating nginx configuration..."
execute_remote "sudo tee /etc/nginx/sites-available/telegram-payment-bot > /dev/null << 'EOF'
server {
    listen 80;
    server_name _;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection \"1; mode=block\";

    # Main application
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        access_log off;
    }

    # Webhook endpoint
    location /webhook {
        proxy_pass http://127.0.0.1:5000/webhook;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Admin panel
    location /admin {
        proxy_pass http://127.0.0.1:5000/admin;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Static files (if any)
    location /static {
        proxy_pass http://127.0.0.1:5000/static;
        expires 1d;
        add_header Cache-Control \"public, immutable\";
    }
}
EOF"

# Step 11: Enable nginx site
print_status "Enabling nginx site..."
execute_remote "sudo ln -sf /etc/nginx/sites-available/telegram-payment-bot /etc/nginx/sites-enabled/"
execute_remote "sudo nginx -t"
execute_remote "sudo systemctl reload nginx"

# Step 12: Set up log rotation
print_status "Setting up log rotation..."
execute_remote "sudo tee /etc/logrotate.d/telegram-payment-bot > /dev/null << 'EOF'
/home/<USER>/app/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 telegrambot telegrambot
    postrotate
        systemctl reload telegram-payment-bot || true
    endscript
}
EOF"

# Step 13: Create startup script for easier management
print_status "Creating management scripts..."
execute_remote "sudo -u $APP_USER tee $APP_DIR/start.sh > /dev/null << 'EOF'
#!/bin/bash
cd /home/<USER>/app
export FLASK_ENV=production
./venv/bin/python app.py
EOF"

execute_remote "sudo -u $APP_USER chmod +x $APP_DIR/start.sh"

# Step 14: Enable and start services
print_status "Enabling and starting services..."
execute_remote "sudo systemctl daemon-reload"
execute_remote "sudo systemctl enable $SERVICE_NAME"
execute_remote "sudo systemctl start $SERVICE_NAME"

# Step 15: Wait for service to start and check status
print_status "Waiting for service to start..."
sleep 5

print_status "Checking service status..."
if execute_remote "sudo systemctl is-active $SERVICE_NAME --quiet"; then
    print_success "Service is running!"
else
    print_warning "Service may not be running properly. Checking logs..."
    execute_remote "sudo journalctl -u $SERVICE_NAME --no-pager -n 10"
fi

# Step 16: Test endpoints
print_status "Testing application endpoints..."
if execute_remote "curl -s http://localhost/ | grep -q 'html\\|admin\\|redirect'"; then
    print_success "Main page is accessible"
else
    print_warning "Main page test failed"
fi

if execute_remote "curl -s http://localhost/health | grep -q 'status'"; then
    print_success "Health check endpoint is working"
else
    print_warning "Health check endpoint test failed"
fi

# Step 17: Display final status
echo ""
print_success "=== DEPLOYMENT COMPLETED ==="
echo ""
print_status "Service Status:"
execute_remote "sudo systemctl status $SERVICE_NAME --no-pager -l"
echo ""
print_status "Recent Logs:"
execute_remote "sudo journalctl -u $SERVICE_NAME --no-pager -n 5"
echo ""
print_status "Application URLs:"
echo "  Main: http://$SERVER_IP/"
echo "  Admin: http://$SERVER_IP/admin"
echo "  Health: http://$SERVER_IP/health"
echo "  Webhook: http://$SERVER_IP/webhook"
echo ""
print_warning "IMPORTANT: Please update the following in $APP_DIR/.env.production:"
echo "  - TELEGRAM_BOT_TOKEN"
echo "  - LAVA_API_KEY"
echo "  - LAVA_SECRET_KEY"
echo "  - WEBHOOK_URL"
echo "  - CHANNEL_ID"
echo "  - ADMIN_USER_IDS"
echo "  - NOTIFICATION_CHAT_ID"
echo ""
print_status "After updating the configuration, restart the service:"
echo "  ssh ubuntu@$SERVER_IP"
echo "  sudo systemctl restart $SERVICE_NAME"
echo ""
print_status "Useful commands:"
echo "  sudo systemctl status $SERVICE_NAME"
echo "  sudo journalctl -u $SERVICE_NAME -f"
echo "  sudo systemctl restart $SERVICE_NAME"
echo "  curl http://localhost/health"