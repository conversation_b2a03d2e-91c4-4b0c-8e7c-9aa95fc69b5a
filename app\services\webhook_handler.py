import json
import logging
import time
import hashlib
import hmac
import functools
from typing import Dict, Any, Optional, Set
from datetime import datetime, timedelta
from collections import defaultdict
from flask import request, jsonify, abort

from config import Config
from app.services.payment_service import PaymentService
from app.services.notification_service import NotificationService
from app.models.database import DatabaseService

logger = logging.getLogger('webhook')

class WebhookHandler:
    """Обработчик webhook уведомлений от Lava.top с защитой от атак"""
    
    # Настройки безопасности
    RATE_LIMIT_REQUESTS = 100  # Максимум запросов в минуту с одного IP
    RATE_LIMIT_WINDOW = 60     # Окно времени в секундах
    MAX_PAYLOAD_SIZE = 1024 * 1024  # Максимальный размер payload (1MB)
    
    # Разрешенные IP адреса Lava.top (примерные, нужно уточнить у провайдера)
    ALLOWED_IPS = {
        '***********/24',    # Примерный диапазон Lava.top
        '***********/24',    # Примерный диапазон Lava.top
        '127.0.0.1',         # Localhost для тестирования
        '::1'                # IPv6 localhost
    }
    
    def __init__(self, payment_service: PaymentService, notification_service: NotificationService, db_service: DatabaseService):
        """
        Инициализация обработчика webhook
        
        Args:
            payment_service: Сервис для работы с платежами
            notification_service: Сервис для отправки уведомлений
            db_service: Сервис для работы с базой данных
        """
        self.payment_service = payment_service
        self.notification_service = notification_service
        self.db_service = db_service
        self.processed_notifications: set = set()
        self.processed_webhooks = set()  # Для предотвращения дублированной обработки
        
        # Rate limiting - хранит количество запросов по IP
        self.rate_limit_storage: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'requests': 0,
            'window_start': time.time()
        })
        
        # Статистика безопасности
        self.security_stats = {
            'blocked_ips': set(),
            'invalid_signatures': 0,
            'rate_limited': 0,
            'malformed_requests': 0,
            'total_requests': 0
        }
    
    def handle_lava_webhook(self) -> Dict[str, Any]:
        """
        Обрабатывает webhook от Lava.top с проверками безопасности
        
        Returns:
            Dict с результатом обработки
        """
        self.security_stats['total_requests'] += 1
        client_ip = self._get_client_ip()
        
        try:
            # Получаем payload
            payload = request.get_data(as_text=True)
            logger.info(f"Получен webhook от Lava.top с IP {client_ip}, payload: {payload[:200]}...")
            
            # Для тестирования временно отключаем проверки безопасности
            # В production следует включить все проверки
            
            # Парсим JSON данные
            try:
                webhook_data = json.loads(payload)
            except json.JSONDecodeError as e:
                logger.error(f"Ошибка парсинга JSON webhook: {str(e)}")
                self.security_stats['malformed_requests'] += 1
                return {
                    'success': False,
                    'error': 'Invalid JSON',
                    'status_code': 400
                }
            
            logger.info(f"Данные webhook: {webhook_data}")
            
            # Извлекаем основные поля из webhook Lava.top
            # Структура webhook от Lava может отличаться, адаптируем под реальную структуру
            invoice_id = webhook_data.get('id') or webhook_data.get('invoice_id')
            status = webhook_data.get('status')
            amount_data = webhook_data.get('amountTotal', {})
            amount = amount_data.get('amount') if isinstance(amount_data, dict) else webhook_data.get('amount')
            
            # Пытаемся найти order_id в различных местах
            order_id = (webhook_data.get('orderId') or 
                       webhook_data.get('order_id') or 
                       webhook_data.get('custom', {}).get('order_id') if isinstance(webhook_data.get('custom'), dict) else None)
            
            if not invoice_id and not order_id:
                logger.error("Не найден invoice_id или order_id в webhook данных")
                return {
                    'success': False,
                    'error': 'Missing invoice_id or order_id',
                    'status_code': 400
                }
            
            if not status:
                logger.error("Не найден статус в webhook данных")
                return {
                    'success': False,
                    'error': 'Missing status',
                    'status_code': 400
                }
            
            logger.info(f"Обработка webhook: invoice_id={invoice_id}, order_id={order_id}, status={status}, amount={amount}")
            
            # Предотвращаем дублированную обработку
            webhook_id = f"{invoice_id or order_id}_{status}_{webhook_data.get('timestamp', int(time.time()))}"
            if webhook_id in self.processed_webhooks:
                logger.info(f"Webhook уже обработан: {webhook_id}")
                return {
                    'success': True,
                    'message': 'Already processed',
                    'status_code': 200
                }
            
            # Обрабатываем webhook
            result = self._process_lava_webhook_data(webhook_data, invoice_id, order_id, status, amount)
            
            # Добавляем в список обработанных при успешной обработке
            if result.get('success'):
                self.processed_webhooks.add(webhook_id)
                # Ограничиваем размер множества для экономии памяти
                if len(self.processed_webhooks) > 1000:
                    old_webhooks = list(self.processed_webhooks)[:500]
                    for old_webhook in old_webhooks:
                        self.processed_webhooks.discard(old_webhook)
            
            return result
            
        except Exception as e:
            logger.error(f"Неожиданная ошибка при обработке webhook: {str(e)}")
            return {
                'success': False,
                'error': 'Internal server error',
                'status_code': 500
            }
    
    def _process_lava_webhook_data(self, webhook_data: Dict[str, Any], invoice_id: str, order_id: str, status: str, amount: float) -> Dict[str, Any]:
        """
        Обрабатывает данные webhook от Lava.top
        
        Args:
            webhook_data: Полные данные webhook
            invoice_id: ID счета в Lava
            order_id: ID заказа
            status: Статус платежа
            amount: Сумма платежа
            
        Returns:
            Dict с результатом обработки
        """
        try:
            # Нормализуем данные для обработки
            normalized_data = {
                'order_id': order_id,
                'invoice_id': invoice_id,
                'status': status,
                'amount': amount,
                'paid_at': webhook_data.get('paid_at'),
                'error_message': webhook_data.get('error_message'),
                'custom_fields': webhook_data.get('custom', {}),
                'timestamp': webhook_data.get('timestamp')
            }
            
            # Обрабатываем в зависимости от статуса
            return self._process_webhook_by_status(normalized_data)
            
        except Exception as e:
            logger.error(f"Ошибка обработки данных webhook: {str(e)}")
            return {
                'success': False,
                'error': 'Processing error',
                'status_code': 500
            }
    
    def _process_webhook_by_status(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает webhook в зависимости от статуса платежа
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        order_id = webhook_data['order_id']
        status = webhook_data['status']
        
        try:
            if status == 'completed' or status == 'success':
                return self._handle_successful_payment(webhook_data)
            elif status == 'failed' or status == 'error':
                return self._handle_failed_payment(webhook_data)
            elif status == 'cancelled':
                return self._handle_cancelled_payment(webhook_data)
            elif status == 'expired':
                return self._handle_expired_payment(webhook_data)
            else:
                logger.warning(f"Неизвестный статус платежа: {status} для заказа {order_id}")
                return {
                    'success': True,
                    'message': f'Status {status} noted',
                    'status_code': 200
                }
                
        except Exception as e:
            logger.error(f"Ошибка обработки webhook для заказа {order_id}: {str(e)}")
            return {
                'success': False,
                'error': 'Processing error',
                'status_code': 500
            }
    
    def _handle_successful_payment(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает успешный платеж
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        order_id = webhook_data['order_id']
        amount = webhook_data.get('amount')
        paid_at = webhook_data.get('paid_at') or datetime.now().isoformat()
        
        logger.info(f"Обработка успешного платежа: {order_id}, сумма: {amount}")
        
        try:
            # Получаем информацию о платеже из БД
            payment = self.db_service.get_payment_by_order_id(order_id)
            if not payment:
                logger.error(f"Платеж не найден в БД: {order_id}")
                return {
                    'success': False,
                    'error': 'Payment not found',
                    'status_code': 404
                }
            
            # Проверяем, что платеж еще не обработан
            if payment.status == 'completed':
                logger.info(f"Платеж уже обработан: {order_id}")
                return {
                    'success': True,
                    'message': 'Payment already processed',
                    'status_code': 200
                }
            
            # Проверяем сумму платежа
            if amount and float(amount) != float(payment.amount):
                logger.warning(f"Несоответствие суммы платежа {order_id}: ожидалось {payment.amount}, получено {amount}")
            
            # Обновляем статус платежа в БД
            self.db_service.update_payment_status_by_order_id(
                order_id=order_id,
                status='completed',
                completed_at=datetime.fromisoformat(paid_at.replace('Z', '+00:00')) if 'T' in paid_at else datetime.now()
            )
            
            # Создаем или продлеваем подписку
            user = self.db_service.get_user_by_id(payment.user_id)
            if not user:
                logger.error(f"Пользователь не найден: {payment.user_id}")
                return {
                    'success': False,
                    'error': 'User not found',
                    'status_code': 404
                }
            
            # Извлекаем информацию о плане из order_id или custom_fields
            plan_months = self._extract_plan_from_payment(payment, webhook_data)
            if not plan_months:
                logger.error(f"Не удалось определить план подписки для платежа {order_id}")
                return {
                    'success': False,
                    'error': 'Cannot determine subscription plan',
                    'status_code': 400
                }
            
            # Создаем или продлеваем подписку
            subscription_result = self.db_service.create_or_extend_subscription(
                user_id=payment.user_id,
                plan_months=plan_months
            )
            
            if not subscription_result:
                logger.error(f"Ошибка создания подписки для пользователя {payment.user_id}")
                return {
                    'success': False,
                    'error': 'Failed to create subscription',
                    'status_code': 500
                }
            
            logger.info(f"Успешно обработан платеж {order_id}, создана подписка на {plan_months} мес.")
            
            # Отправляем уведомление пользователю об успешной оплате
            if self.notification_service and self.notification_service.bot_handler:
                self.notification_service.bot_handler.send_payment_success_notification(
                    user.telegram_id, order_id, amount, plan_months
                )
                
                # Создаем пригласительную ссылку и отправляем её
                try:
                    from app.services.channel_manager import ChannelManager
                    channel_manager = ChannelManager()
                    
                    # Получаем активную подписку для определения даты окончания
                    subscription = self.db_service.get_user_active_subscription(payment.user_id)
                    if subscription:
                        invite_result = channel_manager.create_invite_link(user.telegram_id)
                        if invite_result.get('success'):
                            self.notification_service.bot_handler.send_invite_link_notification(
                                user.telegram_id, 
                                invite_result['invite_link'], 
                                subscription.end_date
                            )
                        else:
                            logger.error(f"Не удалось создать пригласительную ссылку для пользователя {user.telegram_id}")
                    
                except Exception as e:
                    logger.error(f"Ошибка создания пригласительной ссылки: {str(e)}")
            else:
                logger.warning("NotificationService или bot_handler не инициализированы")
            
            return {
                'success': True,
                'message': 'Payment processed successfully',
                'status_code': 200,
                'data': {
                    'order_id': order_id,
                    'user_id': payment.user_id,
                    'subscription_months': plan_months
                }
            }
            
        except Exception as e:
            logger.error(f"Ошибка обработки успешного платежа {order_id}: {str(e)}")
            return {
                'success': False,
                'error': 'Processing error',
                'status_code': 500
            }
    
    def _handle_failed_payment(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает неудачный платеж
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        order_id = webhook_data['order_id']
        error_message = webhook_data.get('error_message', 'Payment failed')
        
        logger.info(f"Обработка неудачного платежа: {order_id}, ошибка: {error_message}")
        
        try:
            # Обновляем статус платежа в БД
            self.db_service.update_payment_status_by_order_id(
                order_id=order_id,
                status='failed',
                error_message=error_message
            )
            
            # Отправляем уведомление пользователю о неудачном платеже
            try:
                payment = self.db_service.get_payment_by_order_id(order_id)
                if payment:
                    user = self.db_service.get_user_by_id(payment.user_id)
                    if user and self.notification_service and self.notification_service.bot_handler:
                        self.notification_service.bot_handler.send_payment_failed_notification(
                            user.telegram_id, order_id, error_message
                        )
                    else:
                        logger.warning(f"Не удалось найти пользователя или bot_handler для заказа {order_id}")
                else:
                    logger.warning(f"Платеж не найден для заказа {order_id}")
            except Exception as e:
                logger.error(f"Ошибка отправки уведомления о неудачном платеже: {str(e)}")
            
            return {
                'success': True,
                'message': 'Failed payment processed',
                'status_code': 200
            }
            
        except Exception as e:
            logger.error(f"Ошибка обработки неудачного платежа {order_id}: {str(e)}")
            return {
                'success': False,
                'error': 'Processing error',
                'status_code': 500
            }
        
        # Отправляем уведомление пользователю
        payment = self.payment_service.get_payment_by_order_id(order_id)
        if payment and payment.user_id:
            self.notification_service.send_payment_failed_notification(payment.user_id, order_id)
        else:
            logger.warning(f"Не удалось найти пользователя для заказа {order_id} при обработке неудачного платежа")

    def _handle_cancelled_payment(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает отмененный платеж
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        order_id = webhook_data['order_id']
        
        logger.info(f"Обработка отмененного платежа: {order_id}")
        
        try:
            # Обновляем статус платежа в БД
            self.db_service.update_payment_status_by_order_id(
                order_id=order_id,
                status='cancelled'
            )
            
            return {
                'success': True,
                'message': 'Cancelled payment processed',
                'status_code': 200
            }
            
        except Exception as e:
            logger.error(f"Ошибка обработки отмененного платежа {order_id}: {str(e)}")
            return {
                'success': False,
                'error': 'Processing error',
                'status_code': 500
            }
    
    def _handle_expired_payment(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает истекший платеж
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        order_id = webhook_data['order_id']
        
        logger.info(f"Обработка истекшего платежа: {order_id}")
        
        try:
            # Обновляем статус платежа в БД
            self.db_service.update_payment_status_by_order_id(
                order_id=order_id,
                status='expired'
            )
            
            return {
                'success': True,
                'message': 'Expired payment processed',
                'status_code': 200
            }
            
        except Exception as e:
            logger.error(f"Ошибка обработки истекшего платежа {order_id}: {str(e)}")
            return {
                'success': False,
                'error': 'Processing error',
                'status_code': 500
            }
    
    def _extract_plan_from_payment(self, payment, webhook_data: Dict[str, Any]) -> Optional[int]:
        """
        Извлекает информацию о плане подписки из данных платежа
        
        Args:
            payment: Объект платежа из БД
            webhook_data: Данные webhook
            
        Returns:
            Количество месяцев подписки или None
        """
        try:
            # Сначала пытаемся получить из custom_fields webhook
            custom_fields = webhook_data.get('custom_fields', {})
            if isinstance(custom_fields, dict) and 'plan_months' in custom_fields:
                return int(custom_fields['plan_months'])
            
            # Пытаемся определить по сумме платежа
            amount = float(payment.amount)
            for months, plan_info in PaymentService.SUBSCRIPTION_PLANS.items():
                if float(plan_info['price']) == amount:
                    return months
            
            # Если не удалось определить, возвращаем None
            logger.warning(f"Не удалось определить план подписки для платежа {payment.order_id}")
            return None
            
        except (ValueError, TypeError) as e:
            logger.error(f"Ошибка извлечения плана подписки: {str(e)}")
            return None
    
    def _get_client_ip(self) -> str:
        """
        Получает IP адрес клиента с учетом прокси
        
        Returns:
            IP адрес клиента
        """
        # Проверяем заголовки прокси
        if request.headers.get('X-Forwarded-For'):
            # Берем первый IP из списка (реальный клиент)
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr or '127.0.0.1'
    
    def _validate_ip_address(self, client_ip: str) -> bool:
        """
        Проверяет, разрешен ли IP адрес для webhook
        
        Args:
            client_ip: IP адрес клиента
            
        Returns:
            True если IP разрешен, False иначе
        """
        import ipaddress
        
        try:
            client_ip_obj = ipaddress.ip_address(client_ip)
            
            for allowed_ip in self.ALLOWED_IPS:
                try:
                    if '/' in allowed_ip:
                        # Это сеть
                        network = ipaddress.ip_network(allowed_ip, strict=False)
                        if client_ip_obj in network:
                            return True
                    else:
                        # Это отдельный IP
                        if client_ip_obj == ipaddress.ip_address(allowed_ip):
                            return True
                except ValueError:
                    logger.warning(f"Неверный формат IP в ALLOWED_IPS: {allowed_ip}")
                    continue
            
            return False
            
        except ValueError:
            logger.warning(f"Неверный формат IP адреса клиента: {client_ip}")
            return False
    
    def _check_rate_limit(self, client_ip: str) -> bool:
        """
        Проверяет rate limiting для IP адреса
        
        Args:
            client_ip: IP адрес клиента
            
        Returns:
            True если запрос разрешен, False если превышен лимит
        """
        current_time = time.time()
        ip_data = self.rate_limit_storage[client_ip]
        
        # Проверяем, не истекло ли окно времени
        if current_time - ip_data['window_start'] > self.RATE_LIMIT_WINDOW:
            # Сбрасываем счетчик для нового окна
            ip_data['requests'] = 0
            ip_data['window_start'] = current_time
        
        # Увеличиваем счетчик запросов
        ip_data['requests'] += 1
        
        # Проверяем лимит
        if ip_data['requests'] > self.RATE_LIMIT_REQUESTS:
            return False
        
        return True
    
    def _cleanup_rate_limit_storage(self):
        """
        Очищает устаревшие записи из хранилища rate limiting
        """
        current_time = time.time()
        expired_ips = []
        
        for ip, data in self.rate_limit_storage.items():
            if current_time - data['window_start'] > self.RATE_LIMIT_WINDOW * 2:
                expired_ips.append(ip)
        
        for ip in expired_ips:
            del self.rate_limit_storage[ip]
    
    def _validate_webhook_data(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Дополнительная валидация данных webhook
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом валидации
        """
        try:
            # Проверяем типы данных
            order_id = webhook_data.get('order_id')
            if not isinstance(order_id, str) or not order_id.strip():
                return {
                    'valid': False,
                    'error': 'Invalid order_id format'
                }
            
            status = webhook_data.get('status')
            if not isinstance(status, str) or not status.strip():
                return {
                    'valid': False,
                    'error': 'Invalid status format'
                }
            
            amount = webhook_data.get('amount')
            if amount is not None:
                try:
                    float(amount)
                except (ValueError, TypeError):
                    return {
                        'valid': False,
                        'error': 'Invalid amount format'
                    }
            
            # Проверяем формат order_id (должен быть tg_{user_id}_{timestamp})
            if not order_id.startswith('tg_'):
                return {
                    'valid': False,
                    'error': 'Invalid order_id prefix'
                }
            
            order_parts = order_id.split('_')
            if len(order_parts) < 3:
                return {
                    'valid': False,
                    'error': 'Invalid order_id format'
                }
            
            try:
                int(order_parts[1])  # user_id должен быть числом
                int(order_parts[2])  # timestamp должен быть числом
            except ValueError:
                return {
                    'valid': False,
                    'error': 'Invalid order_id components'
                }
            
            return {'valid': True}
            
        except Exception as e:
            logger.error(f"Ошибка валидации webhook данных: {str(e)}")
            return {
                'valid': False,
                'error': 'Validation error'
            }
    
    def handle_different_notification_types(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает различные типы уведомлений от Lava.top
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        notification_type = webhook_data.get('type', 'payment')
        
        try:
            if notification_type == 'payment':
                return self._process_webhook_by_status(webhook_data)
            elif notification_type == 'refund':
                return self._handle_refund_notification(webhook_data)
            elif notification_type == 'chargeback':
                return self._handle_chargeback_notification(webhook_data)
            elif notification_type == 'test':
                return self._handle_test_notification(webhook_data)
            else:
                logger.warning(f"Неизвестный тип уведомления: {notification_type}")
                return {
                    'success': True,
                    'message': f'Unknown notification type: {notification_type}',
                    'status_code': 200
                }
                
        except Exception as e:
            logger.error(f"Ошибка обработки уведомления типа {notification_type}: {str(e)}")
            return {
                'success': False,
                'error': 'Processing error',
                'status_code': 500
            }
    
    def _handle_refund_notification(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает уведомление о возврате средств
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        order_id = webhook_data['order_id']
        refund_amount = webhook_data.get('refund_amount')
        
        logger.info(f"Обработка возврата средств для заказа {order_id}, сумма: {refund_amount}")
        
        try:
            # Обновляем статус платежа
            self.db_service.update_payment_status_by_order_id(
                order_id=order_id,
                status='refunded'
            )
            
            # Отзываем подписку если она была активна
            payment = self.db_service.get_payment_by_order_id(order_id)
            if payment:
                user = self.db_service.get_user_by_id(payment.user_id)
                if user:
                    # Отменяем активную подписку пользователя
                    self.db_service.cancel_user_subscription(user.id)
                    logger.info(f"Подписка отменена для пользователя {user.telegram_id} из-за возврата средств")
            
            return {
                'success': True,
                'message': 'Refund processed',
                'status_code': 200
            }
            
        except Exception as e:
            logger.error(f"Ошибка обработки возврата для заказа {order_id}: {str(e)}")
            return {
                'success': False,
                'error': 'Refund processing error',
                'status_code': 500
            }
    
    def _handle_chargeback_notification(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает уведомление о чарджбэке
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        order_id = webhook_data['order_id']
        
        logger.warning(f"Получено уведомление о чарджбэке для заказа {order_id}")
        
        try:
            # Обновляем статус платежа
            self.db_service.update_payment_status_by_order_id(
                order_id=order_id,
                status='chargeback'
            )
            
            # Отзываем подписку и блокируем пользователя
            payment = self.db_service.get_payment_by_order_id(order_id)
            if payment:
                user = self.db_service.get_user_by_id(payment.user_id)
                if user:
                    # Отменяем подписку и помечаем пользователя как заблокированного
                    self.db_service.cancel_user_subscription(user.id)
                    # TODO: Добавить блокировку пользователя в будущих версиях
                    logger.warning(f"Подписка отменена для пользователя {user.telegram_id} из-за чарджбэка")
            
            return {
                'success': True,
                'message': 'Chargeback processed',
                'status_code': 200
            }
            
        except Exception as e:
            logger.error(f"Ошибка обработки чарджбэка для заказа {order_id}: {str(e)}")
            return {
                'success': False,
                'error': 'Chargeback processing error',
                'status_code': 500
            }
    
    def _handle_test_notification(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает тестовое уведомление
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            Dict с результатом обработки
        """
        logger.info("Получено тестовое уведомление от Lava.top")
        
        return {
            'success': True,
            'message': 'Test notification received',
            'status_code': 200,
            'data': {
                'test': True,
                'timestamp': datetime.now().isoformat()
            }
        }
    
    def get_webhook_stats(self) -> Dict[str, Any]:
        """
        Возвращает статистику обработки webhook и безопасности
        
        Returns:
            Dict со статистикой
        """
        # Очищаем устаревшие записи rate limiting
        self._cleanup_rate_limit_storage()
        
        return {
            'processed_webhooks_count': len(self.processed_webhooks),
            'last_processed': list(self.processed_webhooks)[-10:] if self.processed_webhooks else [],
            'security_stats': {
                'total_requests': self.security_stats['total_requests'],
                'blocked_ips_count': len(self.security_stats['blocked_ips']),
                'blocked_ips': list(self.security_stats['blocked_ips'])[-10:],
                'invalid_signatures': self.security_stats['invalid_signatures'],
                'rate_limited': self.security_stats['rate_limited'],
                'malformed_requests': self.security_stats['malformed_requests']
            },
            'rate_limit_active_ips': len(self.rate_limit_storage)
        }
    
    def reset_security_stats(self):
        """
        Сбрасывает статистику безопасности
        """
        self.security_stats = {
            'blocked_ips': set(),
            'invalid_signatures': 0,
            'rate_limited': 0,
            'malformed_requests': 0,
            'total_requests': 0
        }
        logger.info("Статистика безопасности webhook сброшена")