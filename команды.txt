посмотри как в локальной версии сделано и закачай это на сервер


plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"


plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python webapp.py > /var/log/telegram-payment-bot/webapp.log
 2>&1 &'"


телег бот
plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"


веб

plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python webapp.py > /var/log/telegram-payment-bot/webapp.log 2>&1 &'"