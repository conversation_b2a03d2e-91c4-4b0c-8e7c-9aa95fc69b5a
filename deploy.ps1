# PowerShell скрипт для обновления админ-панели на сервере
# Использование: .\deploy.ps1

param(
    [string]$Server = "**************",
    [string]$User = "ubuntu",
    [string]$ProjectDir = "/home/<USER>/telegram_bot",
    [string]$ServiceName = "telegram-bot"
)

Write-Host "Начинаем обновление админ-панели на сервере..." -ForegroundColor Green

# Функция для выполнения SSH команд
function Invoke-SSH {
    param([string]$Command)
    
    $sshArgs = @(
        "-o", "StrictHostKeyChecking=no",
        "$User@$Server",
        $Command
    )
    
    & ssh $sshArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Ошибка выполнения команды: $Command" -ForegroundColor Red
        throw "SSH команда завершилась с ошибкой"
    }
}

# Функция для загрузки файлов
function Copy-ToServer {
    param([string]$LocalPath, [string]$RemotePath)
    
    $scpArgs = @(
        "-o", "StrictHostKeyChecking=no",
        $LocalPath,
        "$User@${Server}:$RemotePath"
    )
    
    & scp $scpArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Ошибка загрузки файла: $LocalPath" -ForegroundColor Red
        throw "SCP команда завершилась с ошибкой"
    }
}

try {
    Write-Host "📦 Создание архива с обновлениями..." -ForegroundColor Yellow
    
    # Удаляем старый архив если есть
    if (Test-Path "admin_panel_fix.zip") {
        Remove-Item "admin_panel_fix.zip" -Force
    }
    
    # Создаем новый архив
    Compress-Archive -Path "app/admin.py", "templates/" -DestinationPath "admin_panel_fix.zip" -Force
    Write-Host "✅ Архив создан" -ForegroundColor Green

    Write-Host "📤 Загрузка архива на сервер..." -ForegroundColor Yellow
    Copy-ToServer "admin_panel_fix.zip" "/home/<USER>/"
    Write-Host "✅ Архив загружен" -ForegroundColor Green

    Write-Host "🛑 Остановка сервиса..." -ForegroundColor Yellow
    Invoke-SSH "sudo systemctl stop $ServiceName"
    Write-Host "✅ Сервис остановлен" -ForegroundColor Green

    Write-Host "Создание резервной копии..." -ForegroundColor Yellow
    $backupCmd = "cd $ProjectDir; sudo cp -r app templates backup_`$(date +%Y%m%d_%H%M%S)"
    Invoke-SSH $backupCmd
    Write-Host "Резервная копия создана" -ForegroundColor Green

    Write-Host "Распаковка обновлений..." -ForegroundColor Yellow
    Invoke-SSH "cd $ProjectDir; unzip -o /home/<USER>/admin_panel_fix.zip"
    Write-Host "Обновления распакованы" -ForegroundColor Green

    Write-Host "Установка прав доступа..." -ForegroundColor Yellow
    Invoke-SSH "cd $ProjectDir; sudo chown -R ubuntu:ubuntu app templates; sudo chmod -R 755 app templates"
    Write-Host "Права доступа установлены" -ForegroundColor Green

    Write-Host "Проверка обновлений..." -ForegroundColor Yellow
    try {
        Invoke-SSH "cd $ProjectDir; grep -q 'safe_url_for' app/admin.py"
        Write-Host "admin.py обновлен" -ForegroundColor Green
    } catch {
        Write-Host "admin.py не обновлен" -ForegroundColor Red
    }
    
    try {
        Invoke-SSH "cd $ProjectDir; grep -q 'safe_url_for' templates/base.html"
        Write-Host "base.html обновлен" -ForegroundColor Green
    } catch {
        Write-Host "base.html не обновлен" -ForegroundColor Red
    }

    Write-Host "🚀 Запуск сервиса..." -ForegroundColor Yellow
    Invoke-SSH "sudo systemctl start $ServiceName"
    Write-Host "✅ Сервис запущен" -ForegroundColor Green

    Write-Host "⏳ Ожидание запуска сервиса..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5

    Write-Host "📊 Проверка статуса сервиса..." -ForegroundColor Yellow
    Invoke-SSH "sudo systemctl status $ServiceName --no-pager -l"

    Write-Host "📝 Последние логи сервиса:" -ForegroundColor Yellow
    Invoke-SSH "sudo journalctl -u $ServiceName -n 10 --no-pager"

    Write-Host ""
    Write-Host "🎉 Обновление завершено!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Следующие шаги:" -ForegroundColor Cyan
    Write-Host "1. Откройте https://$Server/admin/login" -ForegroundColor White
    Write-Host "2. Введите пароль: admin123" -ForegroundColor White
    Write-Host "3. Проверьте работу админ-панели" -ForegroundColor White
    Write-Host ""
    Write-Host "🔍 Для просмотра логов в реальном времени:" -ForegroundColor Cyan
    Write-Host "ssh $User@$Server 'sudo journalctl -u $ServiceName -f'" -ForegroundColor White
    Write-Host ""
    Write-Host "🆘 При проблемах:" -ForegroundColor Cyan
    Write-Host "ssh $User@$Server 'sudo systemctl restart $ServiceName'" -ForegroundColor White

} catch {
    Write-Host ""
    Write-Host "💥 Ошибка при обновлении: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Попробуйте:" -ForegroundColor Yellow
    Write-Host "1. Проверьте подключение к серверу: ssh $User@$Server" -ForegroundColor White
    Write-Host "2. Убедитесь, что файлы существуют локально" -ForegroundColor White
    Write-Host "3. Проверьте права доступа к файлам" -ForegroundColor White
    
    exit 1
}