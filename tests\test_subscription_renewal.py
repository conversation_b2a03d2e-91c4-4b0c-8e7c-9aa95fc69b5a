"""
Тесты для продления подписок
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal

from app.models.database import DatabaseService, DatabaseManager
from app.models.models import User, Subscription, Payment
from app.services.webhook_handler import WebhookHandler
from app.services.payment_service import PaymentService

class TestSubscriptionRenewal(unittest.TestCase):
    """Тесты для функциональности продления подписок"""
    
    def setUp(self):
        """Настройка тестов"""
        self.db_manager = Mock(spec=DatabaseManager)
        self.db_service = DatabaseService(self.db_manager)
        self.payment_service = Mock(spec=PaymentService)
        self.webhook_handler = WebhookHandler(self.payment_service, self.db_service)
    
    def test_extend_subscription_success(self):
        """Тест успешного продления подписки"""
        # Настройка мока
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.rowcount = 1
        self.db_manager.get_connection.return_value.__enter__ = Mock(return_value=mock_conn)
        self.db_manager.get_connection.return_value.__exit__ = Mock(return_value=None)
        
        # Выполнение теста
        result = self.db_service.extend_subscription(subscription_id=1, additional_months=3)
        
        # Проверки
        self.assertTrue(result)
        mock_cursor.execute.assert_called_once()
        mock_conn.commit.assert_called_once()
        
        # Проверяем SQL запрос
        sql_call = mock_cursor.execute.call_args[0][0]
        self.assertIn("UPDATE subscriptions", sql_call)
        self.assertIn("datetime(end_date, '+3 months')", sql_call)
    
    def test_extend_subscription_not_found(self):
        """Тест продления несуществующей подписки"""
        # Настройка мока
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.rowcount = 0  # Подписка не найдена
        self.db_manager.get_connection.return_value.__enter__ = Mock(return_value=mock_conn)
        self.db_manager.get_connection.return_value.__exit__ = Mock(return_value=None)
        
        # Выполнение теста
        result = self.db_service.extend_subscription(subscription_id=999, additional_months=1)
        
        # Проверки
        self.assertFalse(result)
    
    def test_create_or_extend_subscription_with_active_subscription(self):
        """Тест создания/продления подписки при наличии активной подписки"""
        # Настройка мока для активной подписки
        active_subscription = Mock()
        active_subscription.id = 1
        active_subscription.is_active.return_value = True
        
        with patch.object(self.db_service, 'get_user_active_subscription', return_value=active_subscription):
            with patch.object(self.db_service, 'extend_subscription', return_value=True) as mock_extend:
                # Выполнение теста
                result = self.db_service.create_or_extend_subscription(user_id=123, plan_months=3)
                
                # Проверки
                self.assertTrue(result)
                mock_extend.assert_called_once_with(1, 3)
    
    def test_create_or_extend_subscription_without_active_subscription(self):
        """Тест создания/продления подписки без активной подписки"""
        # Настройка мока - нет активной подписки
        with patch.object(self.db_service, 'get_user_active_subscription', return_value=None):
            with patch.object(self.db_service, 'create_subscription') as mock_create:
                mock_subscription = Mock()
                mock_create.return_value = mock_subscription
                
                # Выполнение теста
                result = self.db_service.create_or_extend_subscription(user_id=123, plan_months=1)
                
                # Проверки
                self.assertTrue(result)
                mock_create.assert_called_once_with(123, 'monthly', 1)
    
    def test_create_or_extend_subscription_quarterly_plan(self):
        """Тест создания подписки с квартальным планом"""
        # Настройка мока - нет активной подписки
        with patch.object(self.db_service, 'get_user_active_subscription', return_value=None):
            with patch.object(self.db_service, 'create_subscription') as mock_create:
                mock_subscription = Mock()
                mock_create.return_value = mock_subscription
                
                # Выполнение теста для 3-месячного плана
                result = self.db_service.create_or_extend_subscription(user_id=123, plan_months=3)
                
                # Проверки
                self.assertTrue(result)
                mock_create.assert_called_once_with(123, 'quarterly', 3)
    
    def test_create_or_extend_subscription_yearly_plan(self):
        """Тест создания подписки с годовым планом"""
        # Настройка мока - нет активной подписки
        with patch.object(self.db_service, 'get_user_active_subscription', return_value=None):
            with patch.object(self.db_service, 'create_subscription') as mock_create:
                mock_subscription = Mock()
                mock_create.return_value = mock_subscription
                
                # Выполнение теста для 12-месячного плана
                result = self.db_service.create_or_extend_subscription(user_id=123, plan_months=12)
                
                # Проверки
                self.assertTrue(result)
                mock_create.assert_called_once_with(123, 'yearly', 12)
    
    def test_create_or_extend_subscription_error_handling(self):
        """Тест обработки ошибок при создании/продлении подписки"""
        # Настройка мока для ошибки
        with patch.object(self.db_service, 'get_user_active_subscription', side_effect=Exception("Database error")):
            # Выполнение теста
            result = self.db_service.create_or_extend_subscription(user_id=123, plan_months=1)
            
            # Проверки
            self.assertFalse(result)
    
    def test_webhook_successful_payment_with_existing_subscription(self):
        """Тест обработки успешного платежа с существующей подпиской"""
        # Настройка данных
        webhook_data = {
            'order_id': 'test_order_123',
            'status': 'completed',
            'amount': '1000.00',
            'paid_at': '2024-01-15T10:00:00Z'
        }
        
        # Мокаем платеж
        mock_payment = Mock()
        mock_payment.user_id = 123
        mock_payment.amount = Decimal('1000.00')
        mock_payment.status = 'pending'
        
        # Мокаем пользователя
        mock_user = Mock()
        mock_user.id = 123
        
        with patch.object(self.db_service, 'get_payment_by_order_id', return_value=mock_payment):
            with patch.object(self.db_service, 'get_user_by_id', return_value=mock_user):
                with patch.object(self.db_service, 'update_payment_status_by_order_id') as mock_update_payment:
                    with patch.object(self.db_service, 'create_or_extend_subscription', return_value=True) as mock_create_extend:
                        with patch.object(self.webhook_handler, '_extract_plan_from_payment', return_value=3):
                            # Выполнение теста
                            result = self.webhook_handler._handle_successful_payment(webhook_data)
                            
                            # Проверки
                            self.assertTrue(result['success'])
                            self.assertEqual(result['status_code'], 200)
                            mock_update_payment.assert_called_once()
                            mock_create_extend.assert_called_once_with(user_id=123, plan_months=3)
    
    def test_webhook_successful_payment_without_user(self):
        """Тест обработки успешного платежа без пользователя"""
        # Настройка данных
        webhook_data = {
            'order_id': 'test_order_123',
            'status': 'completed',
            'amount': '1000.00'
        }
        
        # Мокаем платеж
        mock_payment = Mock()
        mock_payment.user_id = 123
        mock_payment.amount = Decimal('1000.00')
        mock_payment.status = 'pending'
        
        # Настройка мока для базы данных
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.rowcount = 1  # Успешное обновление
        mock_conn.cursor.return_value = mock_cursor
        self.db_manager.get_connection.return_value.__enter__ = Mock(return_value=mock_conn)
        self.db_manager.get_connection.return_value.__exit__ = Mock(return_value=None)
        
        with patch.object(self.db_service, 'get_payment_by_order_id', return_value=mock_payment):
            with patch.object(self.db_service, 'get_user_by_id', return_value=None):  # Пользователь не найден
                # Выполнение теста
                result = self.webhook_handler._handle_successful_payment(webhook_data)
                
                # Проверки
                self.assertFalse(result['success'])
                self.assertEqual(result['status_code'], 404)
                self.assertEqual(result['error'], 'User not found')
    
    def test_webhook_successful_payment_already_processed(self):
        """Тест обработки уже обработанного платежа"""
        # Настройка данных
        webhook_data = {
            'order_id': 'test_order_123',
            'status': 'completed',
            'amount': '1000.00'
        }
        
        # Мокаем уже обработанный платеж
        mock_payment = Mock()
        mock_payment.user_id = 123
        mock_payment.amount = Decimal('1000.00')
        mock_payment.status = 'completed'  # Уже обработан
        
        with patch.object(self.db_service, 'get_payment_by_order_id', return_value=mock_payment):
            # Выполнение теста
            result = self.webhook_handler._handle_successful_payment(webhook_data)
            
            # Проверки
            self.assertTrue(result['success'])
            self.assertEqual(result['status_code'], 200)
            self.assertEqual(result['message'], 'Payment already processed')
    
    def test_webhook_successful_payment_amount_mismatch(self):
        """Тест обработки платежа с несоответствием суммы"""
        # Настройка данных
        webhook_data = {
            'order_id': 'test_order_123',
            'status': 'completed',
            'amount': '2000.00',  # Другая сумма
            'paid_at': '2024-01-15T10:00:00Z'
        }
        
        # Мокаем платеж
        mock_payment = Mock()
        mock_payment.user_id = 123
        mock_payment.amount = Decimal('1000.00')  # Ожидаемая сумма
        mock_payment.status = 'pending'
        
        # Мокаем пользователя
        mock_user = Mock()
        mock_user.id = 123
        
        with patch.object(self.db_service, 'get_payment_by_order_id', return_value=mock_payment):
            with patch.object(self.db_service, 'get_user_by_id', return_value=mock_user):
                with patch.object(self.db_service, 'update_payment_status_by_order_id') as mock_update_payment:
                    with patch.object(self.db_service, 'create_or_extend_subscription', return_value=True) as mock_create_extend:
                        with patch.object(self.webhook_handler, '_extract_plan_from_payment', return_value=1):
                            # Выполнение теста
                            result = self.webhook_handler._handle_successful_payment(webhook_data)
                            
                            # Проверки - платеж должен быть обработан несмотря на несоответствие суммы
                            self.assertTrue(result['success'])
                            self.assertEqual(result['status_code'], 200)
                            mock_update_payment.assert_called_once()
                            mock_create_extend.assert_called_once()
    
    def test_subscription_renewal_integration(self):
        """Интеграционный тест полного цикла продления подписки"""
        # Создаем реальные объекты для интеграционного теста
        db_manager = Mock(spec=DatabaseManager)
        db_service = DatabaseService(db_manager)
        
        # Настройка мока для активной подписки
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        db_manager.get_connection.return_value.__enter__ = Mock(return_value=mock_conn)
        db_manager.get_connection.return_value.__exit__ = Mock(return_value=None)
        
        # Мокаем активную подписку
        active_subscription = Mock()
        active_subscription.id = 1
        active_subscription.user_id = 123
        active_subscription.plan_type = 'monthly'
        active_subscription.status = 'active'
        active_subscription.start_date = datetime.now() - timedelta(days=15)
        active_subscription.end_date = datetime.now() + timedelta(days=15)
        active_subscription.is_active.return_value = True
        
        with patch.object(db_service, 'get_user_active_subscription', return_value=active_subscription):
            # Настройка мока для успешного продления
            mock_cursor.rowcount = 1
            
            # Выполнение теста
            result = db_service.create_or_extend_subscription(user_id=123, plan_months=3)
            
            # Проверки
            self.assertTrue(result)
            
            # Проверяем, что был вызван SQL для продления
            mock_cursor.execute.assert_called()
            sql_call = mock_cursor.execute.call_args[0][0]
            self.assertIn("UPDATE subscriptions", sql_call)
            self.assertIn("datetime(end_date, '+3 months')", sql_call)

if __name__ == '__main__':
    unittest.main()