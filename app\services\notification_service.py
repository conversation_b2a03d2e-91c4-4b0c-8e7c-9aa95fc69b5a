"""
Notification Service для управления уведомлениями пользователей
"""

import logging
import functools
from typing import List, Dict, Optional
from datetime import datetime, timedelta

from app.services.bot_handler import TelegramBotHandler
from app.models.database import DatabaseService

logger = logging.getLogger('notification')

class NotificationService:
    """Сервис для управления уведомлениями пользователей"""
    
    def __init__(self, bot_handler: TelegramBotHandler, db_service: DatabaseService):
        """
        Инициализация сервиса уведомлений
        
        Args:
            bot_handler: Обработчик Telegram бота
            db_service: Сервис для работы с базой данных
        """
        self.bot_handler = bot_handler
        self.db_service = db_service
        logger.info("NotificationService инициализирован")
    
    def send_payment_link_notification(self, user_id: int, invoice_data: Dict, plan_months: int) -> bool:
        """
        Отправляет уведомление со ссылкой для оплаты
        
        Args:
            user_id: ID пользователя Telegram
            invoice_data: Данные созданного счета
            plan_months: Количество месяцев подписки
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            success = self.bot_handler.send_payment_link(user_id, invoice_data, plan_months)
            
            if success:
                logger.info(f"Уведомление со ссылкой для оплаты отправлено пользователю {user_id}")
            else:
                logger.error(f"Не удалось отправить ссылку для оплаты пользователю {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Ошибка отправки ссылки для оплаты пользователю {user_id}: {str(e)}")
            return False
    
    def send_invite_link_notification(self, user_id: int, invite_url: str, subscription_end_date: datetime) -> bool:
        """
        Отправляет пригласительную ссылку после успешной оплаты
        
        Args:
            user_id: ID пользователя Telegram
            invite_url: Пригласительная ссылка на канал
            subscription_end_date: Дата окончания подписки
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            success = self.bot_handler.send_invite_link(user_id, invite_url, subscription_end_date)
            
            if success:
                logger.info(f"Пригласительная ссылка отправлена пользователю {user_id}")
            else:
                logger.error(f"Не удалось отправить пригласительную ссылку пользователю {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Ошибка отправки пригласительной ссылки пользователю {user_id}: {str(e)}")
            return False
    
    def send_payment_confirmation(self, user_id: int, payment_amount: float, payment_method: str) -> bool:
        """
        Отправляет подтверждение об успешной оплате
        
        Args:
            user_id: ID пользователя Telegram
            payment_amount: Сумма платежа
            payment_method: Способ оплаты
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            success = self.bot_handler.send_payment_confirmation(user_id, payment_amount, payment_method)
            
            if success:
                logger.info(f"Подтверждение оплаты отправлено пользователю {user_id}")
            else:
                logger.error(f"Не удалось отправить подтверждение оплаты пользователю {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Ошибка отправки подтверждения оплаты пользователю {user_id}: {str(e)}")
            return False
    
    def send_expiry_notifications(self) -> Dict[str, int]:
        """
        Отправляет уведомления об истечении подписок
        
        Returns:
            Dict со статистикой отправленных уведомлений
        """
        try:
            stats = {
                'expiring_today': 0,
                'expiring_tomorrow': 0,
                'expiring_in_3_days': 0,
                'expired': 0,
                'errors': 0
            }
            
            # Получаем подписки, которые истекают
            expiring_subscriptions = self.db_service.get_expiring_subscriptions()
            
            for subscription in expiring_subscriptions:
                try:
                    user = self.db_service.get_user_by_id(subscription.user_id)
                    if not user:
                        continue
                    
                    days_left = subscription.days_until_expiry()
                    
                    if days_left <= 0:
                        # Подписка истекла
                        success = self.bot_handler.send_subscription_expired_notification(user.telegram_id)
                        if success:
                            stats['expired'] += 1
                    elif days_left == 1:
                        # Истекает завтра
                        success = self.bot_handler.send_subscription_expiry_warning(user.telegram_id, days_left)
                        if success:
                            stats['expiring_tomorrow'] += 1
                    elif days_left <= 3:
                        # Истекает в течение 3 дней
                        success = self.bot_handler.send_subscription_expiry_warning(user.telegram_id, days_left)
                        if success:
                            stats['expiring_in_3_days'] += 1
                    
                except Exception as e:
                    logger.error(f"Ошибка отправки уведомления пользователю {subscription.user_id}: {str(e)}")
                    stats['errors'] += 1
            
            total_sent = sum(stats.values()) - stats['errors']
            logger.info(f"Отправлено {total_sent} уведомлений об истечении подписок")
            
            return stats
            
        except Exception as e:
            logger.error(f"Ошибка отправки уведомлений об истечении подписок: {str(e)}")
            return {'errors': 1}
    
    def send_payment_failed_notification(self, user_id: int, order_id: str) -> bool:
        """
        Отправляет уведомление о неудачном платеже
        
        Args:
            user_id: ID пользователя Telegram
            order_id: ID заказа
            
        Returns:
            True если уведомление отправлено успешно
        """
        try:
            if self.bot_handler:
                return self.bot_handler.send_payment_failed_notification(user_id, order_id)
            else:
                logger.error("bot_handler не инициализирован")
                return False
                
        except Exception as e:
            logger.error(f"Ошибка отправки уведомления о неудачном платеже пользователю {user_id}: {str(e)}")
            return False
    
    def send_payment_failed_notification(self, user_id: int, order_id: str):
        """
        Отправляет уведомление о неудачной оплате.

        Args:
            user_id: ID пользователя Telegram
            order_id: ID заказа
        """
        try:
            self.bot_handler.send_payment_failed_message(user_id, order_id)
            logger.info(f"Уведомление о неудачном платеже для заказа {order_id} поставлено в очередь на отправку пользователю {user_id}")
        except Exception as e:
            logger.error(f"Ошибка при постановке в очередь уведомления о неудачном платеже для пользователя {user_id}: {e}")

    def send_expiration_warnings(self, days_left: int):
        """
        Отправляет предупреждения об истечении подписок
        
        Returns:
            Словарь с количеством отправленных уведомлений по типам
        """
        try:
            results = {
                'warnings_7_days': 0,
                'warnings_1_day': 0,
                'errors': 0
            }
            
            # Получаем подписки, которые истекают через 7 дней
            subscriptions_7_days = self.db_service.get_subscriptions_expiring_in_days(7)
            for subscription in subscriptions_7_days:
                user = self.db_service.get_user_by_id(subscription.user_id)
                if user:
                    success = self.bot_handler.send_expiration_warning(
                        user.telegram_id, 7, subscription.end_date
                    )
                    if success:
                        results['warnings_7_days'] += 1
                    else:
                        results['errors'] += 1
            
            # Получаем подписки, которые истекают через 1 день
            subscriptions_1_day = self.db_service.get_subscriptions_expiring_in_days(1)
            for subscription in subscriptions_1_day:
                user = self.db_service.get_user_by_id(subscription.user_id)
                if user:
                    success = self.bot_handler.send_expiration_warning(
                        user.telegram_id, 1, subscription.end_date
                    )
                    if success:
                        results['warnings_1_day'] += 1
                    else:
                        results['errors'] += 1
            
            logger.info(f"Отправлено предупреждений об истечении: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка отправки предупреждений об истечении: {str(e)}")
            return {'warnings_7_days': 0, 'warnings_1_day': 0, 'errors': 1}
    
    def send_subscription_expired_notifications(self) -> Dict[str, int]:
        """
        Отправляет уведомления об истечении подписок
        
        Returns:
            Словарь с количеством отправленных уведомлений
        """
        try:
            results = {
                'expired_notifications': 0,
                'errors': 0
            }
            
            # Получаем истекшие подписки
            expired_subscriptions = self.db_service.get_expired_subscriptions()
            
            for subscription in expired_subscriptions:
                user = self.db_service.get_user_by_id(subscription.user_id)
                if user:
                    success = self.bot_handler.send_subscription_expired_notification(user.telegram_id)
                    if success:
                        results['expired_notifications'] += 1
                    else:
                        results['errors'] += 1
            
            logger.info(f"Отправлено уведомлений об истечении подписок: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Ошибка отправки уведомлений об истечении подписок: {str(e)}")
            return {'expired_notifications': 0, 'errors': 1}
    
    def send_bulk_notification(self, user_ids: List[int], message_text: str, 
                              notification_type: str = 'general') -> Dict[str, int]:
        """
        Отправляет массовое уведомление списку пользователей
        
        Args:
            user_ids: Список ID пользователей Telegram
            message_text: Текст сообщения
            notification_type: Тип уведомления для логирования
            
        Returns:
            Словарь с результатами отправки
        """
        try:
            results = self.bot_handler.send_bulk_notification(user_ids, message_text)
            
            successful_count = sum(1 for success in results.values() if success)
            failed_count = len(user_ids) - successful_count
            
            logger.info(f"Массовое уведомление ({notification_type}): "
                       f"успешно {successful_count}, ошибок {failed_count}")
            
            return {
                'successful': successful_count,
                'failed': failed_count,
                'total': len(user_ids)
            }
            
        except Exception as e:
            logger.error(f"Ошибка массовой отправки уведомлений ({notification_type}): {str(e)}")
            return {'successful': 0, 'failed': len(user_ids), 'total': len(user_ids)}
    
    def send_admin_notification(self, admin_user_ids: List[int], message_text: str) -> bool:
        """
        Отправляет уведомление администраторам
        
        Args:
            admin_user_ids: Список ID администраторов
            message_text: Текст сообщения
            
        Returns:
            True если хотя бы одному администратору отправлено
        """
        try:
            results = self.send_bulk_notification(admin_user_ids, message_text, 'admin')
            return results['successful'] > 0
            
        except Exception as e:
            logger.error(f"Ошибка отправки уведомления администраторам: {str(e)}")
            return False
    
    def schedule_notification_check(self) -> Dict[str, int]:
        """
        Выполняет плановую проверку и отправку уведомлений
        
        Returns:
            Сводка по отправленным уведомлениям
        """
        try:
            logger.info("Начинаем плановую проверку уведомлений")
            
            # Отправляем предупреждения об истечении
            expiration_results = self.send_expiration_warnings()
            
            # Отправляем уведомления об истекших подписках
            expired_results = self.send_subscription_expired_notifications()
            
            total_results = {
                'warnings_7_days': expiration_results['warnings_7_days'],
                'warnings_1_day': expiration_results['warnings_1_day'],
                'expired_notifications': expired_results['expired_notifications'],
                'total_errors': expiration_results['errors'] + expired_results['errors']
            }
            
            logger.info(f"Плановая проверка уведомлений завершена: {total_results}")
            return total_results
            
        except Exception as e:
            logger.error(f"Ошибка плановой проверки уведомлений: {str(e)}")
            return {
                'warnings_7_days': 0,
                'warnings_1_day': 0,
                'expired_notifications': 0,
                'total_errors': 1
            }
    
    def get_notification_stats(self) -> Dict[str, int]:
        """
        Получает статистику по уведомлениям
        
        Returns:
            Словарь со статистикой
        """
        try:
            # Получаем количество подписок по статусам
            active_subscriptions = len(self.db_service.get_all_active_subscriptions())
            expiring_7_days = len(self.db_service.get_subscriptions_expiring_in_days(7))
            expiring_1_day = len(self.db_service.get_subscriptions_expiring_in_days(1))
            expired_subscriptions = len(self.db_service.get_expired_subscriptions())
            
            stats = {
                'active_subscriptions': active_subscriptions,
                'expiring_in_7_days': expiring_7_days,
                'expiring_in_1_day': expiring_1_day,
                'expired_subscriptions': expired_subscriptions
            }
            
            logger.info(f"Статистика уведомлений: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Ошибка получения статистики уведомлений: {str(e)}")
            return {
                'active_subscriptions': 0,
                'expiring_in_7_days': 0,
                'expiring_in_1_day': 0,
                'expired_subscriptions': 0
            }