РУЧНОЕ ОБНОВЛЕНИЕ АДМИН-ПАНЕЛИ
===============================

1. СОЗДАНИЕ АРХИВА (выполните в PowerShell):
   Compress-Archive -Path "app\admin.py", "templates\*" -DestinationPath "admin_panel_fix.zip" -Force

2. ЗАГРУЗКА НА СЕРВЕР:
   pscp admin_panel_fix.zip ubuntu@**************:/home/<USER>/
   (Введите пароль: dkomqgTaijxro7in^bxd)

3. ПОДКЛЮЧЕНИЕ К СЕРВЕРУ:
   plink -ssh ubuntu@**************
   (Введите пароль: dkomqgTaijxro7in^bxd)

4. НА СЕРВЕРЕ ВЫПОЛНИТЕ ПО ОЧЕРЕДИ:
   cd /home/<USER>/telegram_bot
   sudo systemctl stop telegram-bot
   sudo cp -r app templates backup_$(date +%Y%m%d_%H%M%S)
   unzip -o /home/<USER>/admin_panel_fix.zip
   sudo chown -R ubuntu:ubuntu app templates
   sudo chmod -R 755 app templates
   sudo systemctl start telegram-bot
   sudo systemctl status telegram-bot

5. ПРОВЕРКА ОБНОВЛЕНИЙ:
   grep -q 'safe_url_for' app/admin.py && echo "admin.py: OK" || echo "admin.py: FAILED"
   grep -q 'safe_url_for' templates/base.html && echo "templates: OK" || echo "templates: FAILED"

6. ТЕСТИРОВАНИЕ:
   Откройте: https://**************/admin/login
   Пароль: admin123

7. ПРОВЕРКА ЛОГОВ (при необходимости):
   sudo journalctl -u telegram-bot -f