# Простой скрипт для исправления ошибки платежей
param(
    [string]$Server = "**************",
    [string]$User = "ubuntu"
)

Write-Host "Исправление ошибки платежей на сервере..." -ForegroundColor Green

try {
    # Создаем архив если его нет
    if (-not (Test-Path "payment_fix.zip")) {
        Write-Host "Создание архива..." -ForegroundColor Yellow
        Compress-Archive -Path "app/services/payment_service.py", "app/services/bot_handler.py" -DestinationPath "payment_fix.zip" -Force
    }
    
    # Загружаем архив
    Write-Host "Загрузка на сервер..." -ForegroundColor Yellow
    scp -o StrictHostKeyChecking=no payment_fix.zip "$User@${Server}:/home/<USER>/"
    
    # Выполняем команды на сервере
    Write-Host "Обновление файлов на сервере..." -ForegroundColor Yellow
    ssh -o StrictHostKeyChecking=no "$User@$Server" @"
sudo systemctl stop telegram-bot
cd /home/<USER>/telegram_bot
sudo cp -r app/services backup_services_$(date +%Y%m%d_%H%M%S)
unzip -o /home/<USER>/payment_fix.zip
sudo chown -R ubuntu:ubuntu app
sudo chmod -R 755 app
sudo systemctl start telegram-bot
sudo systemctl status telegram-bot --no-pager -l
"@
    
    Write-Host "Исправление завершено!" -ForegroundColor Green
    Write-Host "Проверьте работу бота в Telegram" -ForegroundColor Cyan
    
} catch {
    Write-Host "Ошибка: $($_.Exception.Message)" -ForegroundColor Red
}