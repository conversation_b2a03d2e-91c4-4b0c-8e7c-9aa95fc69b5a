#!/bin/bash

# Rinadzhi Bot Installation Script
echo "🚀 Установка Rinadzhi Telegram Payment Bot..."

# Получение root прав
sudo -i << 'EOF'

# Создание пользователя telegrambot
echo "👤 Создание пользователя telegrambot..."
if ! id "telegrambot" &>/dev/null; then
    useradd -m -s /bin/bash telegrambot
    echo "✅ Пользователь telegrambot создан"
else
    echo "ℹ️ Пользователь telegrambot уже существует"
fi

# Создание директорий
echo "📁 Создание директорий..."
mkdir -p /home/<USER>/app
mkdir -p /var/log/telegram-payment-bot
chown telegrambot:telegrambot /home/<USER>/app
chown telegrambot:telegrambot /var/log/telegram-payment-bot
echo "✅ Директории созданы"

# Установка Python и pip
echo "🐍 Проверка Python..."
if ! command -v python3 &> /dev/null; then
    echo "📦 Установка Python3..."
    apt update
    apt install -y python3 python3-pip python3-venv
fi
echo "✅ Python готов"

# Создание виртуального окружения
echo "🔧 Создание виртуального окружения..."
cd /home/<USER>/app
sudo -u telegrambot python3 -m venv venv
sudo -u telegrambot /home/<USER>/app/venv/bin/pip install --upgrade pip
echo "✅ Виртуальное окружение создано"

# Установка зависимостей
echo "📦 Установка зависимостей..."
sudo -u telegrambot /home/<USER>/app/venv/bin/pip install pyTelegramBotAPI requests
echo "✅ Зависимости установлены"

# Копирование файлов
echo "📄 Копирование файлов бота..."
cp /tmp/rinadzhi_bot.py /home/<USER>/app/
cp /tmp/config.py /home/<USER>/app/
cp /tmp/init_database.py /home/<USER>/app/

# Установка прав доступа
echo "🔐 Установка прав доступа..."
chown -R telegrambot:telegrambot /home/<USER>/app/
chmod +x /home/<USER>/app/rinadzhi_bot.py
echo "✅ Права доступа установлены"

# Инициализация базы данных
echo "🗄️ Инициализация базы данных..."
sudo -u telegrambot /home/<USER>/app/venv/bin/python /home/<USER>/app/init_database.py
echo "✅ База данных инициализирована"

# Создание systemd сервиса
echo "⚙️ Создание systemd сервиса..."
cat > /etc/systemd/system/rinadzhi-bot.service << 'SYSTEMD_EOF'
[Unit]
Description=Rinadzhi Telegram Payment Bot
After=network.target

[Service]
Type=simple
User=telegrambot
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/venv/bin/python rinadzhi_bot.py
Restart=always
RestartSec=10
StandardOutput=append:/var/log/telegram-payment-bot/rinadzhi_bot.log
StandardError=append:/var/log/telegram-payment-bot/rinadzhi_bot.log

[Install]
WantedBy=multi-user.target
SYSTEMD_EOF

systemctl daemon-reload
echo "✅ Systemd сервис создан"

# Запуск бота
echo "🚀 Запуск бота..."
systemctl start rinadzhi-bot
systemctl enable rinadzhi-bot
echo "✅ Бот запущен и добавлен в автозагрузку"

echo ""
echo "🎉 Установка завершена!"
echo ""
echo "📋 Управление ботом:"
echo "   systemctl status rinadzhi-bot   # Статус"
echo "   systemctl restart rinadzhi-bot  # Перезапуск"
echo "   systemctl stop rinadzhi-bot     # Остановка"
echo ""
echo "📊 Логи:"
echo "   journalctl -u rinadzhi-bot -f"
echo "   tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log"
echo ""
echo "✅ Бот готов к использованию!"

EOF
