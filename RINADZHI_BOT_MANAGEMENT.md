# Rinadzhi Bot - Команды управления

Полный набор команд для управления Telegram ботом Rinadzhi на сервере.

## 🔗 Подключение к серверу

**Базовая команда подключения:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd"
```

**⚠️ ВАЖНО:** При подключении появится сообщение "Access granted. Press Return to begin session." - **обязательно нажмите Enter**, иначе подключение зависнет!

**Получение root прав:**
```bash
sudo -i
```

## 🤖 Управление ботом

### 📊 Проверка статуса бота

**Проверить, запущен ли бот:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ps aux | grep rinadzhi"
```

**Проверить процессы Python:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ps aux | grep python"
```

**Проверить использование ресурсов:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "top -p \$(pgrep -f rinadzhi_bot)"
```

### 🚀 Запуск бота

**Запустить бота:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"
```

**Запустить с отображением вывода (для отладки):**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot bash -c 'cd /home/<USER>/app && ./venv/bin/python rinadzhi_bot.py'"
```

### 🛑 Остановка бота

**Остановить бота (мягко):**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot"
```

**Остановить все процессы Python (жестко):**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f python"
```

**Принудительная остановка:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -9 -f rinadzhi_bot"
```

### 🔄 Перезапуск бота

**Полный перезапуск:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot && sleep 3 && sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"
```

**Быстрый перезапуск:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot; sleep 2; sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"
```

## 📋 Просмотр логов

### 📄 Основные логи

**Последние 10 строк логов:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "tail -10 /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

**Последние 50 строк логов:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "tail -50 /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

**Просмотр логов в реальном времени:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "tail -f /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

**Поиск ошибок в логах:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "grep -i error /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

**Поиск активности пользователей:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "grep 'started the bot' /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

### 📊 Системные логи

**Системные логи (если используется systemd):**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo journalctl -u rinadzhi-bot -f"
```

**Логи системы:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo dmesg | tail -20"
```

## 📁 Управление файлами

### 📄 Просмотр файлов

**Список файлов бота:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ls -la /home/<USER>/app/"
```

**Просмотр конфигурации:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "cat /home/<USER>/app/config.py"
```

**Проверка прав доступа:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ls -la /home/<USER>/app/rinadzhi_bot.py"
```

### 📤 Загрузка файлов

**Загрузить новую версию бота:**
```bash
pscp -pw "dkomqgTaijxro7in^bxd" server_files/rinadzhi_bot.py ubuntu@**************:/tmp/rinadzhi_bot_new.py
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo cp /tmp/rinadzhi_bot_new.py /home/<USER>/app/rinadzhi_bot.py && sudo chown telegrambot:telegrambot /home/<USER>/app/rinadzhi_bot.py"
```

**Загрузить новую конфигурацию:**
```bash
pscp -pw "dkomqgTaijxro7in^bxd" server_files/config.py ubuntu@**************:/tmp/config_new.py
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo cp /tmp/config_new.py /home/<USER>/app/config.py && sudo chown telegrambot:telegrambot /home/<USER>/app/config.py"
```

### 💾 Резервное копирование

**Создать резервную копию:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo tar -czf /tmp/rinadzhi_bot_backup_\$(date +%Y%m%d_%H%M%S).tar.gz -C /home/<USER>/app ."
```

**Скачать резервную копию:**
```bash
pscp -pw "dkomqgTaijxro7in^bxd" ubuntu@**************:/tmp/rinadzhi_bot_backup_*.tar.gz ./
```

## 🗄️ Управление базой данных

### 📊 Проверка базы данных

**Проверить существование БД:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ls -la /home/<USER>/app/payments.db"
```

**Проверить таблицы:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot sqlite3 /home/<USER>/app/payments.db '.tables'"
```

**Количество пользователей:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot sqlite3 /home/<USER>/app/payments.db 'SELECT COUNT(*) FROM users;'"
```

**Активные подписки:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot sqlite3 /home/<USER>/app/payments.db 'SELECT COUNT(*) FROM subscriptions WHERE is_active = 1;'"
```

### 💾 Резервное копирование БД

**Создать резервную копию БД:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot cp /home/<USER>/app/payments.db /home/<USER>/backups/payments_backup_\$(date +%Y%m%d_%H%M%S).db"
```

**Скачать резервную копию БД:**
```bash
pscp -pw "dkomqgTaijxro7in^bxd" ubuntu@**************:/home/<USER>/app/payments.db ./payments_backup.db
```

## 🔧 Диагностика и устранение неполадок

### 🚨 Проверка проблем

**Проверить доступность Telegram API:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "curl -s https://api.telegram.org/bot7607933986:AAFCdrEKgOZdVwCH0jqYYgbus928wgEDxdA/getMe"
```

**Проверить webhook:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "curl -s https://api.telegram.org/bot7607933986:AAFCdrEKgOZdVwCH0jqYYgbus928wgEDxdA/getWebhookInfo"
```

**Проверить место на диске:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "df -h"
```

**Проверить память:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "free -h"
```

### 🔄 Исправление проблем

**Исправить права доступа:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo chown -R telegrambot:telegrambot /home/<USER>/app/ && sudo chmod 755 /home/<USER>/app && sudo chmod +x /home/<USER>/app/rinadzhi_bot.py"
```

**Переустановить зависимости:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot /home/<USER>/app/venv/bin/pip install --upgrade pyTelegramBotAPI requests"
```

**Очистить логи:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo truncate -s 0 /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

## 📈 Мониторинг

### 📊 Статистика использования

**Активность за последний час:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "grep '\$(date +%Y-%m-%d\ %H):' /var/log/telegram-payment-bot/rinadzhi_bot.log | wc -l"
```

**Уникальные пользователи за сегодня:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "grep '\$(date +%Y-%m-%d)' /var/log/telegram-payment-bot/rinadzhi_bot.log | grep 'started the bot' | wc -l"
```

**Проверить время работы бота:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ps -o pid,etime,cmd -p \$(pgrep -f rinadzhi_bot)"
```

## 🚀 Быстрые команды

### ⚡ Часто используемые

**Статус + логи:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "echo '=== STATUS ===' && ps aux | grep rinadzhi && echo '=== LOGS ===' && tail -5 /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

**Полная перезагрузка:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot; sleep 3; sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'; sleep 2; ps aux | grep rinadzhi"
```

**Проверка здоровья системы:**
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "echo '=== BOT STATUS ===' && ps aux | grep rinadzhi && echo '=== DISK SPACE ===' && df -h / && echo '=== MEMORY ===' && free -h && echo '=== RECENT LOGS ===' && tail -3 /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

---

## 📞 Поддержка

При возникновении проблем:
1. Проверьте статус бота
2. Посмотрите логи на наличие ошибок
3. Попробуйте перезапустить бота
4. Проверьте права доступа к файлам
5. Убедитесь, что есть место на диске

**Бот готов к работе!** 🚀
