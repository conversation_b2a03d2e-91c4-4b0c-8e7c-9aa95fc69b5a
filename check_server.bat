@echo off
echo Checking server structure...
echo.
echo 1. Home directory:
sshpass -p "dkomqgTaijxro7in^bxd" ssh -o StrictHostKeyChecking=no ubuntu@************** "ls -la ~/"

echo.
echo 2. Checking for .env files:
sshpass -p "dkomqgTaijxro7in^bxd" ssh -o StrictHostKeyChecking=no ubuntu@************** "sudo find /home -name '.env' -type f 2>/dev/null || echo 'No .env files found in /home'"

echo.
echo 3. Checking running processes:
sshpass -p "dkomqgTaijxro7in^bxd" ssh -o StrictHostKeyChecking=no ubuntu@************** "ps aux | grep -E 'python|node|telegram|bot' | grep -v grep"

echo.
echo 4. Checking systemd services:
sshpass -p "dkomqgTaijxro7in^bxd" ssh -o StrictHostKeyChecking=no ubuntu@************** "sudo systemctl list-units --type=service | findstr /i 'bot telegram node python'"

echo.
echo 5. Checking web directories:
sshpass -p "dkomqgTaijxro7in^bxd" ssh -o StrictHostKeyChecking=no ubuntu@************** "ls -la /var/www/ 2>/dev/null || echo '/var/www/ not found'"

pause
