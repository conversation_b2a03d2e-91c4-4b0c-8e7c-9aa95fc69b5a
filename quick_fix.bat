@echo off
echo Quick server check and fix...

echo === Quick status check ===
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" -batch "sudo systemctl is-active telegram-payment-bot.service"

echo === Restart service ===
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" -batch "sudo systemctl restart telegram-payment-bot.service"

echo === Wait and test ===
timeout /t 5 /nobreak > nul
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" -batch "curl -s -o /dev/null -w '%%{http_code}' http://localhost:5000/admin/login"

pause