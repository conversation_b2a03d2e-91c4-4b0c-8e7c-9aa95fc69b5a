@echo off
echo Checking server logs and status...
echo.

echo === Checking systemd service status ===
plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "sudo systemctl status telegram-payment-bot.service"
echo.

echo === Checking recent service logs ===
plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "sudo journalctl -u telegram-payment-bot.service -n 20 --no-pager"
echo.

echo === Checking if Flask app is running ===
plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "ps aux | grep python"
echo.

echo === Checking port 5000 ===
plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "ss -tlnp | grep :5000"
echo.

echo === Checking app directory structure ===
plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "ls -la /home/<USER>/app/"
echo.

echo === Checking if database exists ===
plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "ls -la /home/<USER>/app/payments.db"
echo.

echo === Checking environment variables ===
plink -ssh ubuntu@195.49.212.172 -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot cat /home/<USER>/app/.env | head -5"
echo.

pause