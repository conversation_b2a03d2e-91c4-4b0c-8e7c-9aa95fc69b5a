# Rinadzhi Bot - Быстрые команды

Самые важные команды для ежедневного управления ботом.

## 🔗 Подключение
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd"
```
**⚠️ Не забудьте нажать Enter после "Press Return to begin session."**

## ⚡ Основные команды

### 📊 Проверить статус
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "ps aux | grep rinadzhi"
```

### 📋 Посмотреть логи
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "tail -10 /var/log/telegram-payment-bot/rinadzhi_bot.log"
```

### 🔄 Перезапустить бота
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot && sleep 3 && sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"
```

### 🛑 Остановить бота
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot"
```

### 🚀 Запустить бота
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"
```

## 🔍 Диагностика

### Полная проверка системы
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "echo '=== BOT STATUS ===' && ps aux | grep rinadzhi && echo '=== RECENT LOGS ===' && tail -5 /var/log/telegram-payment-bot/rinadzhi_bot.log && echo '=== DISK SPACE ===' && df -h /"
```

### Поиск ошибок в логах
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "grep -i error /var/log/telegram-payment-bot/rinadzhi_bot.log | tail -5"
```

### Активность пользователей
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "grep 'started the bot' /var/log/telegram-payment-bot/rinadzhi_bot.log | tail -10"
```

## 📤 Обновление файлов

### Загрузить новую версию бота
```bash
pscp -pw "dkomqgTaijxro7in^bxd" server_files/rinadzhi_bot.py ubuntu@**************:/tmp/rinadzhi_bot_new.py
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo cp /tmp/rinadzhi_bot_new.py /home/<USER>/app/rinadzhi_bot.py && sudo chown telegrambot:telegrambot /home/<USER>/app/rinadzhi_bot.py"
```

### Полное обновление и перезапуск
```bash
pscp -pw "dkomqgTaijxro7in^bxd" server_files/rinadzhi_bot.py ubuntu@**************:/tmp/rinadzhi_bot_new.py
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo pkill -f rinadzhi_bot && sudo cp /tmp/rinadzhi_bot_new.py /home/<USER>/app/rinadzhi_bot.py && sudo chown telegrambot:telegrambot /home/<USER>/app/rinadzhi_bot.py && sleep 2 && sudo -u telegrambot bash -c 'cd /home/<USER>/app && nohup ./venv/bin/python rinadzhi_bot.py > /var/log/telegram-payment-bot/rinadzhi_bot.log 2>&1 &'"
```

## 🗄️ База данных

### Количество пользователей
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot sqlite3 /home/<USER>/app/payments.db 'SELECT COUNT(*) FROM users;'"
```

### Активные подписки
```bash
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" "sudo -u telegrambot sqlite3 /home/<USER>/app/payments.db 'SELECT COUNT(*) FROM subscriptions WHERE is_active = 1;'"
```

---

**📋 Полное руководство:** [RINADZHI_BOT_MANAGEMENT.md](RINADZHI_BOT_MANAGEMENT.md)

**🤖 Бот:** https://t.me/rinadzhi_bot
