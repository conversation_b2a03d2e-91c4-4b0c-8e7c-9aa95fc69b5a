# Simple deployment script for admin panel
param(
    [string]$Server = "**************",
    [string]$User = "ubuntu"
)

Write-Host "Starting admin panel update..." -ForegroundColor Green

try {
    # Create archive
    Write-Host "Creating archive..." -ForegroundColor Yellow
    if (Test-Path "admin_panel_fix.zip") {
        Remove-Item "admin_panel_fix.zip" -Force
    }
    Compress-Archive -Path "app/admin.py", "templates/" -DestinationPath "admin_panel_fix.zip" -Force
    Write-Host "Archive created successfully" -ForegroundColor Green

    # Upload to server
    Write-Host "Uploading to server..." -ForegroundColor Yellow
    scp -o StrictHostKeyChecking=no admin_panel_fix.zip ubuntu@**************:/home/<USER>/
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to upload archive"
    }
    Write-Host "Upload completed" -ForegroundColor Green

    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Connect to server: ssh ubuntu@**************" -ForegroundColor White
    Write-Host "2. Run deployment commands manually" -ForegroundColor White
    Write-Host ""

} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}