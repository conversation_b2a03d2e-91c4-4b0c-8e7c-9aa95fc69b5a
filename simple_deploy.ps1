# Simple PowerShell script for admin panel deployment
# Usage: .\simple_deploy.ps1

param(
    [string]$Server = "**************",
    [string]$User = "ubuntu"
)

Write-Host "Starting admin panel update on server..." -ForegroundColor Green

try {
    Write-Host "Creating archive..." -ForegroundColor Yellow
    if (Test-Path "admin_panel_fix.zip") {
        Remove-Item "admin_panel_fix.zip" -Force
    }
    Compress-Archive -Path "app\admin.py", "templates\*" -DestinationPath "admin_panel_fix.zip" -Force
    Write-Host "Archive created successfully" -ForegroundColor Green

    Write-Host "Uploading archive to server..." -ForegroundColor Yellow
    $scpArgs = @("-o", "StrictHostKeyChecking=no", "admin_panel_fix.zip", "$User@${Server}:/home/<USER>/")
    & scp $scpArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to upload archive"
    }
    Write-Host "Archive uploaded successfully" -ForegroundColor Green

    Write-Host "Stopping service..." -ForegroundColor Yellow
    $sshArgs = @("-o", "StrictHostKeyChecking=no", "$User@$Server", "sudo systemctl stop telegram-bot")
    & ssh $sshArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to stop service"
    }
    Write-Host "Service stopped" -ForegroundColor Green

    Write-Host "Creating backup..." -ForegroundColor Yellow
    $backupCmd = "cd /home/<USER>/telegram_bot; sudo cp -r app templates backup_`$(date +%Y%m%d_%H%M%S)"
    $sshArgs = @("-o", "StrictHostKeyChecking=no", "$User@$Server", $backupCmd)
    & ssh $sshArgs
    Write-Host "Backup created" -ForegroundColor Green

    Write-Host "Extracting updates..." -ForegroundColor Yellow
    $extractCmd = "cd /home/<USER>/telegram_bot; unzip -o /home/<USER>/admin_panel_fix.zip"
    $sshArgs = @("-o", "StrictHostKeyChecking=no", "$User@$Server", $extractCmd)
    & ssh $sshArgs
    Write-Host "Updates extracted" -ForegroundColor Green

    Write-Host "Setting permissions..." -ForegroundColor Yellow
    $permCmd = "cd /home/<USER>/telegram_bot; sudo chown -R ubuntu:ubuntu app templates; sudo chmod -R 755 app templates"
    $sshArgs = @("-o", "StrictHostKeyChecking=no", "$User@$Server", $permCmd)
    & ssh $sshArgs
    Write-Host "Permissions set" -ForegroundColor Green

    Write-Host "Starting service..." -ForegroundColor Yellow
    $sshArgs = @("-o", "StrictHostKeyChecking=no", "$User@$Server", "sudo systemctl start telegram-bot")
    & ssh $sshArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to start service"
    }
    Write-Host "Service started" -ForegroundColor Green

    Write-Host "Waiting for service to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5

    Write-Host "Checking service status..." -ForegroundColor Yellow
    $statusCmd = "sudo systemctl status telegram-bot --no-pager -l"
    $sshArgs = @("-o", "StrictHostKeyChecking=no", "$User@$Server", $statusCmd)
    & ssh $sshArgs

    Write-Host ""
    Write-Host "UPDATE COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Open https://$Server/admin/login" -ForegroundColor White
    Write-Host "2. Enter password: admin123" -ForegroundColor White
    Write-Host "3. Test admin panel functionality" -ForegroundColor White
    Write-Host ""
    Write-Host "To view logs in real-time:" -ForegroundColor Cyan
    Write-Host "ssh $User@$Server 'sudo journalctl -u telegram-bot -f'" -ForegroundColor White

} catch {
    Write-Host ""
    Write-Host "ERROR DURING UPDATE: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Try:" -ForegroundColor Yellow
    Write-Host "1. Check server connection: ssh $User@$Server" -ForegroundColor White
    Write-Host "2. Restart service manually: ssh $User@$Server 'sudo systemctl restart telegram-bot'" -ForegroundColor White
    
    exit 1
}