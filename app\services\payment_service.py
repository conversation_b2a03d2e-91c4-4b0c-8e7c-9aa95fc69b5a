import requests
import hashlib
import hmac
import json
import time
import logging
import functools
from typing import Dict, Optional, Any, List
from decimal import Decimal
from datetime import datetime, timedelta
from config import Config

from app.utils.error_handler import (
    ErrorCategory, ErrorSeverity, with_error_handling, with_retry
)
from app.utils.api_error_handler import (
    with_api_error_handling, with_api_retry, with_rate_limiting, 
    APIErrorHandler, APIHealthMonitor
)

logger = logging.getLogger('payment')

class PaymentService:
    """Сервис для работы с платежной системой Lava.top"""
    
    # Конфигурация способов оплаты
    PAYMENT_METHODS = {
        'card_ru': {
            'service': 'card_ru',
            'name': 'Карта РФ',
            'description': 'Оплата российской банковской картой'
        },
        'card_foreign': {
            'service': 'card_intl', 
            'name': 'Карта иностранного банка',
            'description': 'Оплата международной банковской картой'
        },
        'crypto': {
            'service': 'crypto',
            'name': 'Криптовалюта',
            'description': 'Оплата криптовалютой'
        }
    }
    
    # Тарифные планы
    SUBSCRIPTION_PLANS = {
        1: {'months': 1, 'price': Decimal('299.00'), 'name': '1 месяц'},
        3: {'months': 3, 'price': Decimal('799.00'), 'name': '3 месяца'},
        6: {'months': 6, 'price': Decimal('1499.00'), 'name': '6 месяцев'},
        12: {'months': 12, 'price': Decimal('2799.00'), 'name': '12 месяцев'}
    }
    
    def __init__(self):
        self.api_key = Config.LAVA_API_KEY
        self.secret_key = Config.LAVA_SECRET_KEY
        self.base_url = Config.LAVA_BASE_URL or 'https://api.lava.top'
        self.webhook_url = Config.WEBHOOK_URL
        self.session = requests.Session()
        self.session.headers.update({
            'X-API-Key': self.api_key,
            'Content-Type': 'application/json'
        })
        
        # Инициализируем обработчики ошибок
        self.api_error_handler = APIErrorHandler()
        self.health_monitor = APIHealthMonitor('lava_api')
        
    def create_invoice(self, user_id: int, plan_months: int, payment_method: str) -> Dict[str, Any]:
        """
        Создает счет для оплаты через Lava.top API
        
        Args:
            user_id: ID пользователя Telegram
            plan_months: Количество месяцев подписки (1, 3, 6, 12)
            payment_method: Способ оплаты ('ru_card', 'foreign_card', 'crypto')
            
        Returns:
            Dict с данными созданного счета или ошибкой
        """
        try:
            # Валидация входных параметров
            if plan_months not in self.SUBSCRIPTION_PLANS:
                raise ValueError(f"Неподдерживаемый план подписки: {plan_months}")
                
            if payment_method not in self.PAYMENT_METHODS:
                raise ValueError(f"Неподдерживаемый способ оплаты: {payment_method}")
            
            plan = self.SUBSCRIPTION_PLANS[plan_months]
            method_config = self.PAYMENT_METHODS[payment_method]
            
            # Генерация уникального order_id
            timestamp = int(time.time())
            order_id = f"tg_{user_id}_{timestamp}"
            
            # Подготовка данных для создания счета согласно Lava API v2
            invoice_data = {
                'orderId': order_id,
                'sum': float(plan['price']),
                'currency': 'RUB',
                'offerId': '5b34c4d5-56a8-4d12-b666-ef6f6649ad13',  # ID оффера из Lava.top
                'email': f'user_{user_id}@telegram.bot',  # Email пользователя (генерируем для Telegram)
                'description': f"Подписка на канал - {plan['name']}",
                'successUrl': f"{self.webhook_url}/success",
                'failUrl': f"{self.webhook_url}/fail",
                'hookUrl': f"{self.webhook_url}/webhook",
                'expire': int((datetime.now() + timedelta(hours=1)).timestamp()),
                'custom': {
                    'user_id': user_id,
                    'plan_months': plan_months,
                    'payment_method': payment_method
                }
            }
            
            logger.info(f"Создание счета для пользователя {user_id}, план {plan_months} мес., метод {payment_method}")
            
            # Отправка запроса к Lava.top API
            response = self._make_api_request('POST', '/api/v2/invoice', invoice_data)
            
            logger.info(f"Ответ API: {response}")
            
            # Lava API возвращает данные напрямую, без обертки status/data
            if response.get('id'):
                result = {
                    'success': True,
                    'order_id': order_id,
                    'invoice_id': response.get('id'),
                    'payment_url': response.get('paymentUrl'),
                    'amount': plan['price'],
                    'currency': 'RUB',
                    'expires_at': datetime.fromtimestamp(invoice_data['expire']),
                    'payment_method': payment_method,
                    'plan_months': plan_months
                }
                
                logger.info(f"Счет успешно создан: {order_id}, URL: {result['payment_url']}")
                return result
            elif response.get('status') == 'error':
                error_msg = response.get('message', 'Неизвестная ошибка API')
                logger.error(f"Ошибка создания счета: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'error_code': response.get('code')
                }
            else:
                error_msg = response.get('message', 'Неизвестная ошибка API')
                logger.error(f"Неожиданный ответ API: {response}")
                return {
                    'success': False,
                    'error': error_msg,
                    'error_code': response.get('code')
                }
                
        except ValueError as e:
            logger.error(f"Ошибка валидации данных: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_code': 'VALIDATION_ERROR'
            }
        except Exception as e:
            logger.error(f"Неожиданная ошибка при создании счета: {str(e)}")
            return {
                'success': False,
                'error': 'Внутренняя ошибка сервиса',
                'error_code': 'INTERNAL_ERROR'
            }
    
    def get_payment_status(self, order_id: str) -> Dict[str, Any]:
        """
        Получает статус платежа по order_id
        
        Args:
            order_id: Уникальный идентификатор заказа
            
        Returns:
            Dict со статусом платежа
        """
        try:
            logger.info(f"Проверка статуса платежа: {order_id}")
            
            # Для Lava API используем invoice_id вместо order_id
            response = self._make_api_request('GET', f'/api/v1/invoices/{order_id}')
            
            if response.get('status') == 'success':
                payment_data = response.get('data', {})
                return {
                    'success': True,
                    'order_id': order_id,
                    'status': payment_data.get('status'),
                    'amount': payment_data.get('amount'),
                    'currency': payment_data.get('currency'),
                    'paid_at': payment_data.get('paid_at'),
                    'invoice_id': payment_data.get('id')
                }
            else:
                error_msg = response.get('message', 'Ошибка получения статуса')
                logger.error(f"Ошибка получения статуса платежа {order_id}: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'error_code': response.get('code')
                }
                
        except Exception as e:
            logger.error(f"Ошибка при получении статуса платежа {order_id}: {str(e)}")
            return {
                'success': False,
                'error': 'Ошибка связи с платежной системой',
                'error_code': 'API_ERROR'
            }
    
    def validate_webhook_signature(self, payload: str, signature: str) -> bool:
        """
        Валидирует подпись webhook от Lava.top
        
        Args:
            payload: Тело запроса webhook
            signature: Подпись из заголовка
            
        Returns:
            True если подпись валидна, False иначе
        """
        try:
            expected_signature = hmac.new(
                self.secret_key.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Ошибка валидации подписи webhook: {str(e)}")
            return False
    
    @with_api_retry('lava_api', max_retries=3)
    @with_rate_limiting(max_requests=10, time_window=60)  # 10 запросов в минуту
    def _make_api_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Выполняет запрос к API с автоматическими повторными попытками и rate limiting
        
        Args:
            method: HTTP метод
            endpoint: Конечная точка API
            data: Данные для отправки
            
        Returns:
            Ответ API в виде словаря
        """
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=30)
            else:
                raise ValueError(f"Неподдерживаемый HTTP метод: {method}")
            
            response.raise_for_status()
            result = response.json()
            
            # Записываем успешный запрос в мониторинг
            response_time = time.time() - start_time
            self.health_monitor.record_request(True, response_time)
            
            return result
            
        except requests.exceptions.HTTPError as e:
            # Записываем неудачный запрос в мониторинг
            response_time = time.time() - start_time
            self.health_monitor.record_request(False, response_time)
            
            # Обрабатываем HTTP ошибку через специализированный обработчик
            error_info = self.api_error_handler.handle_http_error(e, {
                'method': method,
                'endpoint': endpoint,
                'status_code': e.response.status_code if e.response else 0
            })
            
            try:
                error_data = e.response.json() if e.response else {}
                return {
                    'status': 'error',
                    'message': error_data.get('message', 'HTTP ошибка'),
                    'code': error_data.get('code', f'HTTP_{e.response.status_code}')
                }
            except:
                return {
                    'status': 'error',
                    'message': f'HTTP ошибка {e.response.status_code}',
                    'code': f'HTTP_{e.response.status_code}'
                }
                
        except requests.exceptions.RequestException as e:
            # Записываем неудачный запрос в мониторинг
            response_time = time.time() - start_time
            self.health_monitor.record_request(False, response_time)
            
            # Обрабатываем сетевую ошибку
            self.api_error_handler.handle_http_error(e, {
                'method': method,
                'endpoint': endpoint,
                'error_type': type(e).__name__
            })
            
            # Поднимаем исключение для обработки декоратором retry
            raise
            
        except Exception as e:
            # Записываем неудачный запрос в мониторинг
            response_time = time.time() - start_time
            self.health_monitor.record_request(False, response_time)
            
            logger.error(f"Неожиданная ошибка API запроса: {str(e)}")
            return {
                'status': 'error',
                'message': 'Внутренняя ошибка сервиса',
                'code': 'INTERNAL_ERROR'
            }
    
    @classmethod
    def get_available_payment_methods(cls) -> Dict[str, Dict[str, str]]:
        """Возвращает доступные способы оплаты"""
        return cls.PAYMENT_METHODS.copy()
    
    @classmethod
    def get_subscription_plans(cls) -> Dict[int, Dict[str, Any]]:
        """Возвращает доступные тарифные планы"""
        return cls.SUBSCRIPTION_PLANS.copy()
    
    @classmethod
    def calculate_plan_price(cls, plan_months: int) -> Optional[Decimal]:
        """Возвращает цену тарифного плана"""
        plan = cls.SUBSCRIPTION_PLANS.get(plan_months)
        return plan['price'] if plan else None
    
    def check_stuck_payments(self, db_service) -> Dict[str, Any]:
        """
        Проверяет статус зависших платежей и обновляет их в базе данных
        
        Args:
            db_service: Экземпляр DatabaseService для работы с БД
            
        Returns:
            Dict с результатами проверки
        """
        try:
            logger.info("Начинаем проверку зависших платежей")
            
            # Получаем все платежи со статусом 'pending' старше 10 минут
            stuck_payments = self._get_stuck_payments(db_service)
            
            if not stuck_payments:
                logger.info("Зависших платежей не найдено")
                return {
                    'success': True,
                    'checked_count': 0,
                    'updated_count': 0,
                    'errors': []
                }
            
            logger.info(f"Найдено {len(stuck_payments)} зависших платежей для проверки")
            
            updated_count = 0
            errors = []
            
            for payment in stuck_payments:
                try:
                    # Проверяем статус платежа через API
                    status_result = self.get_payment_status(payment.lava_invoice_id)
                    
                    if status_result['success']:
                        api_status = status_result['status']
                        
                        # Обновляем статус в БД если он изменился
                        if api_status != payment.status:
                            if api_status == 'completed':
                                # Платеж завершен успешно
                                completed_at = datetime.now()
                                if status_result.get('paid_at'):
                                    try:
                                        completed_at = datetime.fromisoformat(
                                            status_result['paid_at'].replace('Z', '+00:00')
                                        )
                                    except:
                                        pass
                                
                                success = db_service.update_payment_status(
                                    payment.lava_invoice_id, 
                                    'completed', 
                                    completed_at
                                )
                                
                                if success:
                                    logger.info(f"Платеж {payment.lava_invoice_id} обновлен на 'completed'")
                                    updated_count += 1
                                    
                                    # Создаем или продлеваем подписку
                                    self._process_successful_payment(payment, db_service)
                                    
                            elif api_status in ['failed', 'expired', 'cancelled']:
                                # Платеж не удался
                                success = db_service.update_payment_status(
                                    payment.lava_invoice_id, 
                                    api_status
                                )
                                
                                if success:
                                    logger.info(f"Платеж {payment.lava_invoice_id} обновлен на '{api_status}'")
                                    updated_count += 1
                            
                            else:
                                logger.info(f"Платеж {payment.lava_invoice_id} остается в статусе '{api_status}'")
                        
                    else:
                        error_msg = f"Ошибка проверки статуса платежа {payment.lava_invoice_id}: {status_result.get('error', 'Unknown error')}"
                        logger.error(error_msg)
                        errors.append(error_msg)
                        
                except Exception as e:
                    error_msg = f"Ошибка обработки платежа {payment.lava_invoice_id}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    
                # Небольшая задержка между запросами к API
                time.sleep(0.5)
            
            logger.info(f"Проверка завершена. Обновлено платежей: {updated_count}, ошибок: {len(errors)}")
            
            return {
                'success': True,
                'checked_count': len(stuck_payments),
                'updated_count': updated_count,
                'errors': errors
            }
            
        except Exception as e:
            logger.error(f"Критическая ошибка при проверке зависших платежей: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'checked_count': 0,
                'updated_count': 0,
                'errors': []
            }
    
    def _get_stuck_payments(self, db_service) -> List:
        """
        Получает список зависших платежей из базы данных
        
        Args:
            db_service: Экземпляр DatabaseService
            
        Returns:
            Список платежей со статусом 'pending' старше 10 минут
        """
        try:
            with db_service.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Получаем платежи со статусом 'pending' старше 10 минут
                cursor.execute('''
                    SELECT * FROM payments 
                    WHERE status = 'pending' 
                    AND created_at < datetime('now', '-10 minutes')
                    AND (expires_at IS NULL OR expires_at > datetime('now'))
                    ORDER BY created_at ASC
                    LIMIT 50
                ''')
                
                rows = cursor.fetchall()
                payments = []
                
                for row in rows:
                    payment = type('Payment', (), {
                        'id': row['id'],
                        'user_id': row['user_id'],
                        'lava_invoice_id': row['lava_invoice_id'],
                        'amount': row['amount'],
                        'currency': row['currency'],
                        'payment_method': row['payment_method'],
                        'status': row['status'],
                        'created_at': datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        'expires_at': datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    })()
                    payments.append(payment)
                
                return payments
                
        except Exception as e:
            logger.error(f"Ошибка получения зависших платежей: {str(e)}")
            return []
    
    def _process_successful_payment(self, payment, db_service):
        """
        Обрабатывает успешный платеж - создает или продлевает подписку
        
        Args:
            payment: Объект платежа
            db_service: Экземпляр DatabaseService
        """
        try:
            # Извлекаем информацию о плане из order_id
            # Формат order_id: tg_{user_id}_{timestamp}
            order_parts = payment.lava_invoice_id.split('_')
            if len(order_parts) >= 2:
                user_telegram_id = int(order_parts[1])
                
                # Получаем пользователя
                user = db_service.get_user_by_telegram_id(user_telegram_id)
                if user:
                    # Определяем план подписки по сумме платежа
                    plan_months = self._determine_plan_by_amount(payment.amount)
                    
                    if plan_months:
                        # Создаем или продлеваем подписку
                        success = db_service.create_or_extend_subscription(user.id, plan_months)
                        
                        if success:
                            logger.info(f"Подписка успешно создана/продлена для пользователя {user_telegram_id} на {plan_months} месяцев")
                        else:
                            logger.error(f"Ошибка создания/продления подписки для пользователя {user_telegram_id}")
                    else:
                        logger.error(f"Не удалось определить план подписки по сумме {payment.amount}")
                else:
                    logger.error(f"Пользователь с Telegram ID {user_telegram_id} не найден")
            else:
                logger.error(f"Неверный формат order_id: {payment.lava_invoice_id}")
                
        except Exception as e:
            logger.error(f"Ошибка обработки успешного платежа {payment.lava_invoice_id}: {str(e)}")
    
    def _determine_plan_by_amount(self, amount: float) -> Optional[int]:
        """
        Определяет план подписки по сумме платежа
        
        Args:
            amount: Сумма платежа
            
        Returns:
            Количество месяцев подписки или None
        """
        amount_decimal = Decimal(str(amount))
        
        for months, plan in self.SUBSCRIPTION_PLANS.items():
            if plan['price'] == amount_decimal:
                return months
        
        return None
    
    def check_expired_payments(self, db_service) -> Dict[str, Any]:
        """
        Проверяет и обновляет статус истекших платежей
        
        Args:
            db_service: Экземпляр DatabaseService
            
        Returns:
            Dict с результатами обработки
        """
        try:
            logger.info("Начинаем проверку истекших платежей")
            
            # Получаем истекшие платежи
            expired_payments = db_service.get_expired_payments()
            
            if not expired_payments:
                logger.info("Истекших платежей не найдено")
                return {
                    'success': True,
                    'processed_count': 0
                }
            
            logger.info(f"Найдено {len(expired_payments)} истекших платежей")
            
            processed_count = 0
            
            for payment in expired_payments:
                try:
                    # Обновляем статус на 'expired'
                    success = db_service.update_payment_status(payment.lava_invoice_id, 'expired')
                    
                    if success:
                        logger.info(f"Платеж {payment.lava_invoice_id} помечен как истекший")
                        processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Ошибка обновления истекшего платежа {payment.lava_invoice_id}: {str(e)}")
            
            logger.info(f"Обработано истекших платежей: {processed_count}")
            
            return {
                'success': True,
                'processed_count': processed_count
            }
            
        except Exception as e:
            logger.error(f"Ошибка проверки истекших платежей: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processed_count': 0
            }