# Deploy payment fix to server
param(
    [string]$Server = "**************",
    [string]$User = "ubuntu"
)

Write-Host "Deploying payment fix to server..." -ForegroundColor Green

try {
    # Create archive if not exists
    if (-not (Test-Path "payment_fix.zip")) {
        Write-Host "Creating archive..." -ForegroundColor Yellow
        Compress-Archive -Path "app/services/payment_service.py", "app/services/bot_handler.py" -DestinationPath "payment_fix.zip" -Force
    }
    
    # Upload archive
    Write-Host "Uploading to server..." -ForegroundColor Yellow
    scp -o StrictHostKeyChecking=no payment_fix.zip "$User@${Server}:/home/<USER>/"
    
    # Execute commands on server
    Write-Host "Updating files on server..." -ForegroundColor Yellow
    $commands = @"
sudo systemctl stop telegram-bot
cd /home/<USER>/telegram_bot
sudo cp -r app/services backup_services_`$(date +%Y%m%d_%H%M%S)
unzip -o /home/<USER>/payment_fix.zip
sudo chown -R ubuntu:ubuntu app
sudo chmod -R 755 app
sudo systemctl start telegram-bot
sleep 5
sudo systemctl status telegram-bot --no-pager -l
"@
    
    ssh -o StrictHostKeyChecking=no "$User@$Server" $commands
    
    Write-Host "Payment fix deployed successfully!" -ForegroundColor Green
    Write-Host "Please test the bot in Telegram" -ForegroundColor Cyan
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}