# Production Environment Configuration
# Конфигурация для production окружения

# ===========================================
# ОБЯЗАТЕЛЬНЫЕ НАСТРОЙКИ - ЗАПОЛНИТЕ ПЕРЕД ЗАПУСКОМ
# ===========================================

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************

# Lava.top API Configuration  
LAVA_API_KEY=WnJ549MVZPHMtNPuUdtLaxS9nWhNVh3e4oazrhFVmUDB2qJaql36ECIeOX4ixD6q
LAVA_SECRET_KEY=WnJ549MVZPHMtNPuUdtLaxS9nWhNVh3e4oazrhFVmUDB2qJaql36ECIeOX4ixD6q
LAVA_BASE_URL=https://gate.lava.top

# Webhook Configuration
WEBHOOK_URL=https://195.49.212.172/webhook

# Channel Configuration
CHANNEL_ID=-1001002786126628

# Admin Configuration
ADMIN_USER_IDS=389794370
ADMIN_PASSWORD=SecureAdmin2024!

# Security - ОБЯЗАТЕЛЬНО ИЗМЕНИТЕ
SECRET_KEY=your-very-secure-secret-key-change-this-in-production-2024

# ===========================================
# НАСТРОЙКИ БАЗЫ ДАННЫХ
# ===========================================

# SQLite (по умолчанию)
DATABASE_URL=sqlite:///payments.db

# PostgreSQL (рекомендуется для production)
# DATABASE_URL=postgresql://username:password@localhost:5432/telegram_bot

# MySQL (альтернатива)
# DATABASE_URL=mysql://username:password@localhost:3306/telegram_bot

# ===========================================
# НАСТРОЙКИ ПРОИЗВОДИТЕЛЬНОСТИ
# ===========================================

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False

# Gunicorn Configuration (если используется)
WORKERS=2
WORKER_CLASS=sync
WORKER_CONNECTIONS=1000
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100
TIMEOUT=30
KEEPALIVE=2

# ===========================================
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# ===========================================

# Log Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log File Paths
LOG_FILE=/var/log/telegram-payment-bot/app.log
ERROR_LOG_FILE=/var/log/telegram-payment-bot/error.log

# Log Rotation
LOG_MAX_BYTES=10485760  # 10MB
LOG_BACKUP_COUNT=5

# ===========================================
# НАСТРОЙКИ БЕЗОПАСНОСТИ
# ===========================================

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# IP Whitelist для webhook (опционально)
# WEBHOOK_IP_WHITELIST=***********/27,***********/27

# Session Configuration
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax
PERMANENT_SESSION_LIFETIME=3600

# ===========================================
# НАСТРОЙКИ УВЕДОМЛЕНИЙ
# ===========================================

# Notification Chat ID (для системных уведомлений)
NOTIFICATION_CHAT_ID=

# Email Notifications (опционально)
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true
ADMIN_EMAIL=

# ===========================================
# НАСТРОЙКИ МОНИТОРИНГА
# ===========================================

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# Metrics
METRICS_ENABLED=true
METRICS_PATH=/metrics

# Sentry (опционально)
# SENTRY_DSN=https://your-sentry-dsn

# ===========================================
# НАСТРОЙКИ РЕЗЕРВНОГО КОПИРОВАНИЯ
# ===========================================

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=/home/<USER>/backups

# S3 Backup (опционально)
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_S3_BUCKET=
# AWS_S3_REGION=

# ===========================================
# НАСТРОЙКИ КЭШИРОВАНИЯ
# ===========================================

# Redis (опционально, для кэширования)
# REDIS_URL=redis://localhost:6379/0
# CACHE_TYPE=redis
# CACHE_DEFAULT_TIMEOUT=300

# Simple Cache (по умолчанию)
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# ===========================================
# НАСТРОЙКИ ПЛАНИРОВЩИКА
# ===========================================

# Scheduler Configuration
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=Europe/Moscow

# Job Intervals (в минутах)
CHECK_SUBSCRIPTIONS_INTERVAL=60
SEND_NOTIFICATIONS_INTERVAL=1440  # 24 часа
CLEANUP_PAYMENTS_INTERVAL=1440    # 24 часа
BACKUP_INTERVAL=1440              # 24 часа

# ===========================================
# НАСТРОЙКИ ИНТЕГРАЦИЙ
# ===========================================

# Telegram API Configuration
TELEGRAM_API_TIMEOUT=30
TELEGRAM_API_RETRIES=3

# Lava.top API Configuration
LAVA_API_TIMEOUT=30
LAVA_API_RETRIES=3

# ===========================================
# ДОПОЛНИТЕЛЬНЫЕ НАСТРОЙКИ
# ===========================================

# Timezone
TZ=Europe/Moscow

# Language
LANGUAGE=ru

# Currency
CURRENCY=RUB

# Subscription Plans (в месяцах и рублях)
PLAN_1_MONTHS=1
PLAN_1_PRICE=299
PLAN_3_MONTHS=3
PLAN_3_PRICE=799
PLAN_6_MONTHS=6
PLAN_6_PRICE=1499
PLAN_12_MONTHS=12
PLAN_12_PRICE=2799

# Payment Methods
PAYMENT_METHODS=ru_card,foreign_card,crypto

# Channel Settings
INVITE_LINK_EXPIRE_HOURS=24
MAX_INVITE_USES=1

# ===========================================
# НАСТРОЙКИ РАЗРАБОТКИ (НЕ ИСПОЛЬЗУЙТЕ В PRODUCTION)
# ===========================================

# Development Settings - ОТКЛЮЧИТЕ В PRODUCTION
# FLASK_DEBUG=False
# TESTING=False
# WTF_CSRF_ENABLED=True