@echo off
echo Fixing Flask app database initialization...
echo.

echo === Stopping the service ===
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" -batch "sudo systemctl stop telegram-payment-bot.service"
echo.

echo === Checking and creating database ===
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" -batch "cd /home/<USER>/app && sudo -u telegrambot python3 -c \"
from app.models.database import DatabaseService
import os
print('Initializing database...')
db = DatabaseService()
db.init_database()
print('Database initialized successfully')
\""
echo.

echo === Restarting the service ===
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" -batch "sudo systemctl start telegram-payment-bot.service"
echo.

echo === Checking service status ===
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" -batch "sudo systemctl status telegram-payment-bot.service --no-pager"
echo.

echo === Testing web interface ===
plink -ssh ubuntu@************** -pw "dkomqgTaijxro7in^bxd" -batch "curl -s http://localhost:5000/admin/login | head -10"
echo.

pause