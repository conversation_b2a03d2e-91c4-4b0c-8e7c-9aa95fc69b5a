"""
Тесты для системы уведомлений пользователей
"""

import pytest
import unittest
from unittest.mock import Mock, MagicMock, patch, call
from datetime import datetime, timedelta
from decimal import Decimal

from app.services.notification_service import NotificationService
from app.services.bot_handler import TelegramBotHandler
from app.models.database import DatabaseService, User, Subscription, Payment


class TestNotificationService(unittest.TestCase):
    """Тесты для NotificationService"""
    
    def setUp(self):
        """Настройка тестов"""
        self.mock_bot_handler = Mock(spec=TelegramBotHandler)
        self.mock_db_service = Mock(spec=DatabaseService)
        self.notification_service = NotificationService(
            self.mock_bot_handler, 
            self.mock_db_service
        )
        
        # Тестовые данные
        self.test_user_id = 123456789
        self.test_user = User(
            id=1,
            telegram_id=self.test_user_id,
            username="testuser",
            first_name="Test",
            last_name="User",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.test_subscription = Subscription(
            id=1,
            user_id=1,
            plan_type="monthly",
            status="active",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.test_invoice_data = {
            'order_id': 'test_order_123',
            'amount': 500.0,
            'currency': 'RUB',
            'payment_method': 'ru_card',
            'payment_url': 'https://test-payment-url.com',
            'expires_at': datetime.now() + timedelta(hours=1)
        }
    
    def test_send_payment_link_notification_success(self):
        """Тест успешной отправки ссылки для оплаты"""
        # Arrange
        self.mock_bot_handler.send_payment_link.return_value = True
        plan_months = 1
        
        # Act
        result = self.notification_service.send_payment_link_notification(
            self.test_user_id, self.test_invoice_data, plan_months
        )
        
        # Assert
        self.assertTrue(result)
        self.mock_bot_handler.send_payment_link.assert_called_once_with(
            self.test_user_id, self.test_invoice_data, plan_months
        )
    
    def test_send_payment_link_notification_failure(self):
        """Тест неудачной отправки ссылки для оплаты"""
        # Arrange
        self.mock_bot_handler.send_payment_link.return_value = False
        plan_months = 1
        
        # Act
        result = self.notification_service.send_payment_link_notification(
            self.test_user_id, self.test_invoice_data, plan_months
        )
        
        # Assert
        self.assertFalse(result)
        self.mock_bot_handler.send_payment_link.assert_called_once()
    
    def test_send_invite_link_notification_success(self):
        """Тест успешной отправки пригласительной ссылки"""
        # Arrange
        self.mock_bot_handler.send_invite_link.return_value = True
        invite_url = "https://t.me/+test_invite_link"
        subscription_end_date = datetime.now() + timedelta(days=30)
        
        # Act
        result = self.notification_service.send_invite_link_notification(
            self.test_user_id, invite_url, subscription_end_date
        )
        
        # Assert
        self.assertTrue(result)
        self.mock_bot_handler.send_invite_link.assert_called_once_with(
            self.test_user_id, invite_url, subscription_end_date
        )
    
    def test_send_invite_link_notification_failure(self):
        """Тест неудачной отправки пригласительной ссылки"""
        # Arrange
        self.mock_bot_handler.send_invite_link.return_value = False
        invite_url = "https://t.me/+test_invite_link"
        subscription_end_date = datetime.now() + timedelta(days=30)
        
        # Act
        result = self.notification_service.send_invite_link_notification(
            self.test_user_id, invite_url, subscription_end_date
        )
        
        # Assert
        self.assertFalse(result)
        self.mock_bot_handler.send_invite_link.assert_called_once()
    
    def test_send_payment_confirmation_success(self):
        """Тест успешной отправки подтверждения оплаты"""
        # Arrange
        self.mock_bot_handler.send_payment_confirmation.return_value = True
        payment_amount = 500.0
        payment_method = "ru_card"
        
        # Act
        result = self.notification_service.send_payment_confirmation(
            self.test_user_id, payment_amount, payment_method
        )
        
        # Assert
        self.assertTrue(result)
        self.mock_bot_handler.send_payment_confirmation.assert_called_once_with(
            self.test_user_id, payment_amount, payment_method
        )
    
    def test_send_expiration_warnings_success(self):
        """Тест успешной отправки предупреждений об истечении"""
        # Arrange
        subscriptions_7_days = [self.test_subscription]
        subscriptions_1_day = [self.test_subscription]
        
        self.mock_db_service.get_subscriptions_expiring_in_days.side_effect = [
            subscriptions_7_days,  # Для 7 дней
            subscriptions_1_day    # Для 1 дня
        ]
        self.mock_db_service.get_user_by_id.return_value = self.test_user
        self.mock_bot_handler.send_expiration_warning.return_value = True
        
        # Act
        result = self.notification_service.send_expiration_warnings()
        
        # Assert
        expected_result = {
            'warnings_7_days': 1,
            'warnings_1_day': 1,
            'errors': 0
        }
        self.assertEqual(result, expected_result)
        
        # Проверяем вызовы
        self.mock_db_service.get_subscriptions_expiring_in_days.assert_has_calls([
            call(7), call(1)
        ])
        self.assertEqual(self.mock_bot_handler.send_expiration_warning.call_count, 2)
    
    def test_send_expiration_warnings_with_errors(self):
        """Тест отправки предупреждений с ошибками"""
        # Arrange
        subscriptions_7_days = [self.test_subscription]
        
        self.mock_db_service.get_subscriptions_expiring_in_days.side_effect = [
            subscriptions_7_days,  # Для 7 дней
            []                     # Для 1 дня
        ]
        self.mock_db_service.get_user_by_id.return_value = self.test_user
        self.mock_bot_handler.send_expiration_warning.return_value = False  # Ошибка отправки
        
        # Act
        result = self.notification_service.send_expiration_warnings()
        
        # Assert
        expected_result = {
            'warnings_7_days': 0,
            'warnings_1_day': 0,
            'errors': 1
        }
        self.assertEqual(result, expected_result)
    
    def test_send_subscription_expired_notifications_success(self):
        """Тест успешной отправки уведомлений об истечении подписок"""
        # Arrange
        expired_subscriptions = [self.test_subscription]
        
        self.mock_db_service.get_expired_subscriptions.return_value = expired_subscriptions
        self.mock_db_service.get_user_by_id.return_value = self.test_user
        self.mock_bot_handler.send_subscription_expired_notification.return_value = True
        
        # Act
        result = self.notification_service.send_subscription_expired_notifications()
        
        # Assert
        expected_result = {
            'expired_notifications': 1,
            'errors': 0
        }
        self.assertEqual(result, expected_result)
        
        self.mock_bot_handler.send_subscription_expired_notification.assert_called_once_with(
            self.test_user.telegram_id
        )
    
    def test_send_bulk_notification_success(self):
        """Тест успешной массовой отправки уведомлений"""
        # Arrange
        user_ids = [123, 456, 789]
        message_text = "Тестовое массовое уведомление"
        mock_results = {123: True, 456: True, 789: False}
        
        self.mock_bot_handler.send_bulk_notification.return_value = mock_results
        
        # Act
        result = self.notification_service.send_bulk_notification(
            user_ids, message_text, "test"
        )
        
        # Assert
        expected_result = {
            'successful': 2,
            'failed': 1,
            'total': 3
        }
        self.assertEqual(result, expected_result)
        
        self.mock_bot_handler.send_bulk_notification.assert_called_once_with(
            user_ids, message_text
        )
    
    def test_send_admin_notification_success(self):
        """Тест успешной отправки уведомления администраторам"""
        # Arrange
        admin_user_ids = [111, 222]
        message_text = "Административное уведомление"
        
        with patch.object(self.notification_service, 'send_bulk_notification') as mock_bulk:
            mock_bulk.return_value = {'successful': 2, 'failed': 0, 'total': 2}
            
            # Act
            result = self.notification_service.send_admin_notification(
                admin_user_ids, message_text
            )
            
            # Assert
            self.assertTrue(result)
            mock_bulk.assert_called_once_with(admin_user_ids, message_text, 'admin')
    
    def test_send_admin_notification_failure(self):
        """Тест неудачной отправки уведомления администраторам"""
        # Arrange
        admin_user_ids = [111, 222]
        message_text = "Административное уведомление"
        
        with patch.object(self.notification_service, 'send_bulk_notification') as mock_bulk:
            mock_bulk.return_value = {'successful': 0, 'failed': 2, 'total': 2}
            
            # Act
            result = self.notification_service.send_admin_notification(
                admin_user_ids, message_text
            )
            
            # Assert
            self.assertFalse(result)
    
    def test_schedule_notification_check_success(self):
        """Тест успешной плановой проверки уведомлений"""
        # Arrange
        expiration_results = {'warnings_7_days': 2, 'warnings_1_day': 1, 'errors': 0}
        expired_results = {'expired_notifications': 3, 'errors': 0}
        
        with patch.object(self.notification_service, 'send_expiration_warnings') as mock_exp:
            with patch.object(self.notification_service, 'send_subscription_expired_notifications') as mock_expired:
                mock_exp.return_value = expiration_results
                mock_expired.return_value = expired_results
                
                # Act
                result = self.notification_service.schedule_notification_check()
                
                # Assert
                expected_result = {
                    'warnings_7_days': 2,
                    'warnings_1_day': 1,
                    'expired_notifications': 3,
                    'total_errors': 0
                }
                self.assertEqual(result, expected_result)
                
                mock_exp.assert_called_once()
                mock_expired.assert_called_once()
    
    def test_get_notification_stats_success(self):
        """Тест получения статистики уведомлений"""
        # Arrange
        self.mock_db_service.get_all_active_subscriptions.return_value = [1, 2, 3]
        self.mock_db_service.get_subscriptions_expiring_in_days.side_effect = [
            [1, 2],  # 7 дней
            [1]      # 1 день
        ]
        self.mock_db_service.get_expired_subscriptions.return_value = [1, 2, 3, 4]
        
        # Act
        result = self.notification_service.get_notification_stats()
        
        # Assert
        expected_result = {
            'active_subscriptions': 3,
            'expiring_in_7_days': 2,
            'expiring_in_1_day': 1,
            'expired_subscriptions': 4
        }
        self.assertEqual(result, expected_result)
    
    def test_notification_service_exception_handling(self):
        """Тест обработки исключений в сервисе уведомлений"""
        # Arrange
        self.mock_bot_handler.send_payment_link.side_effect = Exception("Test error")
        
        # Act
        result = self.notification_service.send_payment_link_notification(
            self.test_user_id, self.test_invoice_data, 1
        )
        
        # Assert
        self.assertFalse(result)


class TestBotHandlerNotificationMethods(unittest.TestCase):
    """Тесты для методов уведомлений в TelegramBotHandler"""
    
    def setUp(self):
        """Настройка тестов"""
        self.mock_db_service = Mock(spec=DatabaseService)
        self.mock_payment_service = Mock()
        
        # Создаем mock для бота
        with patch('app.services.bot_handler.telebot.TeleBot') as mock_bot_class:
            self.mock_bot = Mock()
            mock_bot_class.return_value = self.mock_bot
            
            from app.services.bot_handler import TelegramBotHandler
            self.bot_handler = TelegramBotHandler(
                self.mock_db_service, 
                self.mock_payment_service
            )
            self.bot_handler.bot = self.mock_bot
        
        self.test_user_id = 123456789
        self.test_invite_url = "https://t.me/+test_invite_link"
        self.test_subscription_end_date = datetime.now() + timedelta(days=30)
    
    def test_send_invite_link_success(self):
        """Тест успешной отправки пригласительной ссылки"""
        # Arrange
        self.mock_bot.send_message.return_value = True
        
        # Act
        result = self.bot_handler.send_invite_link(
            self.test_user_id, 
            self.test_invite_url, 
            self.test_subscription_end_date
        )
        
        # Assert
        self.assertTrue(result)
        self.mock_bot.send_message.assert_called_once()
        
        # Проверяем параметры вызова
        call_args = self.mock_bot.send_message.call_args
        self.assertEqual(call_args[0][0], self.test_user_id)  # user_id
        self.assertIn("Оплата прошла успешно!", call_args[0][1])  # text содержит нужный текст
        self.assertEqual(call_args[1]['parse_mode'], 'HTML')
    
    def test_send_invite_link_failure(self):
        """Тест неудачной отправки пригласительной ссылки"""
        # Arrange
        self.mock_bot.send_message.side_effect = Exception("Bot API error")
        
        # Act
        result = self.bot_handler.send_invite_link(
            self.test_user_id, 
            self.test_invite_url, 
            self.test_subscription_end_date
        )
        
        # Assert
        self.assertFalse(result)
    
    def test_send_expiration_warning_7_days(self):
        """Тест отправки предупреждения за 7 дней"""
        # Arrange
        self.mock_bot.send_message.return_value = True
        days_left = 7
        
        # Act
        result = self.bot_handler.send_expiration_warning(
            self.test_user_id, 
            days_left, 
            self.test_subscription_end_date
        )
        
        # Assert
        self.assertTrue(result)
        self.mock_bot.send_message.assert_called_once()
        
        # Проверяем содержимое сообщения
        call_args = self.mock_bot.send_message.call_args
        message_text = call_args[0][1]
        self.assertIn("Подписка истекает через неделю", message_text)
        self.assertIn("⚠️", message_text)
    
    def test_send_expiration_warning_1_day(self):
        """Тест отправки предупреждения за 1 день"""
        # Arrange
        self.mock_bot.send_message.return_value = True
        days_left = 1
        
        # Act
        result = self.bot_handler.send_expiration_warning(
            self.test_user_id, 
            days_left, 
            self.test_subscription_end_date
        )
        
        # Assert
        self.assertTrue(result)
        
        # Проверяем содержимое сообщения
        call_args = self.mock_bot.send_message.call_args
        message_text = call_args[0][1]
        self.assertIn("Подписка истекает завтра!", message_text)
        self.assertIn("🔴", message_text)
    
    def test_send_subscription_expired_notification(self):
        """Тест отправки уведомления об истечении подписки"""
        # Arrange
        self.mock_bot.send_message.return_value = True
        
        # Act
        result = self.bot_handler.send_subscription_expired_notification(self.test_user_id)
        
        # Assert
        self.assertTrue(result)
        self.mock_bot.send_message.assert_called_once()
        
        # Проверяем содержимое сообщения
        call_args = self.mock_bot.send_message.call_args
        message_text = call_args[0][1]
        self.assertIn("Ваша подписка истекла", message_text)
        self.assertIn("❌", message_text)
    
    def test_send_payment_confirmation(self):
        """Тест отправки подтверждения оплаты"""
        # Arrange
        self.mock_bot.send_message.return_value = True
        self.mock_payment_service.get_available_payment_methods.return_value = {
            'ru_card': {'name': 'Карта РФ'}
        }
        
        payment_amount = 500.0
        payment_method = 'ru_card'
        
        # Act
        result = self.bot_handler.send_payment_confirmation(
            self.test_user_id, 
            payment_amount, 
            payment_method
        )
        
        # Assert
        self.assertTrue(result)
        self.mock_bot.send_message.assert_called_once()
        
        # Проверяем содержимое сообщения
        call_args = self.mock_bot.send_message.call_args
        message_text = call_args[0][1]
        self.assertIn("Платеж успешно обработан!", message_text)
        self.assertIn("500.0 ₽", message_text)
        self.assertIn("Карта РФ", message_text)
    
    def test_send_bulk_notification(self):
        """Тест массовой отправки уведомлений"""
        # Arrange
        user_ids = [123, 456, 789]
        message_text = "Тестовое сообщение"
        
        # Настраиваем mock так, чтобы первые два вызова были успешными, третий - с ошибкой
        self.mock_bot.send_message.side_effect = [None, None, Exception("API Error")]
        
        # Act
        with patch('time.sleep'):  # Мокаем sleep для ускорения тестов
            result = self.bot_handler.send_bulk_notification(user_ids, message_text)
        
        # Assert
        expected_result = {123: True, 456: True, 789: False}
        self.assertEqual(result, expected_result)
        self.assertEqual(self.mock_bot.send_message.call_count, 3)


if __name__ == '__main__':
    unittest.main()