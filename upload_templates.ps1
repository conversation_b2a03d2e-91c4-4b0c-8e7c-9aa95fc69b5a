# PowerShell script to upload updated templates to server
$serverIP = "**************"
$password = "dkomqgTaijxro7in^bxd"

Write-Host "Uploading updated templates to server..."

# Upload templates directory
Write-Host "Uploading templates directory..."
pscp -pw $password -r templates ubuntu@${serverIP}:/home/<USER>/templates_new

# Connect to server and move templates
Write-Host "Moving templates to correct location..."
plink -ssh -pw $password -batch ubuntu@$serverIP "echo '$password' | sudo -S rm -rf /home/<USER>/app/templates && echo '$password' | sudo -S mv /home/<USER>/templates_new /home/<USER>/app/templates && echo '$password' | sudo -S chown -R telegrambot:telegrambot /home/<USER>/app/templates"

# Restart services
Write-Host "Restarting services..."
plink -ssh -pw $password -batch ubuntu@$serverIP "echo '$password' | sudo -S systemctl restart telegram-bot && echo '$password' | sudo -S systemctl restart nginx"

Write-Host "Templates updated successfully!"