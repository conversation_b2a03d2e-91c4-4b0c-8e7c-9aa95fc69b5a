{% extends "base.html" %}

{% block title %}Платеж #{{ payment.id }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-credit-card"></i> Платеж #{{ payment.id }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ safe_url_for('admin.payments') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Назад к платежам
        </a>
    </div>
</div>

<div class="row">
    <!-- Основная информация о платеже -->
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> Информация о платеже
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>ID платежа:</strong></td>
                                <td>#{{ payment.id }}</td>
                            </tr>
                            <tr>
                                <td><strong>Lava Invoice ID:</strong></td>
                                <td>
                                    <code>{{ payment.lava_invoice_id }}</code>
                                    <button class="btn btn-sm btn-outline-secondary ms-2" 
                                            onclick="copyToClipboard('{{ payment.lava_invoice_id }}')" 
                                            title="Копировать">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Сумма:</strong></td>
                                <td>
                                    <span class="h5 text-success">{{ "%.2f"|format(payment.amount) }} {{ payment.currency }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Способ оплаты:</strong></td>
                                <td>
                                    {% if payment.payment_method == 'card_ru' %}
                                        <span class="badge bg-primary">Карта РФ</span>
                                    {% elif payment.payment_method == 'card_foreign' %}
                                        <span class="badge bg-info">Карта иностранная</span>
                                    {% elif payment.payment_method == 'crypto' %}
                                        <span class="badge bg-warning">Криптовалюта</span>
                                    {% else %}
                                        {{ payment.payment_method }}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Статус:</strong></td>
                                <td>
                                    {% if payment.status == 'completed' %}
                                        <span class="badge bg-success fs-6">Завершен</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning fs-6">В ожидании</span>
                                    {% elif payment.status == 'failed' %}
                                        <span class="badge bg-danger fs-6">Неудачный</span>
                                    {% elif payment.status == 'expired' %}
                                        <span class="badge bg-secondary fs-6">Истек</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark fs-6">{{ payment.status }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Создан:</strong></td>
                                <td>{{ payment.created_at.strftime('%d.%m.%Y %H:%M:%S') if payment.created_at else '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Завершен:</strong></td>
                                <td>{{ payment.completed_at.strftime('%d.%m.%Y %H:%M:%S') if payment.completed_at else '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Истекает:</strong></td>
                                <td>{{ payment.expires_at.strftime('%d.%m.%Y %H:%M:%S') if payment.expires_at else '-' }}</td>
                            </tr>
                            {% if payment.payment_url %}
                            <tr>
                                <td><strong>Ссылка оплаты:</strong></td>
                                <td>
                                    <a href="{{ payment.payment_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-link-45deg"></i> Открыть
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                {% if payment.status == 'pending' %}
                <div class="alert alert-warning mt-3">
                    <h6><i class="bi bi-exclamation-triangle"></i> Платеж в ожидании</h6>
                    <p class="mb-2">Этот платеж можно обработать вручную:</p>
                    <form method="POST" action="{{ safe_url_for('admin.update_payment_status', payment_id=payment.id) }}" class="d-inline">
                        <div class="btn-group">
                            <button type="submit" name="status" value="completed" class="btn btn-success" 
                                    onclick="return confirm('Вы уверены, что хотите завершить этот платеж?')">
                                <i class="bi bi-check-circle"></i> Завершить
                            </button>
                            <button type="submit" name="status" value="failed" class="btn btn-danger"
                                    onclick="return confirm('Вы уверены, что хотите отклонить этот платеж?')">
                                <i class="bi bi-x-circle"></i> Отклонить
                            </button>
                            <button type="submit" name="status" value="expired" class="btn btn-warning"
                                    onclick="return confirm('Вы уверены, что хотите отменить этот платеж?')">
                                <i class="bi bi-clock"></i> Отменить
                            </button>
                        </div>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Информация о подписке -->
        {% if subscription %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-check"></i> Связанная подписка
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>ID подписки:</strong> #{{ subscription.id }}</p>
                        <p><strong>Тип плана:</strong> 
                            {% if subscription.plan_type == 'monthly' %}
                                <span class="badge bg-info">Месячный</span>
                            {% elif subscription.plan_type == 'quarterly' %}
                                <span class="badge bg-primary">Квартальный</span>
                            {% elif subscription.plan_type == 'yearly' %}
                                <span class="badge bg-success">Годовой</span>
                            {% else %}
                                {{ subscription.plan_type }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Начало:</strong> {{ subscription.start_date.strftime('%d.%m.%Y %H:%M') }}</p>
                        <p><strong>Окончание:</strong> {{ subscription.end_date.strftime('%d.%m.%Y %H:%M') }}</p>
                        <p><strong>Статус:</strong> 
                            {% if subscription.status == 'active' %}
                                <span class="badge bg-success">Активна</span>
                            {% elif subscription.status == 'expired' %}
                                <span class="badge bg-danger">Истекла</span>
                            {% elif subscription.status == 'cancelled' %}
                                <span class="badge bg-secondary">Отменена</span>
                            {% else %}
                                {{ subscription.status }}
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Информация о пользователе -->
    <div class="col-md-4">
        {% if user %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person"></i> Пользователь
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 60px; height: 60px; font-size: 24px;">
                        <i class="bi bi-person"></i>
                    </div>
                </div>
                
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td>#{{ user.id }}</td>
                    </tr>
                    <tr>
                        <td><strong>Telegram ID:</strong></td>
                        <td>{{ user.telegram_id }}</td>
                    </tr>
                    {% if user.username %}
                    <tr>
                        <td><strong>Username:</strong></td>
                        <td>@{{ user.username }}</td>
                    </tr>
                    {% endif %}
                    {% if user.first_name %}
                    <tr>
                        <td><strong>Имя:</strong></td>
                        <td>{{ user.first_name }}</td>
                    </tr>
                    {% endif %}
                    {% if user.last_name %}
                    <tr>
                        <td><strong>Фамилия:</strong></td>
                        <td>{{ user.last_name }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>Регистрация:</strong></td>
                        <td>{{ user.created_at.strftime('%d.%m.%Y') if user.created_at else '-' }}</td>
                    </tr>
                </table>

                <div class="d-grid">
                    <a href="{{ safe_url_for('admin.user_detail', user_id=user.id) }}" 
                       class="btn btn-outline-primary">
                        <i class="bi bi-eye"></i> Подробнее о пользователе
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Действия -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> Действия
                </h5>
            </div>
            <div class="card-body">
                {% if payment.status == 'pending' %}
                <form method="POST" action="{{ safe_url_for('admin.update_payment_status', payment_id=payment.id) }}">
                    <div class="d-grid gap-2">
                        <button type="submit" name="status" value="completed" class="btn btn-success" 
                                onclick="return confirm('Вы уверены, что хотите завершить этот платеж?')">
                            <i class="bi bi-check-circle"></i> Завершить платеж
                        </button>
                        <button type="submit" name="status" value="failed" class="btn btn-danger"
                                onclick="return confirm('Вы уверены, что хотите отклонить этот платеж?')">
                            <i class="bi bi-x-circle"></i> Отклонить платеж
                        </button>
                        <button type="submit" name="status" value="expired" class="btn btn-warning"
                                onclick="return confirm('Вы уверены, что хотите отменить этот платеж?')">
                            <i class="bi bi-clock"></i> Отменить платеж
                        </button>
                    </div>
                </form>
                {% else %}
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    <p class="mt-2">Платеж уже обработан</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Показываем уведомление об успешном копировании
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 1000);
    });
}
</script>
{% endblock %}