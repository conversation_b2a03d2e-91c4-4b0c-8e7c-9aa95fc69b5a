"""
Модуль для работы с базой данных SQLite
"""

import sqlite3
import logging
import functools
from typing import Optional, List, Dict, Any, Tuple
from contextlib import contextmanager
from datetime import datetime, timedelta
from dataclasses import dataclass
from decimal import Decimal
from config import Config

from app.utils.error_handler import (
    ErrorCategory, ErrorSeverity, with_error_handling, with_retry, 
    health_checker, recovery_manager
)
from app.utils.database_error_handler import (
    DatabaseErrorHandler, with_database_error_handling, 
    with_database_retry, with_database_transaction
)

logger = logging.getLogger(__name__)

@dataclass
class User:
    """Модель пользователя"""
    id: Optional[int]
    telegram_id: int
    username: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

@dataclass
class Subscription:
    """Модель подписки"""
    id: Optional[int]
    user_id: int
    plan_type: str  # 'monthly', 'quarterly', 'yearly'
    status: str  # 'active', 'expired', 'cancelled'
    start_date: datetime
    end_date: datetime
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    
    def is_active(self) -> bool:
        """Проверяет, активна ли подписка"""
        return self.status == 'active' and self.end_date > datetime.now()
    
    def days_until_expiry(self) -> int:
        """Возвращает количество дней до истечения подписки"""
        if not self.is_active():
            return 0
        delta = self.end_date - datetime.now()
        return max(0, delta.days)

@dataclass
class Payment:
    """Модель платежа"""
    id: Optional[int]
    user_id: int
    subscription_id: Optional[int]
    lava_invoice_id: str
    amount: Decimal
    currency: str
    payment_method: str  # 'card_ru', 'card_foreign', 'crypto'
    status: str  # 'pending', 'completed', 'failed', 'expired'
    payment_url: Optional[str]
    created_at: Optional[datetime]
    completed_at: Optional[datetime]
    expires_at: Optional[datetime]

@dataclass
class AdminLog:
    """Модель лога администратора"""
    id: Optional[int]
    admin_telegram_id: int
    action: str
    target_user_id: Optional[int]
    details: Optional[str]
    created_at: Optional[datetime]

@dataclass
class SystemSetting:
    """Модель системной настройки"""
    id: Optional[int]
    category: str
    key: str
    value: str
    description: Optional[str]
    data_type: str  # 'string', 'integer', 'float', 'boolean', 'json'
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

@dataclass
class SubscriptionPlan:
    """Модель тарифного плана"""
    id: Optional[int]
    name: str
    duration_months: int
    price: Decimal
    currency: str
    description: Optional[str]
    is_active: bool
    sort_order: int
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

@dataclass
class BotText:
    """Модель текста бота"""
    id: Optional[int]
    key: str
    text: str
    description: Optional[str]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

class DatabaseManager:
    """Менеджер базы данных для работы с SQLite"""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or Config.DATABASE_URL.replace('sqlite:///', '')
        self.error_handler = DatabaseErrorHandler(self.db_path)
        
        # Регистрируем проверку здоровья БД
        health_checker.register_check(
            'database',
            self.check_database_health,
            interval=60
        )
        
    @contextmanager
    def get_connection(self):
        """Контекстный менеджер для работы с соединением БД"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Для доступа к колонкам по имени
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Ошибка работы с БД: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def init_database(self):
        """Инициализация базы данных с созданием всех таблиц"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Создание таблицы пользователей
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        telegram_id INTEGER UNIQUE NOT NULL,
                        username TEXT,
                        first_name TEXT,
                        last_name TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Создание таблицы подписок
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS subscriptions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        plan_type TEXT NOT NULL CHECK(plan_type IN ('monthly', 'quarterly', 'yearly')),
                        status TEXT NOT NULL CHECK(status IN ('active', 'expired', 'cancelled')) DEFAULT 'active',
                        start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        end_date TIMESTAMP NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                    )
                ''')
                
                # Создание таблицы платежей
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        subscription_id INTEGER,
                        lava_invoice_id TEXT UNIQUE NOT NULL,
                        amount DECIMAL(10,2) NOT NULL,
                        currency TEXT DEFAULT 'RUB',
                        payment_method TEXT CHECK(payment_method IN ('card_ru', 'card_foreign', 'crypto')),
                        status TEXT NOT NULL CHECK(status IN ('pending', 'completed', 'failed', 'expired')) DEFAULT 'pending',
                        payment_url TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        completed_at TIMESTAMP,
                        expires_at TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                        FOREIGN KEY (subscription_id) REFERENCES subscriptions (id) ON DELETE SET NULL
                    )
                ''')
                
                # Создание таблицы логов администратора
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS admin_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        admin_telegram_id INTEGER NOT NULL,
                        action TEXT NOT NULL,
                        target_user_id INTEGER,
                        details TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (target_user_id) REFERENCES users (id) ON DELETE SET NULL
                    )
                ''')
                
                # Создание таблицы системных настроек
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        category TEXT NOT NULL,
                        key TEXT NOT NULL,
                        value TEXT NOT NULL,
                        description TEXT,
                        data_type TEXT DEFAULT 'string' CHECK(data_type IN ('string', 'integer', 'float', 'boolean', 'json')),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(category, key)
                    )
                ''')
                
                # Создание таблицы тарифных планов
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS subscription_plans (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        duration_months INTEGER NOT NULL,
                        price DECIMAL(10,2) NOT NULL,
                        currency TEXT DEFAULT 'RUB',
                        description TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        sort_order INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Создание таблицы текстов бота
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bot_texts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key TEXT UNIQUE NOT NULL,
                        text TEXT NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Создание индексов для оптимизации запросов
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users (telegram_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions (user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions (status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date ON subscriptions (end_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments (user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_payments_status ON payments (status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_payments_lava_invoice_id ON payments (lava_invoice_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_admin_logs_admin_id ON admin_logs (admin_telegram_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings (category)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings (key)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscription_plans_active ON subscription_plans (is_active)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscription_plans_sort ON subscription_plans (sort_order)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_bot_texts_key ON bot_texts (key)')
                
                conn.commit()
                logger.info("База данных успешно инициализирована")
                
                # Инициализируем настройки по умолчанию
                db_service = DatabaseService(self)
                db_service.initialize_default_settings()
                
        except Exception as e:
            logger.error(f"Ошибка инициализации базы данных: {e}")
            raise
    
    def migrate_database(self):
        """Выполнение миграций базы данных"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Проверяем версию схемы (можно добавить таблицу migrations в будущем)
                # Пока просто проверяем существование таблиц
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name IN ('users', 'subscriptions', 'payments', 'admin_logs')
                """)
                
                existing_tables = [row[0] for row in cursor.fetchall()]
                
                if len(existing_tables) < 4:
                    logger.info("Обнаружены отсутствующие таблицы, выполняем инициализацию")
                    self.init_database()
                else:
                    logger.info("Все таблицы существуют, миграция не требуется")
                    
        except Exception as e:
            logger.error(f"Ошибка миграции базы данных: {e}")
            raise
    
    def check_database_health(self) -> bool:
        """Проверка состояния базы данных"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"Проблема с базой данных: {e}")
            return False


class DatabaseService:
    """Сервис для работы с базой данных с CRUD операциями"""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        self.db_manager = db_manager or DatabaseManager()
    
    def init_database(self):
        """Инициализация базы данных"""
        return self.db_manager.init_database()
    
    def migrate_database(self):
        """Миграция базы данных"""
        return self.db_manager.migrate_database()
    
    # ==================== ПОЛЬЗОВАТЕЛИ ====================
    
    @with_database_error_handling(severity=ErrorSeverity.MEDIUM)
    @with_database_retry(max_retries=2)
    def create_user(self, telegram_id: int, username: Optional[str] = None, 
                   first_name: Optional[str] = None, last_name: Optional[str] = None) -> User:
        """Создать нового пользователя"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO users (telegram_id, username, first_name, last_name)
                    VALUES (?, ?, ?, ?)
                ''', (telegram_id, username, first_name, last_name))
                
                user_id = cursor.lastrowid
                conn.commit()
                
                logger.info(f"Создан пользователь с ID {user_id}, Telegram ID {telegram_id}")
                return self.get_user_by_id(user_id)
                
        except sqlite3.IntegrityError:
            # Пользователь уже существует, возвращаем существующего
            logger.info(f"Пользователь с Telegram ID {telegram_id} уже существует")
            return self.get_user_by_telegram_id(telegram_id)
        except Exception as e:
            logger.error(f"Ошибка создания пользователя: {e}")
            raise
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Получить пользователя по ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
                row = cursor.fetchone()
                
                if row:
                    return User(
                        id=row['id'],
                        telegram_id=row['telegram_id'],
                        username=row['username'],
                        first_name=row['first_name'],
                        last_name=row['last_name'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения пользователя по ID {user_id}: {e}")
            raise
    
    def get_user_by_telegram_id(self, telegram_id: int) -> Optional[User]:
        """Получить пользователя по Telegram ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM users WHERE telegram_id = ?', (telegram_id,))
                row = cursor.fetchone()
                
                if row:
                    return User(
                        id=row['id'],
                        telegram_id=row['telegram_id'],
                        username=row['username'],
                        first_name=row['first_name'],
                        last_name=row['last_name'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения пользователя по Telegram ID {telegram_id}: {e}")
            raise
    
    def update_user(self, user_id: int, username: Optional[str] = None,
                   first_name: Optional[str] = None, last_name: Optional[str] = None) -> bool:
        """Обновить данные пользователя"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE users 
                    SET username = COALESCE(?, username),
                        first_name = COALESCE(?, first_name),
                        last_name = COALESCE(?, last_name),
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (username, first_name, last_name, user_id))
                
                conn.commit()
                updated = cursor.rowcount > 0
                
                if updated:
                    logger.info(f"Обновлен пользователь с ID {user_id}")
                
                return updated
                
        except Exception as e:
            logger.error(f"Ошибка обновления пользователя {user_id}: {e}")
            raise
    
    def get_all_users(self, limit: Optional[int] = None, offset: int = 0) -> List[User]:
        """Получить всех пользователей с пагинацией"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                query = 'SELECT * FROM users ORDER BY created_at DESC'
                params = []
                
                if limit:
                    query += ' LIMIT ? OFFSET ?'
                    params.extend([limit, offset])
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                users = []
                for row in rows:
                    users.append(User(
                        id=row['id'],
                        telegram_id=row['telegram_id'],
                        username=row['username'],
                        first_name=row['first_name'],
                        last_name=row['last_name'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return users
                
        except Exception as e:
            logger.error(f"Ошибка получения списка пользователей: {e}")
            raise

    def get_users_with_filters(self, search: str = '', status_filter: str = '', 
                              sort_by: str = 'created_desc', limit: int = 20, 
                              offset: int = 0) -> Tuple[List[Dict], int]:
        """Получить пользователей с фильтрацией и поиском"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Базовый запрос с JOIN для получения информации о подписках
                base_query = '''
                    SELECT DISTINCT u.*, 
                           s.id as subscription_id,
                           s.status as subscription_status,
                           s.end_date as subscription_end_date,
                           s.plan_type as subscription_plan_type
                    FROM users u
                    LEFT JOIN subscriptions s ON u.id = s.user_id 
                        AND s.status = 'active' 
                        AND s.end_date > CURRENT_TIMESTAMP
                '''
                
                where_conditions = []
                params = []
                
                # Поиск по имени, username или telegram_id
                if search:
                    where_conditions.append('''
                        (u.first_name LIKE ? OR u.last_name LIKE ? OR 
                         u.username LIKE ? OR CAST(u.telegram_id AS TEXT) LIKE ?)
                    ''')
                    search_param = f'%{search}%'
                    params.extend([search_param, search_param, search_param, search_param])
                
                # Фильтр по статусу подписки
                if status_filter == 'active':
                    where_conditions.append('s.id IS NOT NULL')
                elif status_filter == 'expired':
                    # Пользователи с истекшими подписками
                    base_query = '''
                        SELECT DISTINCT u.*, 
                               s.id as subscription_id,
                               s.status as subscription_status,
                               s.end_date as subscription_end_date,
                               s.plan_type as subscription_plan_type
                        FROM users u
                        LEFT JOIN subscriptions s ON u.id = s.user_id
                        LEFT JOIN (
                            SELECT user_id, MAX(end_date) as last_end_date
                            FROM subscriptions 
                            GROUP BY user_id
                        ) ls ON u.id = ls.user_id
                    '''
                    where_conditions.append('ls.last_end_date < CURRENT_TIMESTAMP')
                elif status_filter == 'no_subscription':
                    where_conditions.append('s.id IS NULL')
                
                # Добавляем условия WHERE
                if where_conditions:
                    base_query += ' WHERE ' + ' AND '.join(where_conditions)
                
                # Сортировка
                if sort_by == 'created_asc':
                    base_query += ' ORDER BY u.created_at ASC'
                elif sort_by == 'name_asc':
                    base_query += ' ORDER BY u.first_name ASC, u.last_name ASC'
                elif sort_by == 'name_desc':
                    base_query += ' ORDER BY u.first_name DESC, u.last_name DESC'
                else:  # created_desc по умолчанию
                    base_query += ' ORDER BY u.created_at DESC'
                
                # Получаем общее количество записей
                count_query = base_query.replace('SELECT DISTINCT u.*,', 'SELECT COUNT(DISTINCT u.id) as count,')
                count_query = count_query.split(' ORDER BY')[0]  # Убираем ORDER BY для подсчета
                
                cursor.execute(count_query, params)
                total_count = cursor.fetchone()[0]
                
                # Добавляем пагинацию
                base_query += ' LIMIT ? OFFSET ?'
                params.extend([limit, offset])
                
                cursor.execute(base_query, params)
                rows = cursor.fetchall()
                
                users_data = []
                for row in rows:
                    user = User(
                        id=row['id'],
                        telegram_id=row['telegram_id'],
                        username=row['username'],
                        first_name=row['first_name'],
                        last_name=row['last_name'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                    
                    # Информация о подписке
                    subscription = None
                    if row['subscription_id']:
                        subscription = Subscription(
                            id=row['subscription_id'],
                            user_id=row['id'],
                            plan_type=row['subscription_plan_type'],
                            status=row['subscription_status'],
                            start_date=datetime.now(),  # Заглушка
                            end_date=datetime.fromisoformat(row['subscription_end_date']),
                            created_at=None,
                            updated_at=None
                        )
                    
                    users_data.append({
                        'user': user,
                        'subscription': subscription
                    })
                
                return users_data, total_count
                
        except Exception as e:
            logger.error(f"Ошибка получения пользователей с фильтрами: {e}")
            raise

    def admin_grant_subscription(self, user_id: int, months: int) -> bool:
        """Административная выдача подписки пользователю"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Проверяем, есть ли активная подписка
                active_subscription = self.get_user_active_subscription(user_id)
                
                if active_subscription:
                    # Продлеваем существующую подписку
                    cursor.execute('''
                        UPDATE subscriptions 
                        SET end_date = datetime(end_date, '+{} months'),
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    '''.format(months), (active_subscription.id,))
                else:
                    # Создаем новую подписку
                    start_date = datetime.now()
                    end_date = start_date + timedelta(days=months * 30)
                    
                    # Определяем тип плана на основе количества месяцев
                    if months == 1:
                        plan_type = 'monthly'
                    elif months <= 6:
                        plan_type = 'quarterly'
                    else:
                        plan_type = 'yearly'
                    
                    cursor.execute('''
                        INSERT INTO subscriptions (user_id, plan_type, start_date, end_date, status)
                        VALUES (?, ?, ?, ?, 'active')
                    ''', (user_id, plan_type, start_date.isoformat(), end_date.isoformat()))
                
                conn.commit()
                logger.info(f"Администратор выдал подписку на {months} месяцев пользователю {user_id}")
                return True
                
        except Exception as e:
            logger.error(f"Ошибка административной выдачи подписки: {e}")
            return False

    def admin_revoke_subscription(self, user_id: int) -> bool:
        """Административный отзыв подписки пользователя"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Отзываем все активные подписки пользователя
                cursor.execute('''
                    UPDATE subscriptions 
                    SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = ? AND status = 'active'
                ''', (user_id,))
                
                conn.commit()
                revoked = cursor.rowcount > 0
                
                if revoked:
                    logger.info(f"Администратор отозвал подписку у пользователя {user_id}")
                
                return revoked
                
        except Exception as e:
            logger.error(f"Ошибка административного отзыва подписки: {e}")
            return False

    def log_admin_action(self, admin_telegram_id: int, action: str, 
                        target_user_id: Optional[int] = None, details: Optional[str] = None) -> bool:
        """Логирование действий администратора"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO admin_logs (admin_telegram_id, action, target_user_id, details)
                    VALUES (?, ?, ?, ?)
                ''', (admin_telegram_id, action, target_user_id, details))
                
                conn.commit()
                logger.info(f"Залогировано действие администратора: {action}")
                return True
                
        except Exception as e:
            logger.error(f"Ошибка логирования действия администратора: {e}")
            return False

    def get_admin_logs(self, limit: int = 100, offset: int = 0) -> List[AdminLog]:
        """Получить логи административных действий"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT al.*, u.first_name, u.last_name, u.username
                    FROM admin_logs al
                    LEFT JOIN users u ON al.target_user_id = u.id
                    ORDER BY al.created_at DESC
                    LIMIT ? OFFSET ?
                ''', (limit, offset))
                rows = cursor.fetchall()
                
                logs = []
                for row in rows:
                    log = AdminLog(
                        id=row['id'],
                        admin_telegram_id=row['admin_telegram_id'],
                        action=row['action'],
                        target_user_id=row['target_user_id'],
                        details=row['details'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
                    )
                    # Добавляем информацию о целевом пользователе
                    if row['target_user_id']:
                        log.target_user_name = f"{row['first_name'] or ''} {row['last_name'] or ''}".strip()
                        if not log.target_user_name and row['username']:
                            log.target_user_name = f"@{row['username']}"
                        if not log.target_user_name:
                            log.target_user_name = f"User {row['target_user_id']}"
                    else:
                        log.target_user_name = None
                    
                    logs.append(log)
                
                return logs
                
        except Exception as e:
            logger.error(f"Ошибка получения логов администратора: {e}")
            return []

    def get_revenue_report(self, period_days: int = 30) -> Dict[str, Any]:
        """Получить отчет по доходам за указанный период"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Общий доход за период
                cursor.execute('''
                    SELECT 
                        COALESCE(SUM(amount), 0) as total_revenue,
                        COUNT(*) as total_payments
                    FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-{} days')
                '''.format(period_days))
                
                row = cursor.fetchone()
                total_revenue = Decimal(str(row['total_revenue']))
                total_payments = row['total_payments']
                
                # Доходы по способам оплаты
                cursor.execute('''
                    SELECT 
                        payment_method,
                        COALESCE(SUM(amount), 0) as revenue,
                        COUNT(*) as count
                    FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-{} days')
                    GROUP BY payment_method
                '''.format(period_days))
                
                payment_methods = []
                for row in cursor.fetchall():
                    payment_methods.append({
                        'method': row['payment_method'],
                        'revenue': Decimal(str(row['revenue'])),
                        'count': row['count']
                    })
                
                # Средний чек
                avg_payment = total_revenue / total_payments if total_payments > 0 else Decimal('0')
                
                # Доходы по дням (для графика)
                cursor.execute('''
                    SELECT 
                        DATE(completed_at) as date,
                        COALESCE(SUM(amount), 0) as daily_revenue,
                        COUNT(*) as daily_count
                    FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-{} days')
                    GROUP BY DATE(completed_at)
                    ORDER BY date
                '''.format(period_days))
                
                daily_data = []
                for row in cursor.fetchall():
                    daily_data.append({
                        'date': row['date'],
                        'revenue': Decimal(str(row['daily_revenue'])),
                        'count': row['daily_count']
                    })
                
                return {
                    'period_days': period_days,
                    'total_revenue': total_revenue,
                    'total_payments': total_payments,
                    'avg_payment': avg_payment,
                    'payment_methods': payment_methods,
                    'daily_data': daily_data
                }
                
        except Exception as e:
            logger.error(f"Ошибка получения отчета по доходам: {e}")
            return {
                'period_days': period_days,
                'total_revenue': Decimal('0'),
                'total_payments': 0,
                'avg_payment': Decimal('0'),
                'payment_methods': [],
                'daily_data': []
            }

    def get_subscription_statistics(self) -> Dict[str, Any]:
        """Получить статистику по подпискам"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Активные подписки
                cursor.execute('''
                    SELECT COUNT(*) as count
                    FROM subscriptions 
                    WHERE status = 'active' AND end_date > CURRENT_TIMESTAMP
                ''')
                active_subscriptions = cursor.fetchone()['count']
                
                # Истекшие подписки
                cursor.execute('''
                    SELECT COUNT(*) as count
                    FROM subscriptions 
                    WHERE status = 'active' AND end_date <= CURRENT_TIMESTAMP
                ''')
                expired_subscriptions = cursor.fetchone()['count']
                
                # Отмененные подписки
                cursor.execute('''
                    SELECT COUNT(*) as count
                    FROM subscriptions 
                    WHERE status = 'cancelled'
                ''')
                cancelled_subscriptions = cursor.fetchone()['count']
                
                # Подписки по типам планов
                cursor.execute('''
                    SELECT 
                        plan_type,
                        COUNT(*) as count
                    FROM subscriptions 
                    WHERE status = 'active' AND end_date > CURRENT_TIMESTAMP
                    GROUP BY plan_type
                ''')
                
                plan_types = {}
                for row in cursor.fetchall():
                    plan_types[row['plan_type']] = row['count']
                
                # Новые подписки за последние 30 дней
                cursor.execute('''
                    SELECT COUNT(*) as count
                    FROM subscriptions 
                    WHERE created_at >= datetime('now', '-30 days')
                ''')
                new_subscriptions_30d = cursor.fetchone()['count']
                
                return {
                    'active': active_subscriptions,
                    'expired': expired_subscriptions,
                    'cancelled': cancelled_subscriptions,
                    'plan_types': plan_types,
                    'new_30d': new_subscriptions_30d,
                    'total': active_subscriptions + expired_subscriptions + cancelled_subscriptions
                }
                
        except Exception as e:
            logger.error(f"Ошибка получения статистики подписок: {e}")
            return {
                'active': 0,
                'expired': 0,
                'cancelled': 0,
                'plan_types': {},
                'new_30d': 0,
                'total': 0
            }
    
    # ==================== ПОДПИСКИ ====================
    
    def create_subscription(self, user_id: int, plan_type: str, months: int) -> Subscription:
        """Создать новую подписку"""
        try:
            start_date = datetime.now()
            end_date = start_date + timedelta(days=months * 30)  # Приблизительно
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO subscriptions (user_id, plan_type, start_date, end_date)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, plan_type, start_date.isoformat(), end_date.isoformat()))
                
                subscription_id = cursor.lastrowid
                conn.commit()
                
                logger.info(f"Создана подписка с ID {subscription_id} для пользователя {user_id}")
                return self.get_subscription_by_id(subscription_id)
                
        except Exception as e:
            logger.error(f"Ошибка создания подписки: {e}")
            raise
    
    def get_subscription_by_id(self, subscription_id: int) -> Optional[Subscription]:
        """Получить подписку по ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM subscriptions WHERE id = ?', (subscription_id,))
                row = cursor.fetchone()
                
                if row:
                    return Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения подписки по ID {subscription_id}: {e}")
            raise
    
    def get_user_active_subscription(self, user_id: int) -> Optional[Subscription]:
        """Получить активную подписку пользователя"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE user_id = ? AND status = 'active' AND end_date > CURRENT_TIMESTAMP
                    ORDER BY end_date DESC LIMIT 1
                ''', (user_id,))
                row = cursor.fetchone()
                
                if row:
                    return Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения активной подписки пользователя {user_id}: {e}")
            raise
    
    def get_user_subscriptions(self, user_id: int) -> List[Subscription]:
        """Получить все подписки пользователя"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE user_id = ? 
                    ORDER BY created_at DESC
                ''', (user_id,))
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения подписок пользователя {user_id}: {e}")
            raise
    
    def update_subscription_status(self, subscription_id: int, status: str) -> bool:
        """Обновить статус подписки"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE subscriptions 
                    SET status = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, subscription_id))
                
                conn.commit()
                updated = cursor.rowcount > 0
                
                if updated:
                    logger.info(f"Обновлен статус подписки {subscription_id} на {status}")
                
                return updated
                
        except Exception as e:
            logger.error(f"Ошибка обновления статуса подписки {subscription_id}: {e}")
            raise
    
    def extend_subscription(self, subscription_id: int, additional_months: int) -> bool:
        """Продлить подписку на дополнительные месяцы"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE subscriptions 
                    SET end_date = datetime(end_date, '+{} months'),
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                '''.format(additional_months), (subscription_id,))
                
                conn.commit()
                updated = cursor.rowcount > 0
                
                if updated:
                    logger.info(f"Продлена подписка {subscription_id} на {additional_months} месяцев")
                
                return updated
                
        except Exception as e:
            logger.error(f"Ошибка продления подписки {subscription_id}: {e}")
            raise
    
    def get_expiring_subscriptions(self, days_ahead: int = 7) -> List[Subscription]:
        """Получить подписки, истекающие в ближайшие дни"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND end_date BETWEEN CURRENT_TIMESTAMP 
                    AND datetime(CURRENT_TIMESTAMP, '+{} days')
                    ORDER BY end_date ASC
                '''.format(days_ahead))
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения истекающих подписок: {e}")
            raise
    
    # ==================== ПЛАТЕЖИ ====================
    
    def create_payment(self, user_id: int, lava_invoice_id: str, amount: Decimal,
                      payment_method: str, payment_url: Optional[str] = None,
                      expires_at: Optional[datetime] = None) -> Payment:
        """Создать новый платеж"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO payments (user_id, lava_invoice_id, amount, payment_method, 
                                        payment_url, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, lava_invoice_id, float(amount), payment_method, 
                     payment_url, expires_at.isoformat() if expires_at else None))
                
                payment_id = cursor.lastrowid
                conn.commit()
                
                logger.info(f"Создан платеж с ID {payment_id} для пользователя {user_id}")
                return self.get_payment_by_id(payment_id)
                
        except Exception as e:
            logger.error(f"Ошибка создания платежа: {e}")
            raise
    
    def get_payment_by_id(self, payment_id: int) -> Optional[Payment]:
        """Получить платеж по ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM payments WHERE id = ?', (payment_id,))
                row = cursor.fetchone()
                
                if row:
                    return Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения платежа по ID {payment_id}: {e}")
            raise
    
    def get_payment_by_lava_id(self, lava_invoice_id: str) -> Optional[Payment]:
        """Получить платеж по Lava invoice ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM payments WHERE lava_invoice_id = ?', (lava_invoice_id,))
                row = cursor.fetchone()
                
                if row:
                    return Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения платежа по Lava ID {lava_invoice_id}: {e}")
            raise
    
    def update_payment_status(self, payment_id: int, status: str, 
                             subscription_id: Optional[int] = None) -> bool:
        """Обновить статус платежа"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                if status == 'completed':
                    cursor.execute('''
                        UPDATE payments 
                        SET status = ?, subscription_id = ?, completed_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (status, subscription_id, payment_id))
                else:
                    cursor.execute('''
                        UPDATE payments 
                        SET status = ?, subscription_id = ?
                        WHERE id = ?
                    ''', (status, subscription_id, payment_id))
                
                conn.commit()
                updated = cursor.rowcount > 0
                
                if updated:
                    logger.info(f"Обновлен статус платежа {payment_id} на {status}")
                
                return updated
                
        except Exception as e:
            logger.error(f"Ошибка обновления статуса платежа {payment_id}: {e}")
            raise
    
    def get_payment_by_order_id(self, order_id: str) -> Optional[Payment]:
        """Получить платеж по order_id"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM payments WHERE lava_invoice_id = ?', (order_id,))
                row = cursor.fetchone()
                
                if row:
                    return Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения платежа по order_id {order_id}: {e}")
            raise

    def update_payment_status_by_order_id(self, order_id: str, status: str, completed_at: Optional[datetime] = None, error_message: Optional[str] = None) -> bool:
        """Обновить статус платежа по order_id"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                if status == 'completed' and completed_at:
                    cursor.execute('''
                        UPDATE payments 
                        SET status = ?, completed_at = ?
                        WHERE lava_invoice_id = ?
                    ''', (status, completed_at.isoformat(), order_id))
                else:
                    cursor.execute('''
                        UPDATE payments 
                        SET status = ?
                        WHERE lava_invoice_id = ?
                    ''', (status, order_id))
                
                conn.commit()
                updated = cursor.rowcount > 0
                
                if updated:
                    logger.info(f"Обновлен статус платежа {order_id} на {status}")
                
                return updated
                
        except Exception as e:
            logger.error(f"Ошибка обновления статуса платежа {order_id}: {e}")
            raise

    def create_or_extend_subscription(self, user_id: int, plan_months: int) -> bool:
        """Создать новую подписку или продлить существующую"""
        try:
            # Проверяем, есть ли активная подписка
            active_subscription = self.get_user_active_subscription(user_id)
            
            if active_subscription:
                # Продлеваем существующую подписку
                return self.extend_subscription(active_subscription.id, plan_months)
            else:
                # Создаем новую подписку
                plan_type_map = {1: 'monthly', 3: 'quarterly', 6: 'quarterly', 12: 'yearly'}
                plan_type = plan_type_map.get(plan_months, 'monthly')
                
                subscription = self.create_subscription(user_id, plan_type, plan_months)
                return subscription is not None
                
        except Exception as e:
            logger.error(f"Ошибка создания/продления подписки для пользователя {user_id}: {e}")
            return False

    def check_connection(self) -> bool:
        """Проверить соединение с базой данных"""
        return self.db_manager.check_database_health()

    def get_user_payments(self, user_id: int) -> List[Payment]:
        """Получить все платежи пользователя"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM payments 
                    WHERE user_id = ? 
                    ORDER BY created_at DESC
                ''', (user_id,))
                rows = cursor.fetchall()
                
                payments = []
                for row in rows:
                    payments.append(Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    ))
                
                return payments
                
        except Exception as e:
            logger.error(f"Ошибка получения платежей пользователя {user_id}: {e}")
            raise
    
    def get_expired_payments(self) -> List[Payment]:
        """Получить истекшие платежи"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM payments 
                    WHERE status = 'pending' AND expires_at < CURRENT_TIMESTAMP
                    ORDER BY expires_at ASC
                ''')
                rows = cursor.fetchall()
                
                payments = []
                for row in rows:
                    payments.append(Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    ))
                
                return payments
                
        except Exception as e:
            logger.error(f"Ошибка получения истекших платежей: {e}")
            raise
    
    # ==================== ЛОГИ АДМИНИСТРАТОРА ====================
    
    def create_admin_log(self, admin_telegram_id: int, action: str, 
                        target_user_id: Optional[int] = None, details: Optional[str] = None) -> AdminLog:
        """Создать лог административного действия"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO admin_logs (admin_telegram_id, action, target_user_id, details)
                    VALUES (?, ?, ?, ?)
                ''', (admin_telegram_id, action, target_user_id, details))
                
                log_id = cursor.lastrowid
                conn.commit()
                
                logger.info(f"Создан лог администратора с ID {log_id}")
                return self.get_admin_log_by_id(log_id)
                
        except Exception as e:
            logger.error(f"Ошибка создания лога администратора: {e}")
            raise
    
    def get_admin_log_by_id(self, log_id: int) -> Optional[AdminLog]:
        """Получить лог администратора по ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM admin_logs WHERE id = ?', (log_id,))
                row = cursor.fetchone()
                
                if row:
                    return AdminLog(
                        id=row['id'],
                        admin_telegram_id=row['admin_telegram_id'],
                        action=row['action'],
                        target_user_id=row['target_user_id'],
                        details=row['details'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения лога администратора по ID {log_id}: {e}")
            raise
    
    def get_admin_logs(self, admin_telegram_id: Optional[int] = None, 
                      limit: Optional[int] = None, offset: int = 0) -> List[AdminLog]:
        """Получить логи администратора с фильтрацией и пагинацией"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                query = 'SELECT * FROM admin_logs'
                params = []
                
                if admin_telegram_id:
                    query += ' WHERE admin_telegram_id = ?'
                    params.append(admin_telegram_id)
                
                query += ' ORDER BY created_at DESC'
                
                if limit:
                    query += ' LIMIT ? OFFSET ?'
                    params.extend([limit, offset])
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                logs = []
                for row in rows:
                    logs.append(AdminLog(
                        id=row['id'],
                        admin_telegram_id=row['admin_telegram_id'],
                        action=row['action'],
                        target_user_id=row['target_user_id'],
                        details=row['details'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
                    ))
                
                return logs
                
        except Exception as e:
            logger.error(f"Ошибка получения логов администратора: {e}")
            raise
    
    # ==================== СТАТИСТИКА И АНАЛИТИКА ====================
    
    def get_statistics(self) -> Dict[str, Any]:
        """Получить общую статистику системы"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Общее количество пользователей
                cursor.execute('SELECT COUNT(*) FROM users')
                total_users = cursor.fetchone()[0]
                
                # Активные подписки
                cursor.execute('''
                    SELECT COUNT(*) FROM subscriptions 
                    WHERE status = 'active' AND end_date > CURRENT_TIMESTAMP
                ''')
                active_subscriptions = cursor.fetchone()[0]
                
                # Общий доход
                cursor.execute('''
                    SELECT COALESCE(SUM(amount), 0) FROM payments 
                    WHERE status = 'completed'
                ''')
                total_revenue = cursor.fetchone()[0]
                
                # Платежи за последний месяц
                cursor.execute('''
                    SELECT COUNT(*) FROM payments 
                    WHERE status = 'completed' 
                    AND created_at > datetime('now', '-1 month')
                ''')
                monthly_payments = cursor.fetchone()[0]
                
                # Истекающие подписки (7 дней)
                cursor.execute('''
                    SELECT COUNT(*) FROM subscriptions 
                    WHERE status = 'active' 
                    AND end_date BETWEEN CURRENT_TIMESTAMP 
                    AND datetime(CURRENT_TIMESTAMP, '+7 days')
                ''')
                expiring_subscriptions = cursor.fetchone()[0]
                
                return {
                    'total_users': total_users,
                    'active_subscriptions': active_subscriptions,
                    'total_revenue': float(total_revenue),
                    'monthly_payments': monthly_payments,
                    'expiring_subscriptions': expiring_subscriptions
                }
                
        except Exception as e:
            logger.error(f"Ошибка получения статистики: {e}")
            raise
    
    # ==================== МЕТОДЫ ДЛЯ УВЕДОМЛЕНИЙ ====================
    
    def get_subscriptions_expiring_in_days(self, days: int) -> List[Subscription]:
        """
        Получить подписки, которые истекают ровно через указанное количество дней
        
        Args:
            days: Количество дней до истечения
            
        Returns:
            Список подписок, истекающих через указанное количество дней
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND date(end_date) = date('now', '+{} days')
                    ORDER BY end_date ASC
                '''.format(days))
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения подписок, истекающих через {days} дней: {e}")
            raise
    
    def get_expired_subscriptions(self) -> List[Subscription]:
        """
        Получить истекшие подписки
        
        Returns:
            Список истекших подписок
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND end_date < CURRENT_TIMESTAMP
                    ORDER BY end_date DESC
                ''')
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения истекших подписок: {e}")
            raise
    
    def get_all_active_subscriptions(self) -> List[Subscription]:
        """
        Получить все активные подписки
        
        Returns:
            Список всех активных подписок
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND end_date > CURRENT_TIMESTAMP
                    ORDER BY end_date ASC
                ''')
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения всех активных подписок: {e}")
            raise    

    # ==================== СТАТИСТИКА СИСТЕМЫ ====================
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Получить общую статистику системы"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                stats = {}
                
                # Статистика пользователей
                cursor.execute('SELECT COUNT(*) FROM users')
                stats['total_users'] = cursor.fetchone()[0]
                
                # Статистика подписок
                cursor.execute("SELECT COUNT(*) FROM subscriptions WHERE status = 'active' AND end_date > CURRENT_TIMESTAMP")
                stats['active_subscriptions'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM subscriptions WHERE status = 'expired' OR end_date <= CURRENT_TIMESTAMP")
                stats['expired_subscriptions'] = cursor.fetchone()[0]
                
                # Подписки по типам
                cursor.execute("SELECT COUNT(*) FROM subscriptions WHERE plan_type = 'monthly' AND status = 'active'")
                stats['monthly_subs'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM subscriptions WHERE plan_type = 'quarterly' AND status = 'active'")
                stats['quarterly_subs'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM subscriptions WHERE plan_type = 'half_yearly' AND status = 'active'")
                stats['half_yearly_subs'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM subscriptions WHERE plan_type = 'yearly' AND status = 'active'")
                stats['yearly_subs'] = cursor.fetchone()[0]
                
                # Истекающие подписки
                cursor.execute("""
                    SELECT COUNT(*) FROM subscriptions 
                    WHERE status = 'active' 
                    AND date(end_date) = date('now')
                """)
                stats['expiring_today'] = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT COUNT(*) FROM subscriptions 
                    WHERE status = 'active' 
                    AND date(end_date) = date('now', '+1 day')
                """)
                stats['expiring_tomorrow'] = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT COUNT(*) FROM subscriptions 
                    WHERE status = 'active' 
                    AND end_date BETWEEN CURRENT_TIMESTAMP 
                    AND datetime(CURRENT_TIMESTAMP, '+7 days')
                """)
                stats['expiring_week'] = cursor.fetchone()[0]
                
                # Административные действия за последние 24 часа
                cursor.execute('''
                    SELECT COUNT(*) FROM admin_logs 
                    WHERE created_at > datetime('now', '-1 day')
                ''')
                stats['admin_actions_24h'] = cursor.fetchone()[0]
                
                # Общее количество административных действий
                cursor.execute('SELECT COUNT(*) FROM admin_logs')
                stats['total_admin_actions'] = cursor.fetchone()[0]
                
                # Системная информация
                stats['uptime'] = 'Неизвестно'  # Можно добавить позже
                stats['last_check'] = datetime.now().strftime('%d.%m.%Y %H:%M')
                
                return stats
                
        except Exception as e:
            logger.error(f"Ошибка получения системной статистики: {e}")
            return {}
    
    def get_payment_statistics(self) -> Dict[str, Any]:
        """Получить статистику платежей"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                stats = {}
                
                # Общая статистика платежей
                cursor.execute('SELECT COUNT(*) FROM payments')
                stats['total_payments'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM payments WHERE status = 'completed'")
                stats['completed_payments'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM payments WHERE status = 'pending'")
                stats['pending_payments'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM payments WHERE status = 'failed'")
                stats['failed_payments'] = cursor.fetchone()[0]
                
                # Доходы
                cursor.execute("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE status = 'completed'")
                stats['total_revenue'] = float(cursor.fetchone()[0])
                
                # Доход за месяц
                cursor.execute("""
                    SELECT COALESCE(SUM(amount), 0) FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-1 month')
                """)
                stats['monthly_revenue'] = float(cursor.fetchone()[0])
                
                # Доход за неделю
                cursor.execute("""
                    SELECT COALESCE(SUM(amount), 0) FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-7 days')
                """)
                stats['weekly_revenue'] = float(cursor.fetchone()[0])
                
                return stats
                
        except Exception as e:
            logger.error(f"Ошибка получения статистики платежей: {e}")
            return {}
    
    def get_revenue_report(self, period_days: int = 30) -> Dict[str, Any]:
        """Получить отчет по доходам за указанный период"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                report = {}
                
                # Общий доход за период
                cursor.execute("""
                    SELECT COALESCE(SUM(amount), 0) FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-{} days')
                """.format(period_days))
                report['total_revenue'] = float(cursor.fetchone()[0])
                
                # Доходы по способам оплаты
                cursor.execute("""
                    SELECT payment_method, COALESCE(SUM(amount), 0) as revenue
                    FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-{} days')
                    GROUP BY payment_method
                """.format(period_days))
                
                payment_methods = {}
                for row in cursor.fetchall():
                    payment_methods[row[0]] = float(row[1])
                report['revenue_by_method'] = payment_methods
                
                # Доходы по дням за весь период
                daily_revenue = []
                for i in range(period_days - 1, -1, -1):
                    cursor.execute("""
                        SELECT COALESCE(SUM(amount), 0) FROM payments 
                        WHERE status = 'completed' 
                        AND date(completed_at) = date('now', '-{} days')
                    """.format(i))
                    
                    date_str = (datetime.now() - timedelta(days=i)).strftime('%d.%m')
                    revenue = float(cursor.fetchone()[0])
                    daily_revenue.append({'date': date_str, 'revenue': revenue})
                
                report['daily_revenue'] = daily_revenue
                
                # Средний чек
                cursor.execute("""
                    SELECT AVG(amount) FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-{} days')
                """.format(period_days))
                avg_result = cursor.fetchone()[0]
                report['average_check'] = float(avg_result) if avg_result else 0.0
                
                # Общее количество транзакций за период
                cursor.execute("""
                    SELECT COUNT(*) FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-{} days')
                """.format(period_days))
                report['total_transactions'] = cursor.fetchone()[0]
                
                return report
                
        except Exception as e:
            logger.error(f"Ошибка получения отчета по доходам: {e}")
            return {}
    
    def get_subscription_report(self) -> Dict[str, Any]:
        """Получить отчет по активным подпискам"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                report = {}
                
                # Активные подписки по типам
                cursor.execute("""
                    SELECT plan_type, COUNT(*) as count
                    FROM subscriptions 
                    WHERE status = 'active' AND end_date > CURRENT_TIMESTAMP
                    GROUP BY plan_type
                """)
                
                active_by_type = {}
                for row in cursor.fetchall():
                    active_by_type[row[0]] = row[1]
                report['active_by_type'] = active_by_type
                
                # Подписки, истекающие в ближайшие дни
                expiring_periods = [1, 3, 7, 30]
                expiring_counts = {}
                
                for days in expiring_periods:
                    cursor.execute("""
                        SELECT COUNT(*) FROM subscriptions 
                        WHERE status = 'active' 
                        AND end_date BETWEEN CURRENT_TIMESTAMP 
                        AND datetime(CURRENT_TIMESTAMP, '+{} days')
                    """.format(days))
                    expiring_counts[f'{days}_days'] = cursor.fetchone()[0]
                
                report['expiring_subscriptions'] = expiring_counts
                
                # Новые подписки за последние 30 дней
                cursor.execute("""
                    SELECT COUNT(*) FROM subscriptions 
                    WHERE created_at >= datetime('now', '-30 days')
                """)
                report['new_subscriptions_30d'] = cursor.fetchone()[0]
                
                # Отмененные подписки за последние 30 дней
                cursor.execute("""
                    SELECT COUNT(*) FROM subscriptions 
                    WHERE status = 'cancelled' 
                    AND updated_at >= datetime('now', '-30 days')
                """)
                report['cancelled_subscriptions_30d'] = cursor.fetchone()[0]
                
                return report
                
        except Exception as e:
            logger.error(f"Ошибка получения отчета по подпискам: {e}")
            return {}
    
    def get_admin_activity_report(self, days: int = 30) -> Dict[str, Any]:
        """Получить отчет по активности администраторов"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                report = {}
                
                # Действия по администраторам
                cursor.execute("""
                    SELECT admin_telegram_id, COUNT(*) as action_count
                    FROM admin_logs 
                    WHERE created_at >= datetime('now', '-{} days')
                    GROUP BY admin_telegram_id
                    ORDER BY action_count DESC
                """.format(days))
                
                admin_actions = {}
                for row in cursor.fetchall():
                    admin_actions[row[0]] = row[1]
                report['actions_by_admin'] = admin_actions
                
                # Действия по типам
                cursor.execute("""
                    SELECT action, COUNT(*) as action_count
                    FROM admin_logs 
                    WHERE created_at >= datetime('now', '-{} days')
                    GROUP BY action
                    ORDER BY action_count DESC
                """.format(days))
                
                action_types = {}
                for row in cursor.fetchall():
                    action_types[row[0]] = row[1]
                report['actions_by_type'] = action_types
                
                # Активность по дням (последние 7 дней)
                daily_activity = []
                for i in range(6, -1, -1):
                    cursor.execute("""
                        SELECT COUNT(*) FROM admin_logs 
                        WHERE date(created_at) = date('now', '-{} days')
                    """.format(i))
                    
                    date_str = (datetime.now() - timedelta(days=i)).strftime('%d.%m')
                    count = cursor.fetchone()[0]
                    daily_activity.append({'date': date_str, 'actions': count})
                
                report['daily_activity'] = daily_activity
                
                return report
                
        except Exception as e:
            logger.error(f"Ошибка получения отчета по активности администраторов: {e}")
            return {}
    
    def get_all_active_subscriptions(self) -> List[Subscription]:
        """Получить все активные подписки"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' AND end_date > CURRENT_TIMESTAMP
                    ORDER BY end_date ASC
                ''')
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения активных подписок: {e}")
            return []
    
    def get_expired_subscriptions(self) -> List[Subscription]:
        """Получить истекшие подписки"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' AND end_date <= CURRENT_TIMESTAMP
                    ORDER BY end_date DESC
                ''')
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения истекших подписок: {e}")
            return []
    
    def get_subscriptions_expiring_in_days(self, days: int) -> List[Subscription]:
        """Получить подписки, истекающие через указанное количество дней"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND date(end_date) = date('now', '+{} days')
                    ORDER BY end_date ASC
                '''.format(days))
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения подписок, истекающих через {days} дней: {e}")
            return []    
    
# ==================== МЕТОДЫ ОЧИСТКИ ДАННЫХ ====================
    
    def get_expired_payments_older_than_days(self, days: int) -> List[Payment]:
        """Получить истекшие платежи старше указанного количества дней"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM payments 
                    WHERE status IN ('expired', 'failed') 
                    AND created_at < datetime('now', '-{} days')
                    ORDER BY created_at ASC
                '''.format(days))
                rows = cursor.fetchall()
                
                payments = []
                for row in rows:
                    payments.append(Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    ))
                
                return payments
                
        except Exception as e:
            logger.error(f"Ошибка получения истекших платежей старше {days} дней: {e}")
            return []
    
    def delete_payment(self, payment_id: int) -> bool:
        """Удалить платеж по ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM payments WHERE id = ?', (payment_id,))
                conn.commit()
                
                deleted = cursor.rowcount > 0
                if deleted:
                    logger.info(f"Платеж {payment_id} удален")
                
                return deleted
                
        except Exception as e:
            logger.error(f"Ошибка удаления платежа {payment_id}: {e}")
            return False
    
    def get_admin_logs_older_than_days(self, days: int) -> List[AdminLog]:
        """Получить логи администратора старше указанного количества дней"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM admin_logs 
                    WHERE created_at < datetime('now', '-{} days')
                    ORDER BY created_at ASC
                '''.format(days))
                rows = cursor.fetchall()
                
                logs = []
                for row in rows:
                    logs.append(AdminLog(
                        id=row['id'],
                        admin_telegram_id=row['admin_telegram_id'],
                        action=row['action'],
                        target_user_id=row['target_user_id'],
                        details=row['details'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
                    ))
                
                return logs
                
        except Exception as e:
            logger.error(f"Ошибка получения логов администратора старше {days} дней: {e}")
            return []
    
    def delete_admin_log(self, log_id: int) -> bool:
        """Удалить лог администратора по ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM admin_logs WHERE id = ?', (log_id,))
                conn.commit()
                
                deleted = cursor.rowcount > 0
                if deleted:
                    logger.info(f"Лог администратора {log_id} удален")
                
                return deleted
                
        except Exception as e:
            logger.error(f"Ошибка удаления лога администратора {log_id}: {e}")
            return False
    
    def get_recent_payments(self, limit: int = 10) -> List[Payment]:
        """Получить последние платежи"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM payments 
                    ORDER BY created_at DESC 
                    LIMIT ?
                ''', (limit,))
                rows = cursor.fetchall()
                
                payments = []
                for row in rows:
                    payments.append(Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    ))
                
                return payments
                
        except Exception as e:
            logger.error(f"Ошибка получения последних платежей: {e}")
            return []
    
    def get_all_payments(self, limit: Optional[int] = None, offset: int = 0) -> List[Payment]:
        """Получить все платежи с пагинацией"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                query = 'SELECT * FROM payments ORDER BY created_at DESC'
                params = []
                
                if limit:
                    query += ' LIMIT ? OFFSET ?'
                    params.extend([limit, offset])
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                payments = []
                for row in rows:
                    payments.append(Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    ))
                
                return payments
                
        except Exception as e:
            logger.error(f"Ошибка получения всех платежей: {e}")
            return []

    def get_payments_with_filters(self, status_filter: str = '', payment_method_filter: str = '',
                                date_from: str = '', date_to: str = '', search: str = '',
                                limit: int = 20, offset: int = 0) -> Tuple[List[Payment], int]:
        """Получить платежи с фильтрацией и поиском"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Базовый запрос с JOIN для получения информации о пользователях
                base_query = '''
                    SELECT p.*, u.username, u.first_name, u.last_name
                    FROM payments p
                    LEFT JOIN users u ON p.user_id = u.id
                '''
                
                where_conditions = []
                params = []
                
                # Фильтр по статусу
                if status_filter:
                    where_conditions.append('p.status = ?')
                    params.append(status_filter)
                
                # Фильтр по способу оплаты
                if payment_method_filter:
                    where_conditions.append('p.payment_method = ?')
                    params.append(payment_method_filter)
                
                # Фильтр по дате от
                if date_from:
                    where_conditions.append('DATE(p.created_at) >= ?')
                    params.append(date_from)
                
                # Фильтр по дате до
                if date_to:
                    where_conditions.append('DATE(p.created_at) <= ?')
                    params.append(date_to)
                
                # Поиск по ID платежа, invoice ID или пользователю
                if search:
                    where_conditions.append('''
                        (CAST(p.id AS TEXT) LIKE ? OR 
                         p.lava_invoice_id LIKE ? OR
                         u.username LIKE ? OR 
                         u.first_name LIKE ? OR 
                         u.last_name LIKE ? OR
                         CAST(u.telegram_id AS TEXT) LIKE ?)
                    ''')
                    search_param = f'%{search}%'
                    params.extend([search_param] * 6)
                
                # Добавляем условия WHERE
                if where_conditions:
                    base_query += ' WHERE ' + ' AND '.join(where_conditions)
                
                # Получаем общее количество записей
                count_query = base_query.replace('SELECT p.*, u.username, u.first_name, u.last_name', 'SELECT COUNT(*)')
                cursor.execute(count_query, params)
                total_count = cursor.fetchone()[0]
                
                # Добавляем сортировку и пагинацию
                base_query += ' ORDER BY p.created_at DESC LIMIT ? OFFSET ?'
                params.extend([limit, offset])
                
                cursor.execute(base_query, params)
                rows = cursor.fetchall()
                
                payments = []
                for row in rows:
                    payment = Payment(
                        id=row['id'],
                        user_id=row['user_id'],
                        subscription_id=row['subscription_id'],
                        lava_invoice_id=row['lava_invoice_id'],
                        amount=Decimal(str(row['amount'])),
                        currency=row['currency'],
                        payment_method=row['payment_method'],
                        status=row['status'],
                        payment_url=row['payment_url'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    )
                    
                    # Добавляем информацию о пользователе как атрибуты
                    payment.user_username = row['username']
                    payment.user_first_name = row['first_name']
                    payment.user_last_name = row['last_name']
                    
                    payments.append(payment)
                
                return payments, total_count
                
        except Exception as e:
            logger.error(f"Ошибка получения платежей с фильтрами: {e}")
            return [], 0

    def get_daily_revenue_chart(self, period_days: int = 30) -> List[Dict[str, Any]]:
        """Получить данные для графика доходов по дням"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                chart_data = []
                
                for i in range(period_days - 1, -1, -1):
                    date = datetime.now() - timedelta(days=i)
                    date_str = date.strftime('%Y-%m-%d')
                    
                    cursor.execute("""
                        SELECT COALESCE(SUM(amount), 0) as revenue,
                               COUNT(*) as count
                        FROM payments 
                        WHERE status = 'completed' 
                        AND DATE(completed_at) = ?
                    """, (date_str,))
                    
                    result = cursor.fetchone()
                    revenue = float(result['revenue']) if result['revenue'] else 0
                    count = result['count'] if result['count'] else 0
                    
                    chart_data.append({
                        'date': date.strftime('%d.%m'),
                        'full_date': date_str,
                        'revenue': revenue,
                        'count': count
                    })
                
                return chart_data
                
        except Exception as e:
            logger.error(f"Ошибка получения данных графика доходов: {e}")
            return []

    def get_monthly_revenue_comparison(self) -> Dict[str, Any]:
        """Получить сравнение доходов по месяцам"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Доходы за последние 12 месяцев
                cursor.execute("""
                    SELECT 
                        strftime('%Y-%m', completed_at) as month,
                        COALESCE(SUM(amount), 0) as revenue,
                        COUNT(*) as transactions
                    FROM payments 
                    WHERE status = 'completed' 
                    AND completed_at >= datetime('now', '-12 months')
                    GROUP BY strftime('%Y-%m', completed_at)
                    ORDER BY month
                """)
                
                monthly_data = []
                for row in cursor.fetchall():
                    month_str = datetime.strptime(row[0], '%Y-%m').strftime('%m.%Y')
                    monthly_data.append({
                        'month': month_str,
                        'revenue': float(row[1]),
                        'transactions': row[2]
                    })
                
                return {
                    'monthly_data': monthly_data,
                    'total_months': len(monthly_data)
                }
                
        except Exception as e:
            logger.error(f"Ошибка получения месячного сравнения доходов: {e}")
            return {'monthly_data': [], 'total_months': 0}

    def get_payment_method_statistics(self) -> Dict[str, Any]:
        """Получить детальную статистику по способам оплаты"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Статистика по способам оплаты за все время
                cursor.execute("""
                    SELECT 
                        payment_method,
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                        COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_revenue,
                        COALESCE(AVG(CASE WHEN status = 'completed' THEN amount END), 0) as avg_amount
                    FROM payments 
                    GROUP BY payment_method
                """)
                
                method_stats = {}
                for row in cursor.fetchall():
                    method_stats[row[0]] = {
                        'total_payments': row[1],
                        'completed_payments': row[2],
                        'failed_payments': row[3],
                        'pending_payments': row[4],
                        'total_revenue': float(row[5]),
                        'avg_amount': float(row[6]),
                        'success_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0
                    }
                
                return method_stats
                
        except Exception as e:
            logger.error(f"Ошибка получения статистики способов оплаты: {e}")
            return {}

    def get_top_revenue_days(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Получить дни с наибольшими доходами"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT 
                        DATE(completed_at) as date,
                        COALESCE(SUM(amount), 0) as revenue,
                        COUNT(*) as transactions
                    FROM payments 
                    WHERE status = 'completed'
                    GROUP BY DATE(completed_at)
                    ORDER BY revenue DESC
                    LIMIT ?
                """, (limit,))
                
                top_days = []
                for row in cursor.fetchall():
                    if row[0]:  # Проверяем что дата не None
                        date_obj = datetime.strptime(row[0], '%Y-%m-%d')
                        top_days.append({
                            'date': date_obj.strftime('%d.%m.%Y'),
                            'revenue': float(row[1]),
                            'transactions': row[2]
                        })
                
                return top_days
                
        except Exception as e:
            logger.error(f"Ошибка получения топ дней по доходам: {e}")
            return []
    
    def get_all_active_subscriptions_with_users(self) -> List[Dict[str, Any]]:
        """Получить все активные подписки с информацией о пользователях"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT s.*, u.telegram_id, u.username, u.first_name, u.last_name
                    FROM subscriptions s
                    JOIN users u ON s.user_id = u.id
                    WHERE s.status = 'active' AND s.end_date > CURRENT_TIMESTAMP
                    ORDER BY s.end_date ASC
                ''')
                rows = cursor.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscription = Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                    
                    user = User(
                        id=row['user_id'],
                        telegram_id=row['telegram_id'],
                        username=row['username'],
                        first_name=row['first_name'],
                        last_name=row['last_name'],
                        created_at=None,
                        updated_at=None
                    )
                    
                    subscriptions.append({
                        'subscription': subscription,
                        'user': user
                    })
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения активных подписок: {e}")
            return []



    def update_subscription_status(self, subscription_id: int, status: str) -> bool:
        """Обновить статус подписки"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE subscriptions 
                    SET status = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, subscription_id))
                
                conn.commit()
                updated = cursor.rowcount > 0
                
                if updated:
                    logger.info(f"Обновлен статус подписки {subscription_id} на {status}")
                
                return updated
                
        except Exception as e:
            logger.error(f"Ошибка обновления статуса подписки {subscription_id}: {e}")
            return False

    def update_payment_status(self, payment_id: int, status: str, subscription_id: Optional[int] = None) -> bool:
        """Обновить статус платежа"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                if status == 'completed':
                    cursor.execute('''
                        UPDATE payments 
                        SET status = ?, completed_at = CURRENT_TIMESTAMP, subscription_id = ?
                        WHERE id = ?
                    ''', (status, subscription_id, payment_id))
                else:
                    cursor.execute('''
                        UPDATE payments 
                        SET status = ?
                        WHERE id = ?
                    ''', (status, payment_id))
                
                conn.commit()
                updated = cursor.rowcount > 0
                
                if updated:
                    logger.info(f"Обновлен статус платежа {payment_id} на {status}")
                
                return updated
                
        except Exception as e:
            logger.error(f"Ошибка обновления статуса платежа {payment_id}: {e}")
            return False   
 # ==================== СИСТЕМНЫЕ НАСТРОЙКИ ====================
    
    def get_system_setting(self, category: str, key: str) -> Optional[SystemSetting]:
        """Получить системную настройку"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM system_settings 
                    WHERE category = ? AND key = ?
                ''', (category, key))
                row = cursor.fetchone()
                
                if row:
                    return SystemSetting(
                        id=row['id'],
                        category=row['category'],
                        key=row['key'],
                        value=row['value'],
                        description=row['description'],
                        data_type=row['data_type'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения настройки {category}.{key}: {e}")
            raise
    
    def set_system_setting(self, category: str, key: str, value: str, 
                          description: Optional[str] = None, data_type: str = 'string') -> bool:
        """Установить системную настройку"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO system_settings 
                    (category, key, value, description, data_type, updated_at)
                    VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (category, key, value, description, data_type))
                
                conn.commit()
                logger.info(f"Установлена настройка {category}.{key} = {value}")
                return True
                
        except Exception as e:
            logger.error(f"Ошибка установки настройки {category}.{key}: {e}")
            return False
    
    def get_settings_by_category(self, category: str) -> List[SystemSetting]:
        """Получить все настройки по категории"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM system_settings 
                    WHERE category = ?
                    ORDER BY key
                ''', (category,))
                rows = cursor.fetchall()
                
                settings = []
                for row in rows:
                    settings.append(SystemSetting(
                        id=row['id'],
                        category=row['category'],
                        key=row['key'],
                        value=row['value'],
                        description=row['description'],
                        data_type=row['data_type'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return settings
                
        except Exception as e:
            logger.error(f"Ошибка получения настроек категории {category}: {e}")
            return []
    
    def get_all_settings(self) -> Dict[str, List[SystemSetting]]:
        """Получить все системные настройки, сгруппированные по категориям"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM system_settings 
                    ORDER BY category, key
                ''')
                rows = cursor.fetchall()
                
                settings_by_category = {}
                for row in rows:
                    setting = SystemSetting(
                        id=row['id'],
                        category=row['category'],
                        key=row['key'],
                        value=row['value'],
                        description=row['description'],
                        data_type=row['data_type'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                    
                    if setting.category not in settings_by_category:
                        settings_by_category[setting.category] = []
                    settings_by_category[setting.category].append(setting)
                
                return settings_by_category
                
        except Exception as e:
            logger.error(f"Ошибка получения всех настроек: {e}")
            return {}
    
    # ==================== ТАРИФНЫЕ ПЛАНЫ ====================
    
    def create_subscription_plan(self, name: str, duration_months: int, price: Decimal,
                               currency: str = 'RUB', description: Optional[str] = None,
                               is_active: bool = True, sort_order: int = 0) -> Optional[SubscriptionPlan]:
        """Создать тарифный план"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO subscription_plans 
                    (name, duration_months, price, currency, description, is_active, sort_order)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (name, duration_months, float(price), currency, description, is_active, sort_order))
                
                plan_id = cursor.lastrowid
                conn.commit()
                
                logger.info(f"Создан тарифный план: {name} ({duration_months} мес., {price} {currency})")
                return self.get_subscription_plan_by_id(plan_id)
                
        except Exception as e:
            logger.error(f"Ошибка создания тарифного плана: {e}")
            return None
    
    def get_subscription_plan_by_id(self, plan_id: int) -> Optional[SubscriptionPlan]:
        """Получить тарифный план по ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM subscription_plans WHERE id = ?', (plan_id,))
                row = cursor.fetchone()
                
                if row:
                    return SubscriptionPlan(
                        id=row['id'],
                        name=row['name'],
                        duration_months=row['duration_months'],
                        price=Decimal(str(row['price'])),
                        currency=row['currency'],
                        description=row['description'],
                        is_active=bool(row['is_active']),
                        sort_order=row['sort_order'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения тарифного плана {plan_id}: {e}")
            return None
    
    def get_all_subscription_plans(self, active_only: bool = False) -> List[SubscriptionPlan]:
        """Получить все тарифные планы"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                query = 'SELECT * FROM subscription_plans'
                params = []
                
                if active_only:
                    query += ' WHERE is_active = 1'
                
                query += ' ORDER BY sort_order, duration_months'
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                plans = []
                for row in rows:
                    plans.append(SubscriptionPlan(
                        id=row['id'],
                        name=row['name'],
                        duration_months=row['duration_months'],
                        price=Decimal(str(row['price'])),
                        currency=row['currency'],
                        description=row['description'],
                        is_active=bool(row['is_active']),
                        sort_order=row['sort_order'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return plans
                
        except Exception as e:
            logger.error(f"Ошибка получения тарифных планов: {e}")
            return []
    
    def update_subscription_plan(self, plan_id: int, name: Optional[str] = None,
                               duration_months: Optional[int] = None, price: Optional[Decimal] = None,
                               currency: Optional[str] = None, description: Optional[str] = None,
                               is_active: Optional[bool] = None, sort_order: Optional[int] = None) -> bool:
        """Обновить тарифный план"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Строим динамический запрос обновления
                updates = []
                params = []
                
                if name is not None:
                    updates.append('name = ?')
                    params.append(name)
                if duration_months is not None:
                    updates.append('duration_months = ?')
                    params.append(duration_months)
                if price is not None:
                    updates.append('price = ?')
                    params.append(float(price))
                if currency is not None:
                    updates.append('currency = ?')
                    params.append(currency)
                if description is not None:
                    updates.append('description = ?')
                    params.append(description)
                if is_active is not None:
                    updates.append('is_active = ?')
                    params.append(is_active)
                if sort_order is not None:
                    updates.append('sort_order = ?')
                    params.append(sort_order)
                
                if not updates:
                    return False
                
                updates.append('updated_at = CURRENT_TIMESTAMP')
                params.append(plan_id)
                
                query = f"UPDATE subscription_plans SET {', '.join(updates)} WHERE id = ?"
                cursor.execute(query, params)
                
                conn.commit()
                updated = cursor.rowcount > 0
                
                if updated:
                    logger.info(f"Обновлен тарифный план {plan_id}")
                
                return updated
                
        except Exception as e:
            logger.error(f"Ошибка обновления тарифного плана {plan_id}: {e}")
            return False
    
    def delete_subscription_plan(self, plan_id: int) -> bool:
        """Удалить тарифный план"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM subscription_plans WHERE id = ?', (plan_id,))
                
                conn.commit()
                deleted = cursor.rowcount > 0
                
                if deleted:
                    logger.info(f"Удален тарифный план {plan_id}")
                
                return deleted
                
        except Exception as e:
            logger.error(f"Ошибка удаления тарифного плана {plan_id}: {e}")
            return False
    
    # ==================== ТЕКСТЫ БОТА ====================
    
    def get_bot_text(self, key: str) -> Optional[BotText]:
        """Получить текст бота по ключу"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM bot_texts WHERE key = ?', (key,))
                row = cursor.fetchone()
                
                if row:
                    return BotText(
                        id=row['id'],
                        key=row['key'],
                        text=row['text'],
                        description=row['description'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения текста бота {key}: {e}")
            return None
    
    def set_bot_text(self, key: str, text: str, description: Optional[str] = None) -> bool:
        """Установить текст бота"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO bot_texts 
                    (key, text, description, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ''', (key, text, description))
                
                conn.commit()
                logger.info(f"Установлен текст бота для ключа {key}")
                return True
                
        except Exception as e:
            logger.error(f"Ошибка установки текста бота {key}: {e}")
            return False
    
    def get_all_bot_texts(self) -> List[BotText]:
        """Получить все тексты бота"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM bot_texts ORDER BY key')
                rows = cursor.fetchall()
                
                texts = []
                for row in rows:
                    texts.append(BotText(
                        id=row['id'],
                        key=row['key'],
                        text=row['text'],
                        description=row['description'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return texts
                
        except Exception as e:
            logger.error(f"Ошибка получения текстов бота: {e}")
            return []
    
    def delete_bot_text(self, key: str) -> bool:
        """Удалить текст бота"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM bot_texts WHERE key = ?', (key,))
                
                conn.commit()
                deleted = cursor.rowcount > 0
                
                if deleted:
                    logger.info(f"Удален текст бота {key}")
                
                return deleted
                
        except Exception as e:
            logger.error(f"Ошибка удаления текста бота {key}: {e}")
            return False
    
    # ==================== РЕЗЕРВНОЕ КОПИРОВАНИЕ ====================
    
    def create_backup(self, backup_path: str) -> bool:
        """Создать резервную копию базы данных"""
        try:
            import shutil
            import os
            from datetime import datetime
            
            # Создаем директорию для бэкапов если не существует
            backup_dir = os.path.dirname(backup_path)
            if backup_dir and not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            # Копируем файл базы данных
            shutil.copy2(self.db_manager.db_path, backup_path)
            
            logger.info(f"Создана резервная копия базы данных: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка создания резервной копии: {e}")
            return False
    
    def restore_backup(self, backup_path: str) -> bool:
        """Восстановить базу данных из резервной копии"""
        try:
            import shutil
            import os
            
            if not os.path.exists(backup_path):
                logger.error(f"Файл резервной копии не найден: {backup_path}")
                return False
            
            # Создаем резервную копию текущей БД перед восстановлением
            current_backup = f"{self.db_manager.db_path}.backup_before_restore"
            shutil.copy2(self.db_manager.db_path, current_backup)
            
            # Восстанавливаем из резервной копии
            shutil.copy2(backup_path, self.db_manager.db_path)
            
            logger.info(f"База данных восстановлена из резервной копии: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка восстановления из резервной копии: {e}")
            return False
    
    def get_expiring_subscriptions(self, days_ahead: int = 7) -> List[Subscription]:
        """
        Получает подписки, которые истекают в ближайшие дни
        
        Args:
            days_ahead: Количество дней вперед для поиска истекающих подписок
            
        Returns:
            Список истекающих подписок
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND end_date <= datetime('now', '+{} days')
                    AND end_date > datetime('now')
                    ORDER BY end_date ASC
                '''.format(days_ahead))
                
                rows = cursor.fetchall()
                subscriptions = []
                
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения истекающих подписок: {e}")
            return []
    
    def get_subscriptions_expiring_in_days(self, days: int) -> List[Subscription]:
        """
        Получает подписки, которые истекают точно через указанное количество дней
        
        Args:
            days: Количество дней
            
        Returns:
            Список подписок
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND date(end_date) = date('now', '+{} days')
                    ORDER BY end_date ASC
                '''.format(days))
                
                rows = cursor.fetchall()
                subscriptions = []
                
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения подписок, истекающих через {days} дней: {e}")
            return []
    
    def get_expired_subscriptions(self) -> List[Subscription]:
        """
        Получает истекшие подписки
        
        Returns:
            Список истекших подписок
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND end_date <= datetime('now')
                    ORDER BY end_date DESC
                ''')
                
                rows = cursor.fetchall()
                subscriptions = []
                
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения истекших подписок: {e}")
            return []
    
    def get_all_active_subscriptions(self) -> List[Subscription]:
        """
        Получает все активные подписки
        
        Returns:
            Список активных подписок
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM subscriptions 
                    WHERE status = 'active' 
                    AND end_date > datetime('now')
                    ORDER BY end_date ASC
                ''')
                
                rows = cursor.fetchall()
                subscriptions = []
                
                for row in rows:
                    subscriptions.append(Subscription(
                        id=row['id'],
                        user_id=row['user_id'],
                        plan_type=row['plan_type'],
                        status=row['status'],
                        start_date=datetime.fromisoformat(row['start_date']),
                        end_date=datetime.fromisoformat(row['end_date']),
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    ))
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"Ошибка получения активных подписок: {e}")
            return []

    def initialize_default_settings(self):
        """Инициализация настроек по умолчанию"""
        try:
            # Настройки интеграций
            default_settings = [
                # API настройки
                ('api', 'lava_api_url', 'https://api.lava.top', 'URL API Lava.top', 'string'),
                ('api', 'webhook_timeout', '30', 'Таймаут webhook в секундах', 'integer'),
                ('api', 'max_retries', '3', 'Максимальное количество повторных попыток', 'integer'),
                
                # Настройки бота
                ('bot', 'welcome_message', 'Добро пожаловать! Выберите действие:', 'Приветственное сообщение', 'string'),
                ('bot', 'payment_timeout', '3600', 'Время жизни ссылки на оплату (секунды)', 'integer'),
                ('bot', 'notification_enabled', 'true', 'Включить уведомления', 'boolean'),
                
                # Настройки подписок
                ('subscription', 'trial_period_days', '0', 'Пробный период в днях', 'integer'),
                ('subscription', 'grace_period_hours', '24', 'Льготный период после истечения (часы)', 'integer'),
                ('subscription', 'auto_renewal', 'false', 'Автоматическое продление', 'boolean'),
                
                # Настройки системы
                ('system', 'backup_enabled', 'true', 'Включить автоматическое резервное копирование', 'boolean'),
                ('system', 'backup_interval_hours', '24', 'Интервал резервного копирования (часы)', 'integer'),
                ('system', 'log_retention_days', '30', 'Срок хранения логов (дни)', 'integer'),
                ('system', 'cleanup_enabled', 'true', 'Включить автоматическую очистку', 'boolean'),
            ]
            
            for category, key, value, description, data_type in default_settings:
                # Проверяем, существует ли уже настройка
                existing = self.get_system_setting(category, key)
                if not existing:
                    self.set_system_setting(category, key, value, description, data_type)
            
            # Инициализация тарифных планов по умолчанию
            existing_plans = self.get_all_subscription_plans()
            if not existing_plans:
                default_plans = [
                    ('1 месяц', 1, Decimal('299'), 'RUB', 'Базовый план на 1 месяц', True, 1),
                    ('3 месяца', 3, Decimal('799'), 'RUB', 'Популярный план на 3 месяца', True, 2),
                    ('6 месяцев', 6, Decimal('1499'), 'RUB', 'Выгодный план на 6 месяцев', True, 3),
                    ('12 месяцев', 12, Decimal('2799'), 'RUB', 'Максимальная выгода на год', True, 4),
                ]
                
                for name, duration, price, currency, description, is_active, sort_order in default_plans:
                    self.create_subscription_plan(name, duration, price, currency, description, is_active, sort_order)
            
            # Инициализация текстов бота по умолчанию
            existing_texts = self.get_all_bot_texts()
            if not existing_texts:
                default_texts = [
                    ('welcome', 'Добро пожаловать в наш бот! 🎉\n\nВыберите действие из меню ниже:', 'Приветственное сообщение'),
                    ('buy_subscription', '💳 Выберите тарифный план:', 'Сообщение при покупке подписки'),
                    ('subscription_status', '📊 Ваш статус подписки:', 'Сообщение статуса подписки'),
                    ('payment_success', '✅ Платеж успешно обработан!\n\nВаша подписка активирована.', 'Сообщение об успешном платеже'),
                    ('payment_failed', '❌ Платеж не прошел.\n\nПопробуйте еще раз или обратитесь в поддержку.', 'Сообщение о неудачном платеже'),
                    ('subscription_expired', '⏰ Ваша подписка истекла.\n\nДля продления выберите тарифный план.', 'Сообщение об истечении подписки'),
                    ('channel_info', '📢 О нашем канале:\n\nЗдесь вы найдете эксклюзивный контент и полезную информацию.', 'Информация о канале'),
                    ('support_info', '🆘 Поддержка:\n\nЕсли у вас есть вопросы, обращайтесь к администратору.', 'Информация о поддержке'),
                ]
                
                for key, text, description in default_texts:
                    self.set_bot_text(key, text, description)
            
            logger.info("Настройки по умолчанию инициализированы")
            
        except Exception as e:
            logger.error(f"Ошибка инициализации настроек по умолчанию: {e}")

# Глобальные функции для удобства
def init_database():
    """Глобальная функция для инициализации базы данных"""
    db_service = DatabaseService()
    return db_service.init_database()

def migrate_database():
    """Глобальная функция для миграции базы данных"""
    db_service = DatabaseService()
    return db_service.migrate_database()

def close_database_connections():
    """Глобальная функция для закрытия соединений с БД"""
    # В SQLite соединения закрываются автоматически
    pass