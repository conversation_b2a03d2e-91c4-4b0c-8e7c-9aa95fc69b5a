#!/bin/bash

# Server connection details
SERVER_IP="**************"
SERVER_USER="root"

# Create temporary .env file with updated content
cat > temp_server_env << 'EOF'
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************

# Lava.top API Configuration
LAVA_API_KEY=WnJ549MVZPHMtNPuUdtLaxS9nWhNVh3e4oazrhFVmUDB2qJaql36ECIeOX4ixD6q
LAVA_SECRET_KEY=WnJ549MVZPHMtNPuUdtLaxS9nWhNVh3e4oazrhFVmUDB2qJaql36ECIeOX4ixD6q
LAVA_BASE_URL=https://gate.lava.top

# Webhook Configuration
WEBHOOK_URL=http://**************/webhook

# Channel Configuration
CHANNEL_ID=-1001002786126628

# Admin Configuration
ADMIN_USER_IDS=389794370
ADMIN_PASSWORD=SecureAdmin2024!

# Security
SECRET_KEY=your-very-secure-secret-key-change-this-in-production-2024

# Notifications
NOTIFICATION_CHAT_ID=

# Database
DATABASE_URL=sqlite:///payments.db

# Environment
FLASK_ENV=production
FLASK_DEBUG=False

# Additional Settings
TZ=Europe/Moscow
LANGUAGE=ru
CURRENCY=RUB

# Subscription Plans
PLAN_1_MONTHS=1
PLAN_1_PRICE=299
PLAN_3_MONTHS=3
PLAN_3_PRICE=799
PLAN_6_MONTHS=6
PLAN_6_PRICE=1499
PLAN_12_MONTHS=12
PLAN_12_PRICE=2799
EOF

echo "Uploading .env file to server $SERVER_IP..."

# Upload the file to server
scp temp_server_env $SERVER_USER@$SERVER_IP:/root/.env

if [ $? -eq 0 ]; then
    echo "Successfully uploaded .env file to server"
    
    # Connect to server and restart services
    echo "Restarting services on server..."
    ssh $SERVER_USER@$SERVER_IP "cd /root && systemctl restart telegram-bot && systemctl restart nginx && echo 'Services restarted successfully'"
    
else
    echo "Failed to upload .env file"
fi

# Clean up
rm temp_server_env

echo "Update process completed."