{% extends "base.html" %}

{% block title %}Пользователь {{ user.first_name or user.username or user.telegram_id }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="pt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ safe_url_for('admin.dashboard') }}">Главная</a></li>
        <li class="breadcrumb-item"><a href="{{ safe_url_for('admin.users') }}">Пользователи</a></li>
        <li class="breadcrumb-item active">{{ user.first_name or user.username or user.telegram_id }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-person-circle"></i> 
        {{ user.first_name or user.username or 'Пользователь' }}
        {% if user.username %}
            <small class="text-muted">@{{ user.username }}</small>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if active_subscription %}
            <button class="btn btn-warning" onclick="revokeSubscription({{ user.id }}, '{{ user.first_name or 'Пользователь' }}')">
                <i class="bi bi-x-circle"></i> Отозвать подписку
            </button>
            <button class="btn btn-success" onclick="extendSubscription({{ user.id }}, '{{ user.first_name or 'Пользователь' }}')">
                <i class="bi bi-plus-circle"></i> Продлить подписку
            </button>
            {% else %}
            <button class="btn btn-success" onclick="grantSubscription({{ user.id }}, '{{ user.first_name or 'Пользователь' }}')">
                <i class="bi bi-plus-circle"></i> Выдать подписку
            </button>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <!-- Информация о пользователе -->
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person"></i> Информация о пользователе</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td>{{ user.id }}</td>
                    </tr>
                    <tr>
                        <td><strong>Telegram ID:</strong></td>
                        <td><code>{{ user.telegram_id }}</code></td>
                    </tr>
                    <tr>
                        <td><strong>Имя:</strong></td>
                        <td>{{ user.first_name or '-' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Фамилия:</strong></td>
                        <td>{{ user.last_name or '-' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Username:</strong></td>
                        <td>
                            {% if user.username %}
                                @{{ user.username }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Регистрация:</strong></td>
                        <td>{{ user.created_at.strftime('%d.%m.%Y %H:%M') if user.created_at else '-' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Обновление:</strong></td>
                        <td>{{ user.updated_at.strftime('%d.%m.%Y %H:%M') if user.updated_at else '-' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Текущая подписка -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-star"></i> Текущая подписка</h5>
            </div>
            <div class="card-body">
                {% if active_subscription %}
                <div class="alert alert-success">
                    <h6><i class="bi bi-check-circle"></i> Активная подписка</h6>
                    <p class="mb-1"><strong>Тип:</strong> {{ active_subscription.plan_type }}</p>
                    <p class="mb-1"><strong>Начало:</strong> {{ active_subscription.start_date.strftime('%d.%m.%Y') }}</p>
                    <p class="mb-0"><strong>Окончание:</strong> {{ active_subscription.end_date.strftime('%d.%m.%Y') }}</p>
                </div>
                {% else %}
                <div class="alert alert-secondary">
                    <h6><i class="bi bi-x-circle"></i> Нет активной подписки</h6>
                    <p class="mb-0">У пользователя нет активной подписки</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- История подписок и платежей -->
    <div class="col-md-8">
        <!-- История подписок -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-clock-history"></i> История подписок</h5>
            </div>
            <div class="card-body">
                {% if subscriptions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Тип</th>
                                <th>Статус</th>
                                <th>Начало</th>
                                <th>Окончание</th>
                                <th>Действия</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for subscription in subscriptions %}
                            <tr>
                                <td>{{ subscription.id }}</td>
                                <td>{{ subscription.plan_type }}</td>
                                <td>
                                    {% if subscription.status == 'active' %}
                                        <span class="badge bg-success">Активна</span>
                                    {% elif subscription.status == 'expired' %}
                                        <span class="badge bg-danger">Истекла</span>
                                    {% elif subscription.status == 'cancelled' %}
                                        <span class="badge bg-warning">Отменена</span>
                                    {% endif %}
                                </td>
                                <td>{{ subscription.start_date.strftime('%d.%m.%Y') }}</td>
                                <td>{{ subscription.end_date.strftime('%d.%m.%Y') }}</td>
                                <td>
                                    {% if subscription.status == 'active' %}
                                    <button class="btn btn-sm btn-outline-warning" 
                                            onclick="cancelSubscription({{ subscription.id }})">
                                        <i class="bi bi-x"></i> Отменить
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="bi bi-clock text-muted" style="font-size: 2rem;"></i>
                    <p class="text-muted mt-2">Нет истории подписок</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- История платежей -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-credit-card"></i> История платежей</h5>
            </div>
            <div class="card-body">
                {% if payments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Сумма</th>
                                <th>Способ</th>
                                <th>Статус</th>
                                <th>Дата создания</th>
                                <th>Дата оплаты</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.id }}</td>
                                <td>{{ payment.amount }} {{ payment.currency }}</td>
                                <td>
                                    {% if payment.payment_method == 'card_ru' %}
                                        <i class="bi bi-credit-card"></i> Карта РФ
                                    {% elif payment.payment_method == 'card_foreign' %}
                                        <i class="bi bi-credit-card-2-front"></i> Карта иностр.
                                    {% elif payment.payment_method == 'crypto' %}
                                        <i class="bi bi-currency-bitcoin"></i> Криптовалюта
                                    {% else %}
                                        {{ payment.payment_method }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.status == 'completed' %}
                                        <span class="badge bg-success">Оплачен</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning">Ожидает</span>
                                    {% elif payment.status == 'failed' %}
                                        <span class="badge bg-danger">Ошибка</span>
                                    {% elif payment.status == 'expired' %}
                                        <span class="badge bg-secondary">Истек</span>
                                    {% endif %}
                                </td>
                                <td>{{ payment.created_at.strftime('%d.%m.%Y %H:%M') if payment.created_at else '-' }}</td>
                                <td>{{ payment.completed_at.strftime('%d.%m.%Y %H:%M') if payment.completed_at else '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="bi bi-credit-card text-muted" style="font-size: 2rem;"></i>
                    <p class="text-muted mt-2">Нет истории платежей</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function grantSubscription(userId, userName) {
    const months = prompt(`Выдать подписку пользователю "${userName}".\nВведите количество месяцев (1-12):`, '1');
    
    if (months === null) return; // Отмена
    
    const monthsNum = parseInt(months);
    if (isNaN(monthsNum) || monthsNum < 1 || monthsNum > 12) {
        alert('Пожалуйста, введите число от 1 до 12');
        return;
    }
    
    if (confirm(`Выдать подписку на ${monthsNum} мес. пользователю "${userName}"?`)) {
        fetch('/admin/api/grant-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                months: monthsNum
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Подписка выдана успешно!');
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}

function extendSubscription(userId, userName) {
    const months = prompt(`Продлить подписку пользователю "${userName}".\nВведите количество месяцев (1-12):`, '1');
    
    if (months === null) return; // Отмена
    
    const monthsNum = parseInt(months);
    if (isNaN(monthsNum) || monthsNum < 1 || monthsNum > 12) {
        alert('Пожалуйста, введите число от 1 до 12');
        return;
    }
    
    if (confirm(`Продлить подписку на ${monthsNum} мес. пользователю "${userName}"?`)) {
        fetch('/admin/api/grant-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                months: monthsNum
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Подписка продлена успешно!');
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}

function revokeSubscription(userId, userName) {
    if (confirm(`Отозвать подписку у пользователя "${userName}"?`)) {
        fetch('/admin/api/revoke-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Подписка отозвана успешно!');
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}

function cancelSubscription(subscriptionId) {
    if (confirm('Отменить эту подписку?')) {
        fetch('/admin/api/cancel-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                subscription_id: subscriptionId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Подписка отменена успешно!');
                location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error);
        });
    }
}
</script>
{% endblock %}