@echo off
echo ========================================
echo   ТЕСТ ПОДКЛЮЧЕНИЯ К СЕРВЕРУ
echo ========================================
echo.

echo Тестируем подключение к серверу...
ping -n 4 **************

echo.
echo Тестируем SSH подключение...
echo Пароль: dkomqgTaijxro7in^bxd
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ubuntu@************** "echo 'Подключение успешно'; exit"

echo.
echo Если подключение не работает, попробуйте:
echo 1. Проверить интернет соединение
echo 2. Убедиться что сервер запущен
echo 3. Проверить правильность пароля
echo.
pause