#!/usr/bin/env python3
"""
Интеграционный тест для функциональности управления платежами
"""

import sys
import os
import sqlite3
import tempfile
from decimal import Decimal
from datetime import datetime

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.database import DatabaseManager, DatabaseService

def test_payment_management_integration():
    """Интеграционный тест управления платежами"""
    print("🧪 Запуск интеграционного теста управления платежами...")
    
    # Создаем временную базу данных
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as temp_db:
        db_path = temp_db.name
    
    try:
        # Инициализируем базу данных
        db_manager = DatabaseManager(db_path)
        db_manager.init_database()
        
        db_service = DatabaseService(db_manager)
        
        print("✅ База данных инициализирована")
        
        # Создаем тестового пользователя
        user = db_service.create_user(
            telegram_id=123456789,
            username='testuser',
            first_name='Test',
            last_name='User'
        )
        
        print(f"✅ Создан пользователь: {user.id}")
        
        # Создаем тестовый платеж
        payment = db_service.create_payment(
            user_id=user.id,
            lava_invoice_id='test_invoice_123',
            amount=Decimal('1000.00'),
            payment_method='card_ru',
            payment_url='https://test.lava.top/pay/123'
        )
        
        print(f"✅ Создан платеж: {payment.id}")
        
        # Тестируем получение платежа по ID
        retrieved_payment = db_service.get_payment_by_id(payment.id)
        assert retrieved_payment is not None
        assert retrieved_payment.id == payment.id
        assert retrieved_payment.status == 'pending'
        
        print("✅ Получение платежа по ID работает")
        
        # Тестируем обновление статуса платежа
        result = db_service.update_payment_status(payment.id, 'completed')
        assert result is True
        
        # Проверяем, что статус обновился
        updated_payment = db_service.get_payment_by_id(payment.id)
        assert updated_payment.status == 'completed'
        
        print("✅ Обновление статуса платежа работает")
        
        # Тестируем получение платежей с фильтрами
        payments, total = db_service.get_payments_with_filters(
            status_filter='completed',
            limit=10,
            offset=0
        )
        
        assert len(payments) == 1
        assert total == 1
        assert payments[0].status == 'completed'
        
        print("✅ Получение платежей с фильтрами работает")
        
        # Тестируем статистику платежей
        payment_stats = db_service.get_payment_statistics()
        assert 'by_status' in payment_stats
        assert 'completed' in payment_stats['by_status']
        assert payment_stats['by_status']['completed']['count'] == 1
        
        print("✅ Статистика платежей работает")
        
        # Тестируем отчет по доходам
        revenue_report = db_service.get_revenue_report(30)
        assert 'total_revenue' in revenue_report
        assert revenue_report['total_revenue'] >= Decimal('1000.00')
        
        print("✅ Отчет по доходам работает")
        
        # Тестируем данные для графика
        chart_data = db_service.get_daily_revenue_chart(7)
        assert isinstance(chart_data, list)
        assert len(chart_data) == 7  # 7 дней
        
        print("✅ Данные для графика доходов работают")
        
        print("🎉 Все интеграционные тесты прошли успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в интеграционном тесте: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Удаляем временную базу данных
        try:
            os.unlink(db_path)
        except:
            pass

if __name__ == '__main__':
    success = test_payment_management_integration()
    sys.exit(0 if success else 1)