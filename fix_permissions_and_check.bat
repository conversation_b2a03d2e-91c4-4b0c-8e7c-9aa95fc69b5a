@echo off
set "PASSWORD=dkomqgTaijxro7in^^bxd"
echo Fixing permissions and checking current state...
echo.
echo Checking if telegrambot user exists:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "id telegrambot"
echo.
echo Creating telegrambot user if not exists:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S useradd -m -s /bin/bash telegrambot 2>/dev/null || echo 'User already exists'"
echo.
echo Creating app directory:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S mkdir -p /home/<USER>/app"
echo.
echo Setting proper ownership:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S chown -R telegrambot:telegrambot /home/<USER>/"
echo.
echo Checking directory structure:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S ls -la /home/<USER>/"
echo.
echo Checking app directory:
plink -ssh -pw %PASSWORD% -batch ubuntu@************** "echo '%PASSWORD%' | sudo -S ls -la /home/<USER>/app/"
echo Done.