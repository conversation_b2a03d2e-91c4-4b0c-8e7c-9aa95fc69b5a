#!/usr/bin/env python3
"""
Тест для функциональности управления платежами в админ-панели
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
from decimal import Decimal
from datetime import datetime

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.database import DatabaseService, Payment, User
from app.admin import admin_bp

class TestPaymentManagement(unittest.TestCase):
    """Тесты для управления платежами"""
    
    def setUp(self):
        """Настройка тестов"""
        self.db_service = Mock(spec=DatabaseService)
        
        # Создаем тестовые данные
        self.test_payment = Payment(
            id=1,
            user_id=1,
            subscription_id=None,
            lava_invoice_id="test_invoice_123",
            amount=Decimal('1000.00'),
            currency='RUB',
            payment_method='card_ru',
            status='pending',
            payment_url='https://test.lava.top/pay/123',
            created_at=datetime.now(),
            completed_at=None,
            expires_at=None
        )
        
        self.test_user = User(
            id=1,
            telegram_id=123456789,
            username='testuser',
            first_name='Test',
            last_name='User',
            created_at=datetime.now(),
            updated_at=None
        )
    
    def test_get_payment_by_id(self):
        """Тест получения платежа по ID"""
        # Настраиваем mock
        self.db_service.get_payment_by_id.return_value = self.test_payment
        
        # Вызываем метод
        result = self.db_service.get_payment_by_id(1)
        
        # Проверяем результат
        self.assertIsNotNone(result)
        self.assertEqual(result.id, 1)
        self.assertEqual(result.status, 'pending')
        self.assertEqual(result.amount, Decimal('1000.00'))
        
        # Проверяем, что метод был вызван с правильными параметрами
        self.db_service.get_payment_by_id.assert_called_once_with(1)
    
    def test_update_payment_status(self):
        """Тест обновления статуса платежа"""
        # Настраиваем mock
        self.db_service.update_payment_status.return_value = True
        
        # Вызываем метод
        result = self.db_service.update_payment_status(1, 'completed')
        
        # Проверяем результат
        self.assertTrue(result)
        
        # Проверяем, что метод был вызван с правильными параметрами
        self.db_service.update_payment_status.assert_called_once_with(1, 'completed')
    
    def test_get_payments_with_filters(self):
        """Тест получения платежей с фильтрами"""
        # Настраиваем mock
        test_payments = [self.test_payment]
        self.db_service.get_payments_with_filters.return_value = (test_payments, 1)
        
        # Вызываем метод
        payments, total = self.db_service.get_payments_with_filters(
            status_filter='pending',
            limit=20,
            offset=0
        )
        
        # Проверяем результат
        self.assertEqual(len(payments), 1)
        self.assertEqual(total, 1)
        self.assertEqual(payments[0].status, 'pending')
        
        # Проверяем, что метод был вызван с правильными параметрами
        self.db_service.get_payments_with_filters.assert_called_once_with(
            status_filter='pending',
            limit=20,
            offset=0
        )
    
    def test_payment_status_validation(self):
        """Тест валидации статусов платежей"""
        valid_statuses = ['completed', 'failed', 'expired']
        invalid_statuses = ['invalid', 'test', '']
        
        for status in valid_statuses:
            self.assertIn(status, ['completed', 'failed', 'expired'])
        
        for status in invalid_statuses:
            self.assertNotIn(status, ['completed', 'failed', 'expired'])
    
    def test_revenue_report_data(self):
        """Тест данных финансового отчета"""
        # Настраиваем mock для отчета по доходам
        test_revenue_report = {
            'total_revenue': Decimal('5000.00'),
            'average_check': Decimal('1000.00'),
            'total_transactions': 5,
            'revenue_by_method': {
                'card_ru': Decimal('3000.00'),
                'card_foreign': Decimal('1500.00'),
                'crypto': Decimal('500.00')
            }
        }
        
        self.db_service.get_revenue_report.return_value = test_revenue_report
        
        # Вызываем метод
        result = self.db_service.get_revenue_report(30)
        
        # Проверяем результат
        self.assertIsNotNone(result)
        self.assertEqual(result['total_revenue'], Decimal('5000.00'))
        self.assertEqual(result['total_transactions'], 5)
        self.assertIn('revenue_by_method', result)
        
        # Проверяем, что метод был вызван с правильными параметрами
        self.db_service.get_revenue_report.assert_called_once_with(30)
    
    def test_daily_revenue_chart_data(self):
        """Тест данных для графика доходов по дням"""
        # Настраиваем mock для данных графика
        test_chart_data = [
            {'date': '01.01', 'full_date': '2025-01-01', 'revenue': 1000.0, 'count': 2},
            {'date': '02.01', 'full_date': '2025-01-02', 'revenue': 1500.0, 'count': 3},
            {'date': '03.01', 'full_date': '2025-01-03', 'revenue': 800.0, 'count': 1}
        ]
        
        self.db_service.get_daily_revenue_chart.return_value = test_chart_data
        
        # Вызываем метод
        result = self.db_service.get_daily_revenue_chart(30)
        
        # Проверяем результат
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]['revenue'], 1000.0)
        self.assertEqual(result[1]['count'], 3)
        
        # Проверяем, что метод был вызван с правильными параметрами
        self.db_service.get_daily_revenue_chart.assert_called_once_with(30)

def run_tests():
    """Запуск тестов"""
    print("🧪 Запуск тестов управления платежами...")
    
    # Создаем test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPaymentManagement)
    
    # Запускаем тесты
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Выводим результаты
    if result.wasSuccessful():
        print("✅ Все тесты прошли успешно!")
        return True
    else:
        print("❌ Некоторые тесты не прошли:")
        for failure in result.failures:
            print(f"  - {failure[0]}: {failure[1]}")
        for error in result.errors:
            print(f"  - {error[0]}: {error[1]}")
        return False

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)