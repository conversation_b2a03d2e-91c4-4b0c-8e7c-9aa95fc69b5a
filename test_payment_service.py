#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы PaymentService с Lava.top API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.payment_service import PaymentService
import json

def test_payment_service():
    """Тестирует PaymentService"""
    
    print("=== Тест PaymentService ===")
    
    # Создаем экземпляр сервиса
    payment_service = PaymentService()
    
    # Тестовые данные
    user_id = 123456789
    plan_months = 1
    payment_method = 'ru_card'
    
    print(f"Тестируем создание счета для пользователя {user_id}")
    print(f"План: {plan_months} месяц(ев)")
    print(f"Способ оплаты: {payment_method}")
    print()
    
    # Создаем счет
    result = payment_service.create_invoice(user_id, plan_months, payment_method)
    
    print("Результат создания счета:")
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    
    if result.get('success'):
        print("\n✅ Счет успешно создан!")
        print(f"🔗 Ссылка для оплаты: {result.get('payment_url')}")
        print(f"💰 Сумма: {result.get('amount')} {result.get('currency')}")
        print(f"📋 Order ID: {result.get('order_id')}")
        
        # Тестируем проверку статуса
        print(f"\n🔄 Проверяем статус платежа...")
        status_result = payment_service.get_payment_status(result.get('invoice_id'))
        print("Статус платежа:")
        print(json.dumps(status_result, indent=2, ensure_ascii=False, default=str))
        
    else:
        print(f"\n❌ Ошибка создания счета: {result.get('error')}")

def test_available_methods():
    """Тестирует получение доступных способов оплаты и планов"""
    
    print("\n=== Доступные способы оплаты ===")
    methods = PaymentService.get_available_payment_methods()
    for key, method in methods.items():
        print(f"• {key}: {method['name']} - {method['description']}")
    
    print("\n=== Доступные тарифные планы ===")
    plans = PaymentService.get_subscription_plans()
    for months, plan in plans.items():
        print(f"• {months} мес.: {plan['price']} руб. - {plan['name']}")

if __name__ == '__main__':
    test_available_methods()
    test_payment_service()